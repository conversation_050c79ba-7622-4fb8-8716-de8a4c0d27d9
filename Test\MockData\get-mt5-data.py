import MetaTrader5 as mt5
import pandas as pd
import numpy as np
import os
from dotenv import load_dotenv
from datetime import datetime, timedelta
# Load .env
load_dotenv() 

prefix = "DEMO1"
login = int(os.getenv(f'{prefix}_LOGIN'))
pwd = os.getenv(f'{prefix}_PWD')
server = os.getenv(f'{prefix}_SERVER')
path = "C:\\Program Files\\MetaTrader 5\\terminal64.exe"
symbol_posfix = os.getenv(f'{prefix}_SYMBOL_POSTFIX')

if not mt5.initialize(path=path):
    print("❌ ไม่สามารถเชื่อมต่อ MT5:", mt5.last_error())
    quit()
else:
    if mt5.login(login,pwd,server):
        print("logged in succesffully")
    else: 
        print("login failed, error code: {}".format(mt5.last_error()))

print(f"Logged in to {server} as {login}")

def get_mt5_data(symbol, start_time, periods, timeframe):
    # ดึงข้อมูลแท่งเทียน
    if not start_time:
    #     rates = mt5.copy_rates_from_pos(symbol, timeframe, 0, periods)
        now = datetime.now()
        start_time = now #- timedelta(hours=0)  # หรือมากกว่านั้นตาม timeframe
    rates = mt5.copy_rates_from(symbol, timeframe, start_time, periods)
    # แปลงเป็น DataFrame
    df = pd.DataFrame(rates)
    df['time'] = pd.to_datetime(df['time'], unit='s')
    df = df[['time', 'open', 'high', 'low', 'close']]
    return df
 
symbol = "XAUUSD" + symbol_posfix
# start_time = datetime(2024, 1, 1, 9, 0)  # เวลาเริ่มต้น
start_time = False
# สร้างข้อมูล 5 นาที 100 แท่ง และ 30 นาที 100 แท่ง 
n_bar = 1000
df_M5 = get_mt5_data(symbol, start_time, n_bar, mt5.TIMEFRAME_M5)
df_M15 = get_mt5_data(symbol, start_time, n_bar, mt5.TIMEFRAME_M15)
df_M30 = get_mt5_data(symbol, start_time, n_bar, mt5.TIMEFRAME_M30)
df_H1 = get_mt5_data(symbol, start_time, n_bar, mt5.TIMEFRAME_H1)
df_H4 = get_mt5_data(symbol, start_time, n_bar, mt5.TIMEFRAME_H4)
print(df_H4)
# 📁 ดึง path ปัจจุบันของไฟล์ python
current_dir = os.path.dirname(os.path.abspath(__file__))
print(current_dir)
# 🟡 โหลด CSV จาก path เดียวกับไฟล์ python
df_M5.to_csv(os.path.join(current_dir, "M5.csv"), index=False)
df_M15.to_csv(os.path.join(current_dir, "M15.csv"), index=False)
df_M30.to_csv(os.path.join(current_dir, "M30.csv"), index=False)
df_H1.to_csv(os.path.join(current_dir, "H1.csv"), index=False)
df_H4.to_csv(os.path.join(current_dir, "H4.csv"), index=False)
# print(df_5m)
print("==================")
print(" GET DATA SUCCESS ")
print("==================")
# ปิดการเชื่อมต่อ MT5
mt5.shutdown()