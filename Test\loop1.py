import sys
import threading
import time

print("Python version:", sys.version)
print("Threading available:", hasattr(threading, 'Thread'))

def run_loop():
    while True:
        print("Running loop...")
        time.sleep(1)

t = threading.Thread(target=run_loop)
t.start()

# Python version: 3.10.0rc2 (tags/v3.10.0rc2:839d789, Sep  7 2021, 18:51:45) [MSC v.1929 64 bit (AMD64)]
# Threading available: True