2025-12-11 10:44:24,444 [INFO] Serving on http://0.0.0.0:5050
2025-12-11 10:44:24,946 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-12-11 10:44:25,483 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-12-11 10:44:26,556 [INFO] \U0001f7e2 Orders processing loop started
2025-12-11 10:44:26,557 [INFO] \U0001f7e2 Orders processing loop started
2025-12-11 10:44:27,794 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 10:44:57,813 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 10:45:27,817 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 10:45:57,834 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 10:46:27,838 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 10:46:57,856 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 10:47:27,871 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 10:47:57,889 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 10:48:27,894 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 10:48:57,901 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 10:49:27,918 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 10:49:57,931 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 10:50:27,935 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 10:50:57,954 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 10:51:27,957 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 10:51:57,964 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 10:52:27,932 [INFO] \u2705 Webhook Instant: s:XAUUSD a:Buy Now c:None :: {'a': 'Buy Now', 's': 'XAUUSD', 'test': True, 'message': 'Test: ZD Path with x-current-path header', '_meta': {'source': 'web_app', 'group': 'g_instant', 'path': '/zd', 'webhookHost': 'http://localhost:5050', 'timestamp': '2025-12-11T03:52:27.911Z', 'accessToken': 'provided'}}
2025-12-11 10:52:27,936 [ERROR] Exception on /webhook_instant [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\Users\<USER>\OneDrive\Documents\python\App\tab_webhook.py", line 359, in webhook_instant
    tp = (entry + (point*ptp)) if not vtp else vtp
TypeError: unsupported operand type(s) for *: 'float' and 'NoneType'
2025-12-11 10:52:27,968 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 10:52:28,263 [INFO] \u2705 Webhook Instant: s:XAUUSD a:Buy Now c:None :: {'a': 'Buy Now', 's': 'XAUUSD', 'test': True, 'message': 'Test: No path header (should use referer)', '_meta': {'source': 'web_app', 'group': 'g_instant', 'path': '/zd', 'webhookHost': 'http://localhost:5050', 'timestamp': '2025-12-11T03:52:28.258Z', 'accessToken': 'provided'}}
2025-12-11 10:52:28,270 [ERROR] Exception on /webhook_instant [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\Users\<USER>\OneDrive\Documents\python\App\tab_webhook.py", line 359, in webhook_instant
    tp = (entry + (point*ptp)) if not vtp else vtp
TypeError: unsupported operand type(s) for *: 'float' and 'NoneType'
2025-12-11 10:52:58,011 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 10:53:28,017 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 10:53:58,030 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 10:54:28,047 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 10:54:58,057 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 10:55:28,065 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 10:55:58,072 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 10:56:28,079 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 10:56:58,082 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 10:57:28,097 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 10:57:58,113 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 10:58:28,121 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 10:58:58,136 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 10:59:28,150 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 10:59:58,168 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:00:28,179 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:00:58,190 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:01:28,203 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:01:58,221 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:02:28,228 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:02:58,245 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:03:28,258 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:03:58,270 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:04:28,282 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:04:58,298 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:05:28,314 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:05:58,328 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:06:28,332 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:06:58,345 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:07:28,361 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:07:58,372 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:08:28,379 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:08:58,392 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:09:28,456 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:09:58,467 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:10:28,485 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:10:58,497 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:11:28,506 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:11:58,522 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:12:28,529 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:12:58,542 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:13:28,557 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:13:58,574 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:14:28,590 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:14:58,609 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:15:28,618 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:15:58,631 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:16:28,639 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:16:58,658 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:17:28,663 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:17:58,674 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:18:28,679 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:18:58,685 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:19:28,696 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:19:58,710 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:20:28,721 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:20:53,574 [INFO] \u2705 Webhook Instant: s:XAUUSD a:Buy Now c:None :: {'a': 'Buy Now', 's': 'XAUUSD', 'test': True, '_meta': {'source': 'web_app', 'group': 'g_instant', 'path': '/zd', 'webhookHost': 'http://localhost:5050', 'timestamp': '2025-12-11T04:20:53.548Z', 'accessToken': 'provided'}}
2025-12-11 11:20:53,582 [ERROR] Exception on /webhook_instant [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\Users\<USER>\OneDrive\Documents\python\App\tab_webhook.py", line 359, in webhook_instant
    tp = (entry + (point*ptp)) if not vtp else vtp
TypeError: unsupported operand type(s) for *: 'float' and 'NoneType'
2025-12-11 11:20:58,736 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:21:28,772 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:21:58,779 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:22:28,790 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:22:58,807 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:23:28,816 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:23:58,823 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:24:28,833 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:24:58,849 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:25:28,862 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:25:58,874 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:26:28,881 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:26:58,892 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:27:28,907 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:27:58,913 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:28:28,930 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:28:58,937 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:29:28,953 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:29:58,962 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:30:28,971 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:30:58,981 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:31:28,988 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:31:58,994 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:32:29,005 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:32:59,017 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:33:29,023 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:33:59,032 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:34:29,093 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:34:59,103 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:35:29,115 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:35:59,125 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:36:29,139 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:36:59,145 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:37:29,158 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:37:59,174 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:38:29,182 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:38:59,187 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:39:29,194 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:39:59,209 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:40:29,214 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:40:59,225 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:41:29,235 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:41:59,257 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:42:29,274 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:42:59,288 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:43:29,298 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:43:59,316 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:44:29,330 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:44:59,338 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:45:29,347 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:45:59,361 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:46:29,374 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:46:59,378 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:47:29,384 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:47:59,397 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:48:29,405 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:48:59,420 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:49:29,425 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:49:59,432 [DEBUG] No positions found for auto BE/TP processing
2025-12-11 11:50:16,458 [INFO] \U0001f534 Webhook server STOPPED
2025-12-11 11:50:16,459 [INFO] \U0001f534 Webhook server stopped
2025-12-11 11:50:16,459 [INFO] \U0001f534 Stopping background processes...
2025-12-11 11:50:18,464 [INFO] \U0001f534 MT5 connection closed
