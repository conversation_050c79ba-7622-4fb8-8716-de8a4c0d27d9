# OpenAI API Setup and Troubleshooting Guide

## 🚨 Current Issue: API Quota Exceeded

Your OpenAI API key is correctly configured, but you've exceeded your usage quota. Here's how to resolve this:

## 📋 Quick Fix Steps

### 1. Check Your OpenAI Usage
1. Go to [OpenAI Platform Usage](https://platform.openai.com/usage)
2. Log in with your OpenAI account
3. Check your current usage and limits

### 2. Add Billing Information (If Needed)
1. Go to [OpenAI Billing](https://platform.openai.com/account/billing)
2. Add a payment method if you haven't already
3. Set up usage limits to control costs

### 3. Upgrade Your Plan (If Needed)
- **Free Tier**: Limited credits (usually $5-18 for new accounts)
- **Pay-as-you-go**: $0.002 per 1K tokens for GPT-3.5-turbo
- **Plus/Pro**: Higher limits and priority access

## 🔧 Application Configuration

### Current Settings
Your application is now configured to use:
- **Model**: `gpt-3.5-turbo` (more cost-effective than GPT-4)
- **Max <PERSON>s**: 1000 per request
- **Environment**: `.env` file loading ✅
- **API Key**: Correctly loaded ✅

### Cost Estimation
- **GPT-3.5-turbo**: ~$0.002 per 1K tokens
- **Average analysis**: ~500-1000 tokens
- **Cost per analysis**: ~$0.001-0.002 (very affordable!)

## 🛠️ Testing Your Setup

### 1. Run Environment Test
```bash
python test_env_loading.py
```
This will verify your API key is loaded correctly.

### 2. Run Direct AI Test
```bash
python test_ai_analysis_direct.py
```
This will test the actual AI analysis functionality.

### 3. Expected Results
- ✅ Environment variables loaded
- ✅ API key found
- ✅ GPT API connection successful
- ✅ Signal parsing working

## 🔍 Error Messages and Solutions

### "insufficient_quota"
**Problem**: You've used up your OpenAI credits
**Solution**: Add billing information or wait for quota reset

### "model_not_found" 
**Problem**: Your API key doesn't have access to the requested model
**Solution**: Use `gpt-3.5-turbo` instead of `gpt-4` (already configured)

### "invalid_request_error"
**Problem**: API request format issue
**Solution**: Check your API key and request format

### "rate_limit_exceeded"
**Problem**: Too many requests in a short time
**Solution**: Wait a few minutes and try again

## 💡 Cost Optimization Tips

### 1. Reduce Token Usage
- Use shorter prompts
- Reduce `max_tokens` in config (currently 1000)
- Use structured signals only when needed

### 2. Smart Usage
- Test with small amounts first
- Use the analysis button sparingly during development
- Consider caching results for repeated analysis

### 3. Monitor Usage
- Check [OpenAI Usage Dashboard](https://platform.openai.com/usage) regularly
- Set up usage alerts
- Use the free tier efficiently

## 🚀 Getting Started Again

### Step 1: Resolve Quota Issue
1. Visit [OpenAI Billing](https://platform.openai.com/account/billing)
2. Add payment method or check your free credits
3. Ensure you have available quota

### Step 2: Test the Application
1. Restart your trading application
2. Go to AI Bots tab
3. Create or edit a bot
4. Click the purple "🤖 Analyze" button
5. Check for successful analysis

### Step 3: Monitor Results
- Analysis results will appear in a popup
- Check `Logs/Signals/` folder for saved results
- Monitor your OpenAI usage dashboard

## 📊 Application Features Now Working

### ✅ Multi-timeframe Analysis
- Select multiple timeframes (M15, H1, H4, etc.)
- Comprehensive technical analysis across timeframes
- Structured data for AI processing

### ✅ Structured Signal Format
```
Signal ID: a7b9c2d4
C.GPT 
Symbol: XAUUSD 
Status: PENDING
Signal: Buy Limit 
Price: 2045.50
SL: 2040.00
TP1: 2050.00
TP2: 2055.00 
TP3: 2060.00 
TP4: 2065.00 
TP5: 2070.00
```

### ✅ Analysis Popup
- Professional results display
- Copy analysis to clipboard
- Copy structured signal format
- Comprehensive technical data

### ✅ Automatic Logging
- All analyses saved to `Logs/Signals/`
- Timestamped JSON files
- Complete analysis data preserved

## 🔧 Advanced Configuration

### Custom Model Settings
Edit `App/config.py` to customize:
```python
self.ai_api_config = {
    "gpt": {
        "model": "gpt-3.5-turbo",  # or "gpt-4" if you have access
        "max_tokens": 1000,        # Reduce to save costs
        "api_key": ""              # Leave empty to use .env
    }
}
```

### Environment Variables
Your `.env` file should contain:
```
OPENAI_API_KEY=sk-proj-your-actual-key-here
GEMINI_API_KEY=your-gemini-key-here  # Optional
```

## 📞 Support Resources

### OpenAI Support
- [OpenAI Help Center](https://help.openai.com/)
- [API Documentation](https://platform.openai.com/docs)
- [Community Forum](https://community.openai.com/)

### Application Support
- Check application logs in `Logs/` folder
- Use debug mode for detailed error messages
- Test individual components with provided test scripts

## 🎉 Success Indicators

When everything is working correctly, you should see:
- ✅ "GPT analysis completed" in application logs
- ✅ Analysis popup with structured results
- ✅ Signal files saved in `Logs/Signals/`
- ✅ No API error messages

## 💰 Pricing Reference

### GPT-3.5-turbo Pricing (as of 2024)
- **Input**: $0.0015 per 1K tokens
- **Output**: $0.002 per 1K tokens
- **Typical analysis**: ~$0.001-0.003 per request

### Usage Estimates
- **100 analyses/month**: ~$0.10-0.30
- **1000 analyses/month**: ~$1-3
- **Very affordable for trading analysis!**

---

**Remember**: The application is now fully configured and ready to work once your OpenAI quota issue is resolved. The technical implementation is complete and tested!
