<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calculate TP, SL, and P/L</title>
</head>
<body>
    <h1>Calculate TP, SL, and P/L</h1>
    <textarea id="input" rows="10" cols="50">
XAUUSD BUY 2668
MORE BUY 2665

TP 2671
TP 2674
TP 2677
TP 2680
TP 2685

SL 2659
    </textarea>
    <br><br>
    <label for="lot">Lot Size:</label>
    <input type="number" id="lot" value="0.04" step="0.01"> (default: 0.04)
    <br><br>
    <label for="contractSize">Contract Size:</label>
    <input type="number" id="contractSize" value="1000" step="1000"> (default: 1000)
    <br><br>
    <button onclick="processInput()">Calculate</button>
    <h2>Results:</h2>
    <pre id="output"></pre>

    <script>
        function processInput() {
            const input = document.getElementById('input').value;
            const lot = parseFloat(document.getElementById('lot').value) || 0.04;
            const contractSize = parseInt(document.getElementById('contractSize').value, 10) || 100000;

            const lines = input.trim().split("\n");

            // ดึงค่า Entry1 และ Entry2https://www.youtube.com/watch?v=mcSLoaycVDQ
            const entry1Match = lines[0].match(/(BUY|SELL) (\d+)/);
            const entry2Match = lines[1].match(/(BUY|SELL) (\d+)/);
            const entry1 = entry1Match ? parseInt(entry1Match[1], 10) : null;
            const entry2 = entry2Match ? parseInt(entry2Match[1], 10) : null;

            // แยกค่า TP และ SL
            const tpValues = [];
            let slValue = null;
            lines.forEach(line => {
                if (line.startsWith("TP")) {
                    const tpMatch = line.match(/TP (\d+)/);
                    if (tpMatch) {
                        tpValues.push(parseInt(tpMatch[1], 10));
                    }
                } else if (line.startsWith("SL")) {
                    const slMatch = line.match(/SL (\d+)/);
                    if (slMatch) {
                        slValue = parseInt(slMatch[1], 10);
                    }
                }
            });

            // ฟังก์ชันคำนวณ P/L
            function calculatePL(diff) {
                return (diff * lot * contractSize).toFixed(2); // คำนวณ P/L เป็น $
            }

            // ฟังก์ชันเพื่อสร้างผลลัพธ์
            function formatOutput(entry, tpValues, slValue) {
                let result = `Entry ${entry}\n`;
                tpValues.forEach((tp, index) => {
                    const diff = Math.abs(tp - entry);
                    const pl = calculatePL(diff);
                    result += `TP${index + 1} ${tp} Diff ${diff} P/L $${pl}\n`;
                });
                if (slValue !== null) {
                    const diffSL = Math.abs(entry - slValue);
                    const plSL = calculatePL(diffSL);
                    result += `SL ${slValue} Diff ${diffSL} P/L $${plSL}\n`;
                }
                result += "\n";
                return result;
            }

            // สร้างผลลัพธ์และแสดงในหน้า HTML
            let output = "";
            if (entry1 !== null) {
                output += formatOutput(entry1, tpValues, slValue);
            }
            if (entry2 !== null) {
                output += formatOutput(entry2, tpValues, slValue);
            }
            document.getElementById('output').textContent = output;
        }
    </script>
</body>
</html>