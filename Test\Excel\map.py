import pandas as pd
from geopy.geocoders import Nominatim
from geopy.extra.rate_limiter import RateLimiter
import os
pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)
# Load the Excel file
current_dir = os.path.dirname(os.path.abspath(__file__))
file_path = os.path.join(current_dir, "addresses-demo.xlsx")  # Change this to your actual file path
sheet_name = "Sheet1"         # Change if needed

# Read Excel file
df = pd.read_excel(file_path, sheet_name=sheet_name)
print(df['school'])
# Initialize geocoder
geolocator = Nominatim(user_agent="geoapiExercises")
geocode = RateLimiter(geolocator.geocode, min_delay_seconds=1)  # To respect Nominatim's rate limit

# Function to fetch latitude and longitude
def get_lat_lon(address):
    try:
        location = geocode(address)
        if location:
            return pd.Series([location.latitude, location.longitude])
        else:
            return pd.Series([None, None])
    except:
        return pd.Series([None, None])

# Apply geocoding
df[['Latitude', 'Longitude']] = df['school'].apply(get_lat_lon)  # Replace 'Address' with your column name

# Save back to Excel
output_file = "geocoded_addresses.xlsx"
df.to_excel(output_file, index=False)

print(f"Geocoded file saved to {output_file}")