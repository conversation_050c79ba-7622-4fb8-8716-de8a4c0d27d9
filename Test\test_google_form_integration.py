#!/usr/bin/env python3
"""
Test script for Google Form Integration
Demonstrates the Google Form tracking feature for order data
"""

def demonstrate_google_form_integration():
    """Demonstrate the Google Form integration feature"""
    
    print("=" * 80)
    print("GOOGLE FORM INTEGRATION FOR ORDER TRACKING")
    print("=" * 80)
    
    print("\n🎯 FEATURE OVERVIEW:")
    print("-" * 80)
    print("• Automatically sends order data to Google Sheets via pre-filled form")
    print("• Tracks all orders from Input tab, Instant tab, and Webhook")
    print("• Includes reason field for trade analysis and review")
    print("• Configurable URL template in config.py for easy updates")
    print("• Supports all order parameters: Symbol, Action, Entry, TP, Lot, etc.")
    
    print("\n📋 GOOGLE FORM CONFIGURATION:")
    print("-" * 80)
    print("Location: App/config.py")
    print()
    print("```python")
    print("# Google Form Integration for Order Tracking")
    print("# Pre-fill URL template for sending order data to Google Sheets")
    print("self.GOOGLE_FORM_URL = \"https://docs.google.com/forms/d/e/1FAIpQLSda4_GifbWv-MGp9j-2jdbtCYzUlDN-chjhprEMHUG4DkkH_g/viewform?usp=pp_url&entry.178506718={Symbol}&entry.645304246={Action}&entry.785671803={Entry}&entry.898028705={SL}&entry.1523790774={TP}&entry.381285031={Lot}&entry.1073635155={SignalID}&entry.1148525053={Comment}&entry.398055293={Reason}&entry.305299530={Risk}\"")
    print()
    print("# Google Form field mapping for easy reference")
    print("self.GOOGLE_FORM_FIELDS = {")
    print("    \"symbol\": \"entry.178506718\",")
    print("    \"action\": \"entry.645304246\",")
    print("    \"entry\": \"entry.785671803\",")
    print("    \"sl\": \"entry.898028705\",")
    print("    \"tp\": \"entry.1523790774\",")
    print("    \"lot\": \"entry.381285031\",")
    print("    \"signal_id\": \"entry.1073635155\",")
    print("    \"comment\": \"entry.1148525053\",")
    print("    \"reason\": \"entry.398055293\",")
    print("    \"risk\": \"entry.305299530\"")
    print("}")
    print("```")
    
    print("\n🔧 IMPLEMENTATION DETAILS:")
    print("-" * 80)
    
    print("1. REASON FIELD ADDED TO TABS:")
    print("   • Input Tab: Multi-line textbox for detailed trade analysis")
    print("   • Instant Tab: Multi-line textbox for quick trade notes")
    print("   • Both tabs now capture reason and pass to send_order()")
    print()
    
    print("2. ENHANCED send_order() FUNCTION:")
    print("   • Added reason and signal_id parameters")
    print("   • Automatically calls send_to_google_form() after successful orders")
    print("   • URL encoding for special characters in reason text")
    print()
    
    print("3. WEBHOOK INTEGRATION:")
    print("   • Webhook endpoints now accept 'reason' and 'signal_id' fields")
    print("   • Backward compatible - fields are optional")
    print("   • Automatically forwards data to Google Form")
    
    print("\n📊 EXAMPLE USAGE SCENARIOS:")
    print("-" * 80)
    
    print("Scenario 1 - Input Tab Order:")
    print("• Symbol: XAUUSD")
    print("• Action: Buy Limit")
    print("• Entry: 2650.00")
    print("• TP: 2680.00")
    print("• Lot: 0.02")
    print("• Comment: IN_TP1_abc12345")
    print("• Reason: \"4H uptrend above EMA20; H1 pullback into overlapping\"")
    print("         \"demand 3840-3852 with 61.8% Fib and pivot support.\"")
    print("         \"RSI midline bounce, MACD improving.\"")
    print("• Result: Data sent to Google Form automatically")
    print()
    
    print("Scenario 2 - Instant Tab Order:")
    print("• Symbol: EURUSD")
    print("• Action: Sell Now")
    print("• Entry: 1.0850 (current price)")
    print("• TP: 1.0800")
    print("• Lot: 0.05")
    print("• Comment: INSTANT")
    print("• Reason: \"Break below key support, momentum bearish\"")
    print("• Result: Data sent to Google Form with auto-generated signal ID")
    print()
    
    print("Scenario 3 - Webhook Order:")
    print("• POST /webhook_instant")
    print("• JSON payload:")
    print("  {")
    print("    \"s\": \"XAUUSD\",")
    print("    \"a\": \"Buy Now\",")
    print("    \"lot\": 0.01,")
    print("    \"ptp\": 200,")
    print("    \"psl\": 100,")
    print("    \"c\": \"WH_Signal\",")
    print("    \"reason\": \"AI signal: RSI oversold + MACD bullish crossover\",")
    print("    \"signal_id\": \"ai_001234\"")
    print("  }")
    print("• Result: Order placed and data sent to Google Form")
    
    print("\n🔍 GOOGLE FORM URL STRUCTURE:")
    print("-" * 80)
    print("Base URL: https://docs.google.com/forms/d/e/FORM_ID/viewform")
    print("Parameters:")
    print("• ?usp=pp_url - Enable pre-fill mode")
    print("• &entry.178506718={Symbol} - Symbol field")
    print("• &entry.645304246={Action} - Action field")
    print("• &entry.785671803={Entry} - Entry price field")
    print("• &entry.898028705={SL} - Stop loss field")
    print("• &entry.1523790774={TP} - Take profit field")
    print("• &entry.381285031={Lot} - Lot size field")
    print("• &entry.1073635155={SignalID} - Signal ID field")
    print("• &entry.1148525053={Comment} - Comment field")
    print("• &entry.398055293={Reason} - Reason field")
    print("• &entry.305299530={Risk} - Risk field")
    
    print("\n⚙️ CONFIGURATION MANAGEMENT:")
    print("-" * 80)
    print("Easy URL Updates:")
    print("1. Open App/config.py")
    print("2. Modify self.GOOGLE_FORM_URL")
    print("3. Update entry IDs if form structure changes")
    print("4. Restart application")
    print()
    print("Field Mapping Updates:")
    print("1. Update self.GOOGLE_FORM_FIELDS dictionary")
    print("2. Modify send_to_google_form() if needed")
    print("3. Test with sample data")
    
    print("\n🛡️ ERROR HANDLING:")
    print("-" * 80)
    print("• Network timeouts handled gracefully")
    print("• Invalid characters URL-encoded automatically")
    print("• Form submission errors logged but don't stop trading")
    print("• Fallback to console logging if form unavailable")
    print("• Debug information available in logs")
    
    print("\n📈 BENEFITS FOR TRADING ANALYSIS:")
    print("-" * 80)
    print("• Complete trade history in Google Sheets")
    print("• Reason field enables performance analysis")
    print("• Easy filtering and sorting of trades")
    print("• Statistical analysis of trade reasons")
    print("• Performance tracking by signal source")
    print("• Historical review for strategy improvement")
    
    print("\n🔧 TECHNICAL IMPLEMENTATION:")
    print("-" * 80)
    
    print("New Function: send_to_google_form()")
    print("```python")
    print("def send_to_google_form(self, symbol, action, entry, sl, tp, lot, signal_id=\"\", comment=\"\", reason=\"\", risk=\"\"):")
    print("    try:")
    print("        # Format URL with URL encoding")
    print("        url = self.config.GOOGLE_FORM_URL.format(")
    print("            Symbol=urllib.parse.quote(str(symbol)),")
    print("            Action=urllib.parse.quote(str(action)),")
    print("            Entry=urllib.parse.quote(str(entry)),")
    print("            SL=urllib.parse.quote(str(sl)),")
    print("            TP=urllib.parse.quote(str(tp)),")
    print("            Lot=urllib.parse.quote(str(lot)),")
    print("            SignalID=urllib.parse.quote(str(signal_id)),")
    print("            Comment=urllib.parse.quote(str(comment)),")
    print("            Reason=urllib.parse.quote(str(reason)),")
    print("            Risk=urllib.parse.quote(str(risk))")
    print("        )")
    print("        ")
    print("        # Send GET request to pre-fill form")
    print("        response = requests.get(url, timeout=10)")
    print("        return response.status_code == 200")
    print("    except Exception as e:")
    print("        self.add_status_frame(f\"❌ Google Form error: {e}\", \"red\")")
    print("        return False")
    print("```")
    
    print("\n📝 REASON FIELD EXAMPLES:")
    print("-" * 80)
    print("Good Reason Examples:")
    print("• \"4H uptrend above EMA20; H1 pullback into demand zone\"")
    print("• \"Break above resistance with volume confirmation\"")
    print("• \"RSI oversold bounce + MACD bullish crossover\"")
    print("• \"News-driven volatility, trading breakout pattern\"")
    print("• \"Fibonacci 61.8% retracement + pivot support confluence\"")
    print()
    print("Analysis Categories:")
    print("• Technical: Chart patterns, indicators, support/resistance")
    print("• Fundamental: News events, economic data")
    print("• Sentiment: Market mood, risk-on/risk-off")
    print("• Strategy: Specific trading system signals")
    
    print("\n🚀 GETTING STARTED:")
    print("-" * 80)
    print("1. Verify Google Form URL in config.py")
    print("2. Test with a small order from Input tab")
    print("3. Check Google Sheets for data arrival")
    print("4. Adjust field mappings if needed")
    print("5. Start using reason field for all trades")
    print("6. Review and analyze trade data regularly")
    
    print("\n" + "=" * 80)
    print("GOOGLE FORM INTEGRATION READY FOR USE!")
    print("=" * 80)
    
    print("\n💡 Your trading data will now be automatically tracked in")
    print("   Google Sheets with detailed reasons for later analysis!")
    print()
    print("🎯 Use the reason field to build a comprehensive trading journal")
    print("   that will help improve your trading performance over time!")

def show_webhook_examples():
    """Show webhook payload examples with reason field"""
    
    print("\n" + "=" * 80)
    print("WEBHOOK PAYLOAD EXAMPLES WITH REASON FIELD")
    print("=" * 80)
    
    print("\n📡 BASIC WEBHOOK PAYLOAD:")
    print("-" * 80)
    print("POST /webhook_instant")
    print("Content-Type: application/json")
    print("Authorization: Bearer YOUR_ACCESS_TOKEN")
    print("X-Access-Token: YOUR_ACCESS_TOKEN")
    print()
    print("{")
    print("  \"s\": \"XAUUSD\",")
    print("  \"a\": \"Buy Limit\",")
    print("  \"p\": 2650.00,")
    print("  \"lot\": 0.02,")
    print("  \"ptp\": 200,")
    print("  \"psl\": 100,")
    print("  \"c\": \"WH_AI_Signal\",")
    print("  \"reason\": \"AI analysis: Strong bullish momentum with RSI oversold recovery\",")
    print("  \"signal_id\": \"ai_20250106_001\"")
    print("}")
    
    print("\n📡 ADVANCED WEBHOOK PAYLOAD:")
    print("-" * 80)
    print("POST /webhook_instant")
    print("Content-Type: application/json")
    print()
    print("{")
    print("  \"s\": \"EURUSD\",")
    print("  \"a\": \"Sell Now\",")
    print("  \"lot\": 0.05,")
    print("  \"tp\": 1.0800,")
    print("  \"sl\": 1.0870,")
    print("  \"c\": \"NEWS_TRADE\",")
    print("  \"reason\": \"ECB dovish statement + USD strength. Break below 1.0850 support with volume. Target 1.0800 previous low. Risk management at 1.0870 resistance.\",")
    print("  \"signal_id\": \"news_ecb_20250106\"")
    print("}")
    
    print("\n📡 CURL COMMAND EXAMPLES:")
    print("-" * 80)
    print("# Basic order with reason")
    print("curl -X POST http://localhost:5000/webhook_instant \\")
    print("  -H \"Content-Type: application/json\" \\")
    print("  -H \"Authorization: Bearer YOUR_TOKEN\" \\")
    print("  -H \"X-Access-Token: YOUR_TOKEN\" \\")
    print("  -d '{")
    print("    \"s\": \"XAUUSD\",")
    print("    \"a\": \"Buy Now\",")
    print("    \"lot\": 0.01,")
    print("    \"ptp\": 150,")
    print("    \"psl\": 75,")
    print("    \"c\": \"SCALP\",")
    print("    \"reason\": \"Quick scalp on support bounce\",")
    print("    \"signal_id\": \"scalp_001\"")
    print("  }'")
    
    print("\n🔍 FIELD DESCRIPTIONS:")
    print("-" * 80)
    print("Required Fields:")
    print("• s: Symbol (XAUUSD, EURUSD, etc.)")
    print("• a: Action (Buy Now, Sell Now, Buy Limit, Sell Limit)")
    print()
    print("Optional Fields:")
    print("• p: Price (for limit orders)")
    print("• lot: Lot size (default from app settings)")
    print("• ptp: TP in points")
    print("• psl: SL in points")
    print("• tp: Exact TP price")
    print("• sl: Exact SL price")
    print("• c: Comment")
    print("• reason: Trade analysis/reason")
    print("• signal_id: Unique signal identifier")
    
    print("\n✅ RESPONSE EXAMPLES:")
    print("-" * 80)
    print("Success Response:")
    print("{")
    print("  \"error\": false,")
    print("  \"message\": \"Success\"")
    print("}")
    print()
    print("Error Response:")
    print("{")
    print("  \"error\": true,")
    print("  \"message\": \"Missing symbol or action or price\"")
    print("}")

if __name__ == "__main__":
    demonstrate_google_form_integration()
    show_webhook_examples()
