
import MetaTrader5 as mt5
import customtkinter as ctk
import talib as ta
import threading
import time
from datetime import datetime

# ===========================
# Class: TabAuto
# ===========================
class TabAuto:
    def __init__(self, master, config, util):
        self.frame = master.add("Auto")
        self.config = config
        self.util = util
        self.side_var = ctk.StringVar(value="BOTH")
        self.symbol_var = ctk.StringVar(value="XU")
        self.lot_var = ctk.DoubleVar(value=0.02)
        self.point_bsl_var = ctk.IntVar(value=self.config.SL_POINTS)
        self.point_btp_var = ctk.IntVar(value=self.config.TP1_POINTS)
        self.point_ssl_var = ctk.IntVar(value=self.config.SL_POINTS)
        self.point_stp_var = ctk.IntVar(value=self.config.TP1_POINTS)
        self.time_var = ctk.IntVar(value=60)
        self.checkbox_tf_vars = {}
        self.count_tf = {}
        self.count_orders = 0
        self.loop_running = False
        self.thread1 = None
        self.thread2 = None

        self.form1 = ctk.CTkFrame(self.frame)
        self.form1.pack(pady=10) 
        self.form2 = ctk.CTkFrame(self.frame)
        self.form2.pack(pady=10, padx=20) 
        self.form3 = ctk.CTkFrame(self.frame)
        self.form3.pack(pady=10) 

        self.build_ui()

    def build_ui(self):
        self.lot_var.set(0.02)  # Default lot size
        self.lot_label = ctk.CTkLabel(self.form1, text="L Size:")
        self.lot_label.grid(row=1, column=0, padx=10, pady=5)
        self.lot_val = ctk.CTkEntry(self.form1, textvariable=self.lot_var)
        self.lot_val.grid(row=1, column=1, padx=10, pady=5)

        self.symbol_label = ctk.CTkLabel(self.form1, text="Symbol:")
        self.symbol_label.grid(row=1, column=2, padx=10, pady=5)
        self.symbol_var.set("XU")  # Default symbol
        self.symbol_dropdown = ctk.CTkOptionMenu(self.form1, values=list(self.config.symbols), variable=self.symbol_var)
        self.symbol_dropdown.grid(row=1, column=3, padx=10, pady=5)
        
        self.time_var.set(5)  # Default 5 min
        self.time_label = ctk.CTkLabel(self.form1, text="Loop (min):")
        self.time_label.grid(row=2, column=0, padx=10, pady=5)
        self.time_val = ctk.CTkEntry(self.form1, textvariable=self.time_var)
        self.time_val.grid(row=2, column=1, padx=10, pady=5)

        self.side_label = ctk.CTkLabel(self.form1, text="Side:")
        self.side_label.grid(row=2, column=2, padx=10, pady=5)
        self.side_var.set("BOTH")  # Default symbol
        self.side_dropdown = ctk.CTkOptionMenu(self.form1, values=["BOTH","BUY","SELL"], variable=self.side_var)
        self.side_dropdown.grid(row=2, column=3, padx=10, pady=5)
 
        self.point_bsl_var.set(self.config.SL_POINTS)  # Default sl size
        self.point_bsl_label = ctk.CTkLabel(self.form1, text="B.SL Point:")
        self.point_bsl_label.grid(row=3, column=0, padx=10, pady=5)
        self.point_bsl_val = ctk.CTkEntry(self.form1, textvariable=self.point_bsl_var)
        self.point_bsl_val.grid(row=3, column=1, padx=10, pady=5)

        self.point_btp_var.set(self.config.TP2_POINTS)  # Default tp size
        self.point_btp_label = ctk.CTkLabel(self.form1, text="B.TP Point:")
        self.point_btp_label.grid(row=3, column=2, padx=10, pady=5)
        self.point_btp_val = ctk.CTkEntry(self.form1, textvariable=self.point_btp_var)
        self.point_btp_val.grid(row=3, column=3, padx=10, pady=5)

        self.point_ssl_var.set(self.config.SL_POINTS)  # Default sl size
        self.point_ssl_label = ctk.CTkLabel(self.form1, text="S.SL Point:")
        self.point_ssl_label.grid(row=4, column=0, padx=10, pady=5)
        self.point_ssl_val = ctk.CTkEntry(self.form1, textvariable=self.point_ssl_var)
        self.point_ssl_val.grid(row=4, column=1, padx=10, pady=5)

        self.point_stp_var.set(self.config.TP1_POINTS)  # Default tp size
        self.point_stp_label = ctk.CTkLabel(self.form1, text="S.TP Point:")
        self.point_stp_label.grid(row=4, column=2, padx=10, pady=5)
        self.point_stp_val = ctk.CTkEntry(self.form1, textvariable=self.point_stp_var)
        self.point_stp_val.grid(row=4, column=3, padx=10, pady=5)

        # ctk.CTkLabel(self.form2, text="🕒 Select Timeframes:").grid(row=0, column=0, columnspan=6, padx=10, pady=(0, 5), sticky="w")

        self.status_label = ctk.CTkLabel(self.form3, text="🔘 Not Monitoring")
        self.status_label.pack(pady=1)
        self.status_label_1_tf = {}
        self.status_label_2_tf = {}
        self.status_label_3_tf = {}
        for i, tf_name in enumerate(self.config.timeframes):
            var = ctk.BooleanVar()
            checkbox = ctk.CTkCheckBox(self.form2, text=tf_name, variable=var)
            checkbox.grid(row=1, column=i, padx=0, pady=5, sticky="w")
            self.checkbox_tf_vars[tf_name] = var
            self.status_label_1_tf[tf_name] = ctk.CTkLabel(self.form2, text="")
            self.status_label_1_tf[tf_name].grid(row=2, column=i, padx=0, pady=5, sticky="w")
            self.status_label_2_tf[tf_name] = ctk.CTkLabel(self.form2, text="")
            self.status_label_2_tf[tf_name].grid(row=3, column=i, padx=0, pady=5, sticky="w")
            self.status_label_3_tf[tf_name] = ctk.CTkLabel(self.form2, text="")
            self.status_label_3_tf[tf_name].grid(row=4, column=i, padx=0, pady=5, sticky="w")

        stop_btn = ctk.CTkButton(self.frame, text="⏹ Stop", fg_color="red", command=self.stop_loop)
        stop_btn.pack(side="left", pady=10)
        
        start_btn = ctk.CTkButton(self.frame, text="▶ Start", fg_color="green", command=self.start_loop)
        start_btn.pack(side="right", pady=10)

    def get_selected_timeframes(self):
        selected = [self.config.timeframes[name] for name, var in self.checkbox_tf_vars.items() if var.get()]
        # selected = [self.config.timeframes[name] for name, var in self.checkbox_tf_vars if var]
        print("✅ Selected Timeframes:", selected)
        return selected
    
    def start_loop(self):
        if not self.loop_running:
            self.loop_running = True
            self.thread1 = threading.Thread(target=self.checking_loop, daemon=True)
            self.thread1.start()
            self.thread2 = threading.Thread(target=self.compute_loop, daemon=True)
            self.thread2.start()
        self.util.add_status_frame(f"🟢 Monitoring {self.side_var.get()} side started {self.symbol_var.get()}")
        self.status_label.configure(text=f"🟢 Monitoring {self.side_var.get()} side started {self.symbol_var.get()}")

    def stop_loop(self):
        self.loop_running = False
        self.util.add_status_frame(f"🔴 Monitoring {self.side_var.get()} side stopped {self.symbol_var.get()}")
        self.status_label.configure(text=f"🔴 Monitoring {self.side_var.get()} side stopped {self.symbol_var.get()}")

    def checking_loop(self):
        try:
            symbol = self.util.get_symbol(self.symbol_var)
            i = 0 # Loop ตรวจสอบ
            while self.loop_running:
                if self.util.is_in_restricted_time():
                    self.util.set_status_label(f"{time.strftime('%H:%M:%S')} ⏳ Restricted time (21:00 - 22:00 UTC). Skipping...", "yellow")
                    time.sleep(60*60) # 1 hour
                else:
                    i += 1
                    self.count_tf = self.util.update_SL_to_BE_by_point(symbol, "A1_", False) 
                    self.util.set_status_label(f"{time.strftime('%H:%M:%S')} 🕒 Checking {i}: curently {self.count_tf["All_FT"]} orders", "yellow")
                    # time.sleep(self.time_var.get()*60)  # 60 sec = 1 min /  300 sec = 5 mins
                    time.sleep(60)  # 60 sec = 1 min /  300 sec = 5 mins
        except KeyboardInterrupt:
            print("Stopped by user")
        finally:
            mt5.shutdown()
    
    def compute_loop(self):
        # while self.loop_running: 
        try:
            symbol = self.util.get_symbol(self.symbol_var)
            lot = self.lot_var.get()
            i = 0 # Loop ตรวจสอบ
            while self.loop_running:
                if self.util.is_in_restricted_time():
                    # self.util.set_status_label(f"{time.strftime('%H:%M:%S')} ⏳ Restricted time (21:00 - 22:00 UTC). Skipping...", "yellow")
                    time.sleep(60*60) # 1 hour
                else:
                    i += 1 
                    # self.count_orders = self.util.update_SL_to_BE_by_point() 
                    # self.util.set_status_label(f"{time.strftime('%H:%M:%S')} 🕒 Checking {i}: curently {self.count_orders} orders", "yellow")
                    for tf_name, tf_check in self.checkbox_tf_vars.items():
                        if tf_check.get():
                            df = self.util.get_data(symbol, self.config.timeframes[tf_name])
                            trend1 = self.compute_rsma_indicators(df, symbol, lot, self.count_tf[tf_name], self.config.RSI1_LEN, self.config.SMA1_LEN, tf_name+"_R1")
                            # trend2 = self.compute_rsma_indicators(df, symbol, lot, self.count_orders, self.config.RSI2_LEN, self.config.SMA2_LEN, "R2")
                            trend3 = self.compute_rsma_indicators(df, symbol, lot, self.count_tf[tf_name], self.config.RSI3_LEN, self.config.SMA3_LEN, tf_name+"_R3")
                                
                            self.status_label_1_tf[tf_name].configure(text=f"📦 {self.count_tf[tf_name]['B']} / {self.count_tf[tf_name]['S']}", text_color="yellow")
                            self.status_label_2_tf[tf_name].configure(text=f"🕒 {trend1['trend']}", text_color=trend1['color'])
                            # self.status_label2.configure(text=f"🕒 {trend2['trend']}: {trend2['data']}", text_color=trend2['color'])
                            self.status_label_3_tf[tf_name].configure(text=f"🕒 {trend3['trend']}", text_color=trend3['color'])
                            
                    self.status_label.configure(text=f"🟢 Monitoring {self.side_var.get()} side started {self.symbol_var.get()}\n{time.strftime('%H:%M:%S')} 🕒 Checking indicators {i} time(s)")

                    # self.util.set_status_label(f"{time.strftime('%H:%M:%S')} 🕒 Checking {i}: curently {count} orders", "yellow")
                    # time.sleep(self.config.TIMEFRAME1)  # 60 sec = 1 min /  300 sec = 5 mins
                    time.sleep(self.time_var.get()*60)  # 60 sec = 1 min /  300 sec = 5 mins
        except KeyboardInterrupt:
            print("Stopped by user")
        finally:
            mt5.shutdown()

                
    def compute_rsma_indicators(self, df, symbol, lot, count, rsi, sma, comment):
        df['rsi'] = ta.RSI(df['close'], timeperiod=rsi)
        df['sma'] = ta.SMA(df['rsi'], timeperiod=sma)
        
        df['rsi_prev_1'] = df['rsi'].shift(1)
        df['sma_prev_1'] = df['sma'].shift(1)
        df['rsi_prev_2'] = df['rsi'].shift(2)
        df['sma_prev_2'] = df['sma'].shift(2)
        order_type = "NO" 
        point = mt5.symbol_info(symbol).point 

        k0 = -1
        k1 = -1
        k2 = -2
        df['signalBuy'] = (df['rsi_prev_1'] < df['sma_prev_1']) & (df['rsi'] > df['sma'])  # (df['rsi_prev_2'] < df['sma_prev_2']) & (df['rsi_prev_1'] > df['sma_prev_1']) & (df['rsi'] > df['sma'])
        df['signalSell'] =  (df['rsi_prev_1'] > df['sma_prev_1']) & (df['rsi'] < df['sma']) # (df['rsi_prev_2'] > df['sma_prev_2']) & (df['rsi_prev_1'] < df['sma_prev_1']) & (df['rsi'] < df['sma'])
        if df['signalBuy'].iloc[-1]:  
            # df['rsi'].iloc[k2] < df['sma'].iloc[k2] and df['rsi'].iloc[k1] > df['sma'].iloc[k1]: # and df['rsi'].iloc[k0] > df['sma'].iloc[k0]: # crossover(df['rsi'], df['sma']):
            entry = mt5.symbol_info_tick(symbol).ask
            sl = entry - self.point_bsl_var.get() * point 
            tp = entry + self.point_btp_var.get() * point  
            order_type = "Buy Now"
            if (self.side_var.get() == "BUY" or self.side_var.get() == "BOTH") and (count['B'] < self.config.MAX_ORDERS):
                # self.util.close_orders_by_condition("all-sell")
                self.util.close_orders_by_condition("sell-profit")
                self.util.send_order(order_type, symbol, lot, entry, sl, tp, "A1_" + comment)

        elif df['signalSell'].iloc[-1]:  # (df['rsi_prev'] > df['sma_prev']) & (df['rsi'] < df['sma']): 
            # df['rsi'].iloc[k2] > df['sma'].iloc[k2] and df['rsi'].iloc[k1] < df['sma'].iloc[k1]: # and df['rsi'].iloc[k0] < df['sma'].iloc[k0]: # crossunder(df['rsi'], df['sma']):
            entry = mt5.symbol_info_tick(symbol).bid
            sl =  entry + self.point_ssl_var.get() * point
            tp =  entry - self.point_stp_var.get() * point
            order_type = "Sell Now"
            if (self.side_var.get() == "SELL" or self.side_var.get() == "BOTH") and (count['S'] < self.config.MAX_ORDERS) :
                # self.util.close_orders_by_condition("all-buy")
                self.util.close_orders_by_condition("buy-profit")
                self.util.send_order(order_type, symbol, lot, entry, sl, tp, "A1_" + comment)
        
        trend = df['rsi'].iloc[k1] > df['sma'].iloc[k1]
        return {
            "color" : ('lime' if trend else 'red'),
            "trend" : ('BULL' if trend else 'BEAR'),
            "order_type" : order_type,
            "data" : f"r{rsi} = " + "{:.2f}".format(df['rsi'].iloc[k1], 2) + f" | s{sma} = " + "{:.2f}".format(df['sma'].iloc[k1], 2)
    }
    