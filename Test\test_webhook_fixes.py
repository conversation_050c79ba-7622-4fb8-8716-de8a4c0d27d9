#!/usr/bin/env python3
"""
Test the webhook fixes for GPT-5 and symbol handling
"""

import os
import sys

# Load environment variables first
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ Environment variables loaded from .env file")
except ImportError:
    # Manual loading
    env_path = os.path.join(os.getcwd(), '.env')
    if os.path.exists(env_path):
        with open(env_path, 'r') as f:
            for line in f:
                if '=' in line and not line.strip().startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
        print("✅ Environment variables loaded manually from .env file")

def test_gpt5_parameters():
    """Test GPT-5 parameter handling"""
    print("🧪 Testing GPT-5 Parameter Handling")
    print("=" * 40)
    
    try:
        from App.config import Config
        from App.util import Util
        
        config = Config()
        util = Util(config)
        
        # Check current model configuration
        model = config.ai_api_config.get('gpt', {}).get('model', 'gpt-4')
        print(f"✅ Current model: {model}")
        
        # Test parameter selection logic
        max_tokens_value = 100  # Small value for testing
        
        payload = {
            "model": model,
            "messages": [{"role": "user", "content": "Test"}],
            "temperature": 0.7
        }
        
        # Use correct parameter name based on model
        if "gpt-5" in model.lower():
            payload["max_completion_tokens"] = max_tokens_value
            param_used = "max_completion_tokens"
        else:
            payload["max_tokens"] = max_tokens_value
            param_used = "max_tokens"
        
        print(f"✅ Using parameter: {param_used} = {max_tokens_value}")
        print(f"✅ Payload structure: {list(payload.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ GPT-5 parameter test failed: {e}")
        return False

def test_symbol_variations():
    """Test symbol variation handling"""
    print("\n🧪 Testing Symbol Variations")
    print("=" * 35)
    
    try:
        from App.config import Config
        from App.util import Util
        
        config = Config()
        util = Util(config)
        
        # Test symbol variations logic
        symbol = "XAUUSD"
        
        symbol_variations = [
            symbol,  # Original symbol
            symbol + (config.symbol_posfix.get() if config.symbol_posfix else ""),  # With postfix
            symbol.replace("XAUUSD", "GOLD"),  # Common MT5 variation
            symbol.replace("XAUUSD", "XAU/USD"),  # Another variation
            symbol.replace("EURUSD", "EUR/USD"),  # EUR variation
        ]
        
        # Remove duplicates while preserving order
        symbol_variations = list(dict.fromkeys(symbol_variations))
        
        print(f"✅ Original symbol: {symbol}")
        print(f"✅ Symbol variations to try: {symbol_variations}")
        
        # Test if any symbols exist in MT5
        import MetaTrader5 as mt5
        
        if mt5.initialize():
            print("✅ MT5 initialized successfully")
            
            # Get all available symbols
            symbols = mt5.symbols_get()
            if symbols:
                symbol_names = [s.name for s in symbols]
                print(f"✅ Found {len(symbol_names)} symbols in MT5")
                
                # Check which variations exist
                found_symbols = []
                for var in symbol_variations:
                    if var in symbol_names:
                        found_symbols.append(var)
                
                if found_symbols:
                    print(f"✅ Available symbol variations: {found_symbols}")
                else:
                    print("⚠️ None of the symbol variations found in MT5")
                    # Show some gold-related symbols
                    gold_symbols = [s for s in symbol_names if 'gold' in s.lower() or 'xau' in s.lower()]
                    if gold_symbols:
                        print(f"💡 Gold-related symbols found: {gold_symbols[:5]}")
            else:
                print("⚠️ No symbols retrieved from MT5")
        else:
            print("❌ MT5 initialization failed")
        
        return True
        
    except Exception as e:
        print(f"❌ Symbol variation test failed: {e}")
        return False

def test_error_handling():
    """Test improved error handling"""
    print("\n🧪 Testing Error Handling")
    print("=" * 30)
    
    try:
        # Test error message formatting
        symbol = "XAUUSD"
        timeframes = ["H1"]
        
        error_msg = f"Failed to retrieve chart data for {symbol} {timeframes}. Check if symbol exists in MT5 and is spelled correctly."
        
        print(f"✅ Improved error message format:")
        print(f"   {error_msg}")
        
        # Test different error scenarios
        test_cases = [
            ("Invalid symbol", "INVALID_SYMBOL", ["H1"]),
            ("Invalid timeframe", "XAUUSD", ["INVALID_TF"]),
            ("Multiple issues", "INVALID", ["INVALID"])
        ]
        
        for case_name, test_symbol, test_timeframes in test_cases:
            error_msg = f"Failed to retrieve chart data for {test_symbol} {test_timeframes}. Check if symbol exists in MT5 and is spelled correctly."
            print(f"✅ {case_name}: {error_msg}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False

def test_api_call_simulation():
    """Simulate API call with new parameters"""
    print("\n🧪 Testing API Call Simulation")
    print("=" * 35)
    
    try:
        from App.config import Config
        from App.util import Util
        
        config = Config()
        util = Util(config)
        
        # Test with a simple prompt
        test_prompt = "Analyze XAUUSD and provide a brief trading signal."
        
        print(f"✅ Testing with prompt: {test_prompt[:50]}...")
        
        # This will test the actual API call with new parameters
        result = util.call_gpt_api(test_prompt)
        
        if result.get("error"):
            print(f"⚠️ API call failed (expected if quota exceeded): {result.get('message')}")
            
            # Check if it's a parameter error
            if "max_tokens" in result.get('message', '').lower():
                print("❌ Parameter error still exists - fix needed")
                return False
            else:
                print("✅ Parameter handling working - error is quota/auth related")
                return True
        else:
            print("✅ API call successful!")
            print(f"   Response length: {len(result.get('analysis', ''))} chars")
            return True
            
    except Exception as e:
        print(f"❌ API call simulation failed: {e}")
        return False

def main():
    """Run webhook fix tests"""
    print("🔧 Webhook Fixes Test Suite")
    print("=" * 40)
    
    # Run all tests
    tests = [
        ("GPT-5 Parameters", test_gpt5_parameters),
        ("Symbol Variations", test_symbol_variations),
        ("Error Handling", test_error_handling),
        ("API Call Simulation", test_api_call_simulation)
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n📋 Fix Test Results:")
    print("=" * 25)
    
    all_passed = True
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  - {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All fixes working correctly!")
        print("\n📖 What was fixed:")
        print("  ✅ GPT-5 parameter handling (max_completion_tokens)")
        print("  ✅ Symbol variation attempts (GOLD, XAU/USD, etc.)")
        print("  ✅ Better error messages with troubleshooting hints")
        print("  ✅ Improved webhook error handling")
    else:
        print("\n⚠️ Some fixes need attention.")
    
    print("\n💡 Next Steps:")
    print("1. Restart the webhook server")
    print("2. Test the AI analysis request again")
    print("3. Check MT5 for correct symbol names")
    print("4. Verify OpenAI API quota and model access")

if __name__ == "__main__":
    main()
