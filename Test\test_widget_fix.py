#!/usr/bin/env python3
"""
Test script to verify the CustomTkinter widget error fixes.
This script simulates the conditions that cause the TclError exceptions.
"""

import customtkinter as ctk
import tkinter as tk
import threading
import time
import random

class TestApp:
    def __init__(self):
        self.is_shutting_down = False
        
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        
        self.root = ctk.CTk()
        self.root.title("Widget Error Fix Test")
        self.root.geometry("600x400")
        
        # Create scrollable frame similar to status_scroll_frame
        self.scroll_frame = ctk.CTkScrollableFrame(self.root, width=500, height=300)
        self.scroll_frame.pack(padx=20, pady=20, fill="both", expand=True)
        
        # List to track labels
        self.labels = []
        self.LIMIT_LOGS = 50
        
        # Control buttons
        button_frame = ctk.CTkFrame(self.root)
        button_frame.pack(fill="x", padx=20, pady=10)
        
        self.start_button = ctk.CTkButton(button_frame, text="Start Adding Labels", command=self.start_adding_labels)
        self.start_button.pack(side="left", padx=5)
        
        self.stop_button = ctk.CTkButton(button_frame, text="Stop", command=self.stop_adding_labels)
        self.stop_button.pack(side="left", padx=5)
        
        self.clear_button = ctk.CTkButton(button_frame, text="Clear All", command=self.clear_all_labels)
        self.clear_button.pack(side="left", padx=5)
        
        # Status
        self.status_label = ctk.CTkLabel(self.root, text="Ready")
        self.status_label.pack(pady=5)
        
        # Threading control
        self.adding_labels = False
        self.label_thread = None
        
        self.root.protocol("WM_DELETE_WINDOW", self.on_close)
        
    def _is_widget_valid(self, widget):
        """Check if a widget is valid and still exists"""
        try:
            return widget and hasattr(widget, 'winfo_exists') and widget.winfo_exists()
        except (AttributeError, tk.TclError):
            return False
    
    def _cleanup_old_labels(self):
        """Safely cleanup old labels with improved error handling"""
        try:
            # First, clean up any invalid references
            valid_labels = []
            for label in self.labels:
                if self._is_widget_valid(label):
                    valid_labels.append(label)
            
            self.labels = valid_labels
            
            # Now remove excess labels
            while len(self.labels) > self.LIMIT_LOGS:
                try:
                    old_label = self.labels.pop()
                    if self._is_widget_valid(old_label):
                        # Schedule destruction on main thread to avoid race conditions
                        try:
                            old_label.after_idle(old_label.destroy)
                        except (AttributeError, tk.TclError):
                            # Fallback to immediate destruction
                            try:
                                old_label.destroy()
                            except (AttributeError, tk.TclError):
                                pass  # Already destroyed
                except (IndexError, AttributeError):
                    break  # No more labels to remove
                    
        except Exception as e:
            print(f"Error during label cleanup: {e}")
            # Reset the list if cleanup fails completely
            self.labels = []
    
    def add_label_safely(self, text, color="white"):
        """Add a label with improved error handling"""
        if self.is_shutting_down:
            return
            
        def update_gui():
            try:
                # Enhanced safety checks for frame existence
                if not self.scroll_frame or not self._is_widget_valid(self.scroll_frame):
                    return
                
                # Check if parent window exists
                try:
                    if not self.scroll_frame.winfo_toplevel().winfo_exists():
                        return
                except (AttributeError, tk.TclError):
                    return
                
                # Create label with error handling
                try:
                    label = ctk.CTkLabel(self.scroll_frame, text=text, anchor="w", justify="left", text_color=color)
                except Exception as e:
                    print(f"Failed to create label: {e}")
                    return
                
                # Safely add the label
                try:
                    if self.labels:
                        # Clean up any invalid references first
                        self.labels = [l for l in self.labels if self._is_widget_valid(l)]
                        
                        if self.labels and self._is_widget_valid(self.labels[0]):
                            label.pack(fill="x", anchor="w", before=self.labels[0])
                        else:
                            label.pack(fill="x", anchor="w")
                    else:
                        label.pack(fill="x", anchor="w")
                    
                    # Only add to list if packing succeeded
                    self.labels.insert(0, label)
                    
                except Exception as e:
                    print(f"Failed to pack label: {e}")
                    try:
                        label.destroy()
                    except:
                        pass
                    return
                
                # Safe cleanup of old labels
                self._cleanup_old_labels()
                
            except Exception as e:
                print(f"GUI update error: {e}")
        
        # Schedule GUI update safely
        if self._is_widget_valid(self.scroll_frame):
            try:
                self.scroll_frame.after_idle(update_gui)
            except (AttributeError, tk.TclError):
                try:
                    self.scroll_frame.after(0, update_gui)
                except (AttributeError, tk.TclError):
                    print(f"Failed to schedule GUI update: {text}")
    
    def label_adding_loop(self):
        """Background thread that adds labels continuously"""
        counter = 0
        colors = ["white", "yellow", "cyan", "green", "red", "orange"]
        
        while self.adding_labels and not self.is_shutting_down:
            counter += 1
            color = random.choice(colors)
            text = f"Test message #{counter} - {time.strftime('%H:%M:%S')}"
            
            self.add_label_safely(text, color)
            
            # Update status on main thread
            if self._is_widget_valid(self.status_label):
                try:
                    self.status_label.after_idle(
                        lambda: self.status_label.configure(text=f"Added {counter} labels")
                    )
                except:
                    pass
            
            # Random delay to simulate real usage
            time.sleep(random.uniform(0.1, 0.5))
    
    def start_adding_labels(self):
        """Start the label adding thread"""
        if not self.adding_labels:
            self.adding_labels = True
            self.label_thread = threading.Thread(target=self.label_adding_loop, daemon=True)
            self.label_thread.start()
            self.start_button.configure(state="disabled")
            self.stop_button.configure(state="normal")
    
    def stop_adding_labels(self):
        """Stop the label adding thread"""
        self.adding_labels = False
        self.start_button.configure(state="normal")
        self.stop_button.configure(state="disabled")
        if self._is_widget_valid(self.status_label):
            self.status_label.configure(text="Stopped")
    
    def clear_all_labels(self):
        """Clear all labels"""
        try:
            for label in self.labels[:]:  # Create a copy to iterate
                try:
                    if self._is_widget_valid(label):
                        label.destroy()
                except:
                    pass
            self.labels.clear()
            if self._is_widget_valid(self.status_label):
                self.status_label.configure(text="Cleared all labels")
        except Exception as e:
            print(f"Error clearing labels: {e}")
    
    def on_close(self):
        """Handle window close event"""
        self.is_shutting_down = True
        self.adding_labels = False
        
        # Clean up labels
        try:
            for label in self.labels[:]:
                try:
                    if hasattr(label, 'destroy'):
                        label.destroy()
                except:
                    pass
            self.labels.clear()
        except Exception as e:
            print(f"Error during cleanup: {e}")
        
        # Close the window
        try:
            self.root.quit()
        except:
            pass
        try:
            self.root.destroy()
        except:
            pass
    
    def run(self):
        """Start the application"""
        print("Starting Widget Error Fix Test...")
        print("This test simulates the conditions that cause TclError exceptions.")
        print("Click 'Start Adding Labels' to begin the test.")
        print("Try closing the window while labels are being added to test the fix.")
        self.root.mainloop()

if __name__ == "__main__":
    app = TestApp()
    app.run()
