import customtkinter as ctk
import MetaTrader5 as mt5
from dotenv import load_dotenv
import os
import time
import subprocess
import psutil

# Initialize MetaTrader 5
load_dotenv()

folder_dir = os.getenv('DIR')
server = os.getenv('SERVER')
symbol = os.getenv('SYMBOL')
path = "C:\\Program Files\\MetaTrader 5\\terminal64.exe"
strategy_name = "GUI 2"

def is_mt5_running():
    """Check if MetaTrader 5 is already running"""
    for process in psutil.process_iter(attrs=["name"]):
        if "terminal64.exe" in process.info["name"]:
            return True
    return False

def start_mt5():
    """ Start MT5 in the background if it's not running """
    if not is_mt5_running():
        print("Starting MT5...")
        subprocess.Popen(
            path,
            shell=True,
            creationflags=subprocess.CREATE_NO_WINDOW  # Hide MT5 window
        )
        time.sleep(5)  # Wait for MT5 to initialize

start_mt5()  # Ensure MT5 is running
if not mt5.initialize():
    print("Initialize() failed, error code =", mt5.last_error())
 
# Check if the symbol is available in the market watch
# if not mt5.symbol_select(symbol, True):
#     messagebox.showerror("Error", f"Failed to select symbol {symbol}")
#     mt5.shutdown()
    #return

init_price = 3000 # mt5.symbol_info_tick(symbol).ask  # sell = bid/buyer price  , buy = ask/seller price
init_tp = init_price + 10
init_sl = init_price - 5

# ==============================================

# Maximum number of TP fields
MAX_TP_FIELDS = 10

# Map order type to MT5 constants
order_type_mapping = {
    "Buy Now": mt5.ORDER_TYPE_BUY,
    "Sell Now": mt5.ORDER_TYPE_SELL,
    "Buy Limit": mt5.ORDER_TYPE_BUY_LIMIT,
    "Sell Limit": mt5.ORDER_TYPE_SELL_LIMIT,
    "Buy Stop": mt5.ORDER_TYPE_BUY_STOP,
    "Sell Stop": mt5.ORDER_TYPE_SELL_STOP,
}

action_type_mapping = {
    "Buy Now": mt5.TRADE_ACTION_DEAL,
    "Sell Now": mt5.TRADE_ACTION_DEAL,
    "Buy Limit": mt5.TRADE_ACTION_PENDING,
    "Sell Limit": mt5.TRADE_ACTION_PENDING,
    "Buy Stop": mt5.TRADE_ACTION_PENDING,
    "Sell Stop": mt5.TRADE_ACTION_PENDING,
}

def send_order(order_type, lot, entry, sl, tp):
    request = {
        "action": action_type_mapping[order_type],
        "type_filling": mt5.ORDER_FILLING_IOC,
        "symbol": symbol,
        "volume": lot,
        "type": order_type_mapping[order_type],
        "price": entry,
        "sl": sl,
        "tp": tp,
        "deviation": 10,
        "magic": 161032,  # Unique identifier for your EA or script
        "comment": "Python Script Order",
    }

    # Send the order
    result = mt5.order_send(request)
    if result.retcode != mt5.TRADE_RETCODE_DONE:
        ctk.CTkMessageBox.show_error("Error", f"Failed to send order: {result.comment} // {order_type} et {entry} sl {sl}")
        return
    return result

def submit_instant_BUY(): 
    try:
        entry = mt5.symbol_info_tick(symbol).ask
        send_order("Buy Now", 0.02, entry, entry - 5, entry + 10)
    except Exception as e:
        ctk.CTkMessageBox.show_error("Error", f"An error occurred: {e}")
 
def submit_instant_SELL():
    try:
        entry = mt5.symbol_info_tick(symbol).ask
        send_order("Sell Now", 0.02, entry, entry + 5, entry - 10)
    except Exception as e:
        ctk.CTkMessageBox.show_error("Error", f"An error occurred: {e}")
 
def submit_order():  
    try:
        # Get user inputs
        entry = float(entry_var.get())
        sl = float(sl_var.get())
        order_type = order_type_var.get()

        # Collect TP and Lot values
        tp_lot_values = []
        confirmTxt = ""
        for tp_var, lot_var in tp_lot_vars:
            tp = float(tp_var.get())
            lot = float(lot_var.get())
            if tp and lot:
                tp_lot_values.append((tp, lot))
                tp_point = int(abs(entry - tp) * 100)
                confirmTxt += f"\nTP {tp}  /  {tp_point} point"
 
        sl_point = int(abs(entry - sl) * 100)
        response = ctk.CTkMessageBox.askyesno("Order Confirmation!!", f'{order_type} \nET {entry} \nSL {sl}  /  {sl_point} point {confirmTxt}')
        if response:
            # Prepare and send orders with different take-profit levels and lot sizes
            for tp, lot in tp_lot_values:
                send_order(order_type, lot, entry, sl, tp)
            ctk.CTkMessageBox.show_info("Success", "Orders sent successfully!")
    except Exception as e:
        ctk.CTkMessageBox.show_error("Error", f"An error occurred: {e}")

def add_tp_field():
    if len(tp_lot_vars) < MAX_TP_FIELDS:
        tp_var = ctk.StringVar(value=init_tp)
        lot_var = ctk.StringVar(value="0.01")
        row = len(tp_lot_vars) + 3

        tp_label = ctk.CTkLabel(root, text=f"TP{len(tp_lot_vars) + 1}:")
        tp_label.grid(row=row, column=0)

        tp_entry = ctk.CTkEntry(root, textvariable=tp_var)
        tp_entry.grid(row=row, column=1)

        lot_label = ctk.CTkLabel(root, text="Lot:")
        lot_label.grid(row=row, column=2)

        lot_entry = ctk.CTkEntry(root, textvariable=lot_var)
        lot_entry.grid(row=row, column=3)

        delete_button = ctk.CTkButton(root, text="X", command=lambda: delete_tp_field(tp_label, tp_entry, lot_label, lot_entry, delete_button, tp_var, lot_var))
        delete_button.grid(row=row, column=4)

        tp_lot_vars.append((tp_var, lot_var))
    else:
        ctk.CTkMessageBox.show_warning("Limit Reached", f"You can only add up to {MAX_TP_FIELDS} TP fields.")

def delete_tp_field(tp_label, tp_entry, lot_label, lot_entry, button, tp_var, lot_var):
    tp_label.destroy()
    tp_entry.destroy()
    lot_label.destroy()
    lot_entry.destroy()
    button.destroy()
    tp_lot_vars.remove((tp_var, lot_var))

# Initialize customtkinter
ctk.set_appearance_mode("dark")  # Set dark mode for the GUI
ctk.set_default_color_theme("blue")  # Set blue theme

# Create main window
root = ctk.CTk()
root.geometry("600x400")
root.title(server)

# Create Tabview
tabview = ctk.CTkTabview(root)
tabview.pack(pady=20, padx=20, fill="both", expand=True)

# Add "Trade" tab
trade_tab_frame = tabview.add("Trade")

# Variables for input fields
entry_var = ctk.StringVar(value=init_price)
sl_var = ctk.StringVar(value=init_sl)
tp_lot_vars = []
order_type_var = ctk.StringVar(value="Buy Limit")

# Input labels and fields in Trade tab
ctk.CTkLabel(trade_tab_frame, text=" =============== Input ORDER =============== ").grid(row=0, column=0, columnspan=5)
ctk.CTkLabel(trade_tab_frame, text="Type:").grid(row=1, column=0)
ctk.CTkOptionMenu(trade_tab_frame, values=["Buy Limit", "Sell Limit", "Buy Now", "Sell Now", "Buy Stop", "Sell Stop"], variable=order_type_var).grid(row=1, column=1)

ctk.CTkLabel(trade_tab_frame, text="Entry:").grid(row=2, column=0)
ctk.CTkEntry(trade_tab_frame, textvariable=entry_var).grid(row=2, column=1)

ctk.CTkLabel(trade_tab_frame, text="SL:").grid(row=2, column=2)
ctk.CTkEntry(trade_tab_frame, textvariable=sl_var).grid(row=2, column=3)

add_tp_field()

# Button to add more TP fields
ctk.CTkButton(trade_tab_frame, text="Add TP", command=add_tp_field).grid(row=MAX_TP_FIELDS + 3, column=1, columnspan=2)

# Submit button
ctk.CTkButton(trade_tab_frame, text="Submit", command=submit_order).grid(row=MAX_TP_FIELDS + 3, column=2, columnspan=2)

# Instant Order button
ctk.CTkLabel(trade_tab_frame, text=" ============== Instant ORDER ============== ").grid(row=MAX_TP_FIELDS + 5, column=0, columnspan=5)
ctk.CTkButton(trade_tab_frame, text="Instant BUY", command=submit_instant_BUY).grid(row=MAX_TP_FIELDS + 6, column=1, columnspan=2)
ctk.CTkButton(trade_tab_frame, text="Instant SELL", command=submit_instant_SELL).grid(row=MAX_TP_FIELDS + 6, column=2, columnspan=2)

# Run the GUI event loop
root.mainloop()
