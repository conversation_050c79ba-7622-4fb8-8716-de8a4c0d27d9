#!/usr/bin/env python3
"""
MyApp Launcher - Alternative Python-based launcher
This can be converted to an executable using PyInstaller
"""

import os
import sys
import subprocess
import tkinter as tk
from tkinter import messagebox
from pathlib import Path

def find_python_executable():
    """Find the Python executable in the virtual environment"""
    script_dir = Path(__file__).parent
    venv_python = script_dir / "venv" / "Scripts" / "python.exe"
    
    if venv_python.exists():
        return str(venv_python)
    
    # Fallback to system Python
    return sys.executable

def check_requirements():
    """Check if all required files exist"""
    script_dir = Path(__file__).parent
    
    # Check for main.py
    main_py = script_dir / "main.py"
    if not main_py.exists():
        return False, "main.py not found in the application directory"
    
    # Check for virtual environment
    venv_dir = script_dir / "venv"
    if not venv_dir.exists():
        return False, "Virtual environment (venv) not found"
    
    return True, "All requirements met"

def launch_app():
    """Launch the main application"""
    try:
        # Change to script directory
        script_dir = Path(__file__).parent
        os.chdir(script_dir)
        
        # Check requirements
        requirements_ok, message = check_requirements()
        if not requirements_ok:
            messagebox.showerror("Error", f"Cannot start application:\n{message}")
            return False
        
        # Find Python executable
        python_exe = find_python_executable()
        
        # Launch the application
        print("Starting MyApp...")
        print(f"Using Python: {python_exe}")
        print(f"Working directory: {script_dir}")
        
        # Run the application
        result = subprocess.run([python_exe, "main.py"], 
                              cwd=script_dir,
                              capture_output=False)
        
        if result.returncode != 0:
            messagebox.showerror("Error", f"Application exited with error code: {result.returncode}")
            return False
        
        return True
        
    except Exception as e:
        messagebox.showerror("Error", f"Failed to start application:\n{str(e)}")
        return False

def create_gui_launcher():
    """Create a simple GUI launcher"""
    root = tk.Tk()
    root.title("MyApp Launcher")
    root.geometry("300x150")
    root.resizable(False, False)
    
    # Center the window
    root.eval('tk::PlaceWindow . center')
    
    # Create widgets
    label = tk.Label(root, text="MyApp Launcher", font=("Arial", 14, "bold"))
    label.pack(pady=20)
    
    def on_launch():
        root.withdraw()  # Hide launcher window
        success = launch_app()
        if success:
            root.destroy()  # Close launcher if app started successfully
        else:
            root.deiconify()  # Show launcher again if failed
    
    launch_btn = tk.Button(root, text="Launch Application", 
                          command=on_launch, 
                          font=("Arial", 12),
                          bg="#4CAF50", fg="white",
                          padx=20, pady=10)
    launch_btn.pack(pady=10)
    
    exit_btn = tk.Button(root, text="Exit", 
                        command=root.destroy,
                        font=("Arial", 10),
                        padx=20, pady=5)
    exit_btn.pack(pady=5)
    
    root.mainloop()

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--gui":
        create_gui_launcher()
    else:
        # Direct launch
        launch_app()
