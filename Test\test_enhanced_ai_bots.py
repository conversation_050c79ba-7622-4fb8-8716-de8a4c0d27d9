#!/usr/bin/env python3
"""
Test script for enhanced AI Bot functionality including:
- Multi-timeframe support
- Structured signal format
- Real OpenAI API integration
"""

import requests
import json
import os

# Configuration
WEBHOOK_URL = "http://localhost:5000"
AUTH_TOKEN = "your_auth_token_here"
ACCESS_TOKEN = "your_access_token_here"

def test_multi_timeframe_ai_analysis():
    """Test multi-timeframe AI analysis with structured signal format"""
    print("🧪 Testing Multi-timeframe AI Analysis with Signal Format")
    print("=" * 60)
    
    # Test 1: Multi-timeframe with structured signal format
    print("\n1. Testing Multi-timeframe with Structured Signal Format...")
    signal_format_data = {
        "symbol": "XAUUSD",
        "timeframes": ["M15", "H1", "H4"],
        "barback": 50,
        "prompt": "Analyze gold market trends and provide a trading signal with specific entry and exit levels",
        "ai": "gpt",
        "use_signal_format": True
    }
    
    response = send_webhook_request("/webhook_ai_analysis", signal_format_data)
    if response and not response.get("error"):
        print("✅ Multi-timeframe signal analysis successful!")
        print(f"   - Symbol: {response.get('symbol')}")
        print(f"   - Timeframes: {response.get('timeframes')}")
        print(f"   - AI Provider: {response.get('ai_provider')}")
        print(f"   - Structured Signal: {response.get('structured_signal', False)}")
        
        # Display signal data if available
        signal_data = response.get('signal_data')
        if signal_data:
            print("   📋 Parsed Signal Data:")
            print(f"      Signal ID: {signal_data.get('signal_id', 'N/A')}")
            print(f"      Signal Type: {signal_data.get('signal_type', 'N/A')}")
            print(f"      Entry Price: {signal_data.get('entry_price', 'N/A')}")
            print(f"      Stop Loss: {signal_data.get('sl_price', 'N/A')}")
            print(f"      Take Profits: TP1:{signal_data.get('tp1_price', 'N/A')} TP2:{signal_data.get('tp2_price', 'N/A')}")
        
        print("\n   📊 Raw AI Response:")
        print(f"   {response.get('analysis', '')[:200]}...")
        
    else:
        print(f"❌ Multi-timeframe signal analysis failed: {response.get('message', 'Unknown error') if response else 'No response'}")
    
    # Test 2: Single timeframe with regular analysis
    print("\n2. Testing Single Timeframe Regular Analysis...")
    regular_data = {
        "symbol": "XAUUSD",
        "timeframes": ["H1"],
        "barback": 100,
        "prompt": "Provide detailed technical analysis and market outlook",
        "ai": "gpt",
        "use_signal_format": False
    }
    
    response = send_webhook_request("/webhook_ai_analysis", regular_data)
    if response and not response.get("error"):
        print("✅ Single timeframe regular analysis successful!")
        print(f"   - Timeframes: {response.get('timeframes')}")
        print(f"   - Analysis Length: {len(response.get('analysis', ''))} characters")
        print(f"   - Chart Data Bars: {response.get('chart_data', {}).get('total_bars', 0)}")
    else:
        print(f"❌ Single timeframe analysis failed: {response.get('message', 'Unknown error') if response else 'No response'}")

def test_signal_format_parsing():
    """Test signal format parsing with sample data"""
    print("\n🧪 Testing Signal Format Parsing")
    print("=" * 40)
    
    # Sample signal format response
    sample_signal = """Signal ID: a7b9c2d4
C.GPT 
Symbol: XAUUSD 
Status: PENDING
Signal: Buy Limit 
Price: 2045.50
SL: 2040.00
TP1: 2050.00
TP2: 2055.00 
TP3: 2060.00 
TP4: 2065.00 
TP5: 2070.00"""
    
    print("Sample Signal Format:")
    print(sample_signal)
    print("\nExpected Parsing:")
    print("- Signal ID: a7b9c2d4")
    print("- Signal Type: Buy Limit")
    print("- Entry Price: 2045.50")
    print("- Stop Loss: 2040.00")
    print("- Take Profits: 2050.00, 2055.00, 2060.00, 2065.00, 2070.00")

def test_ai_bot_configuration():
    """Test AI bot configuration with new features"""
    print("\n🧪 Testing Enhanced AI Bot Configuration")
    print("=" * 45)
    
    # Sample enhanced bot configuration
    enhanced_bot_config = {
        "name": "Enhanced XAUUSD Multi-TF Bot",
        "symbol": "XU",
        "timeframes": ["M15", "H1", "H4"],  # Multi-timeframe
        "bars_back": 100,
        "prompt": "Analyze XAUUSD across multiple timeframes and provide structured trading signals",
        "api_provider": "gpt",
        "auto_place_order": True,
        "schedule_check": True,
        "check_interval": 30,
        "use_chart_image": False,
        "use_signal_format": True,  # Use structured format
        "multi_timeframe": True
    }
    
    print("Enhanced Bot Configuration:")
    print(json.dumps(enhanced_bot_config, indent=2))
    
    print("\n✅ New Features:")
    print("   - Multi-timeframe Analysis: ✅")
    print("   - Structured Signal Format: ✅")
    print("   - Auto Place Order: ✅")
    print("   - Scheduled Checks: ✅")
    print("   - Chart Image Support: ✅")

def send_webhook_request(endpoint, data):
    """Send webhook request with authentication"""
    try:
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {AUTH_TOKEN}",
            "X-Access-Token": ACCESS_TOKEN
        }
        
        print(f"   Sending request to {WEBHOOK_URL}{endpoint}")
        
        response = requests.post(
            f"{WEBHOOK_URL}{endpoint}",
            json=data,
            headers=headers,
            timeout=120  # Longer timeout for AI analysis
        )
        
        print(f"   Response Status: {response.status_code}")
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"   HTTP Error: {response.text}")
            return {"error": True, "message": f"HTTP {response.status_code}: {response.text}"}
            
    except requests.exceptions.RequestException as e:
        print(f"   Request Exception: {e}")
        return {"error": True, "message": f"Request failed: {e}"}

def test_openai_connection():
    """Test OpenAI API connection"""
    print("🔧 Testing OpenAI API Connection")
    print("=" * 35)
    
    api_key = os.getenv('OPENAI_API_KEY')
    if api_key:
        print("✅ OpenAI API Key found in environment")
        print(f"   Key: {api_key[:10]}...{api_key[-4:]}")
        
        # Test basic OpenAI connection
        try:
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            
            test_payload = {
                "model": "gpt-4",
                "messages": [
                    {"role": "user", "content": "Test connection - respond with 'OK'"}
                ],
                "max_tokens": 10
            }
            
            response = requests.post(
                "https://api.openai.com/v1/chat/completions",
                headers=headers,
                json=test_payload,
                timeout=30
            )
            
            if response.status_code == 200:
                print("✅ OpenAI API connection successful")
                result = response.json()
                if 'choices' in result:
                    print(f"   Response: {result['choices'][0]['message']['content']}")
            else:
                print(f"❌ OpenAI API connection failed: {response.status_code}")
                print(f"   Error: {response.text}")
                
        except Exception as e:
            print(f"❌ OpenAI API test failed: {e}")
    else:
        print("❌ OpenAI API Key not found")
        print("   Set environment variable: export OPENAI_API_KEY='your_key'")

def main():
    """Run enhanced AI Bot tests"""
    print("🚀 Enhanced AI Bot Test Suite")
    print("=" * 50)
    
    # Test OpenAI connection
    test_openai_connection()
    
    # Test signal format parsing
    test_signal_format_parsing()
    
    # Test AI bot configuration
    test_ai_bot_configuration()
    
    # Test multi-timeframe AI analysis
    test_multi_timeframe_ai_analysis()
    
    print("\n🎉 Enhanced AI Bot Tests Completed!")
    print("\n📋 Summary of New Features:")
    print("  ✅ Multi-timeframe Support in AI Bots Tab")
    print("  ✅ Structured Signal Format Configuration")
    print("  ✅ Real OpenAI API Integration")
    print("  ✅ Signal Format Parsing")
    print("  ✅ Enhanced Bot Configuration")
    
    print("\n📖 Usage Instructions:")
    print("1. Go to AI Bots tab in the application")
    print("2. Select multiple timeframes using checkboxes")
    print("3. Enable 'Use Structured Signal Format'")
    print("4. Set OpenAI API key: export OPENAI_API_KEY='your_key'")
    print("5. Test bot to get structured trading signals")

if __name__ == "__main__":
    main()
