2025-12-15 14:57:50,055 [INFO] Serving on http://0.0.0.0:5050
2025-12-15 14:57:50,565 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-12-15 14:57:51,118 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-12-15 14:57:52,118 [INFO] \U0001f7e2 Orders processing loop started
2025-12-15 14:57:52,119 [INFO] \U0001f7e2 Orders processing loop started
2025-12-15 14:57:53,134 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 14:58:23,155 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 14:58:53,171 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 14:59:23,189 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 14:59:53,209 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:00:23,215 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:00:53,233 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:01:23,238 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:01:53,247 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:02:11,703 [INFO] \U0001f534 Webhook server STOPPED
2025-12-15 15:02:11,704 [INFO] \U0001f534 Webhook server stopped
2025-12-15 15:02:11,704 [INFO] \U0001f534 Stopping background processes...
2025-12-15 15:02:13,709 [INFO] \U0001f534 MT5 connection closed
2025-12-15 15:02:21,854 [INFO] Serving on http://0.0.0.0:5050
2025-12-15 15:02:22,361 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-12-15 15:02:22,787 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-12-15 15:02:23,829 [INFO] \U0001f7e2 Orders processing loop started
2025-12-15 15:02:23,830 [INFO] \U0001f7e2 Orders processing loop started
2025-12-15 15:02:24,433 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:02:54,449 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:03:24,462 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:03:54,478 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:04:24,489 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:04:54,499 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:05:24,516 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:05:54,533 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:06:24,546 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:06:54,557 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:07:24,572 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:07:54,579 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:08:24,593 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:08:54,612 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:09:24,626 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:09:52,113 [INFO] \U0001f534 Webhook server STOPPED
2025-12-15 15:09:52,113 [INFO] \U0001f534 Webhook server stopped
2025-12-15 15:09:52,114 [INFO] \U0001f534 Stopping background processes...
2025-12-15 15:09:54,114 [INFO] \U0001f534 MT5 connection closed
2025-12-15 15:10:17,306 [INFO] Serving on http://0.0.0.0:5050
2025-12-15 15:10:17,815 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-12-15 15:10:18,236 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-12-15 15:10:19,252 [INFO] \U0001f7e2 Orders processing loop started
2025-12-15 15:10:19,253 [INFO] \U0001f7e2 Orders processing loop started
2025-12-15 15:10:19,257 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:10:49,321 [INFO] \u26a0\ufe0f MT5 connection lost, attempting to reconnect...
2025-12-15 15:10:59,333 [INFO] \u26a0\ufe0f MT5 connection lost, attempting to reconnect...
2025-12-15 15:11:09,351 [INFO] \u26a0\ufe0f MT5 connection lost, attempting to reconnect...
2025-12-15 15:11:19,364 [INFO] \u26a0\ufe0f MT5 connection lost, attempting to reconnect...
2025-12-15 15:11:29,370 [INFO] \u26a0\ufe0f MT5 connection lost, attempting to reconnect...
2025-12-15 15:11:39,382 [INFO] \U0001f4c9 Market appears closed - stopping refresh loop and starting market monitor
2025-12-15 15:11:39,398 [INFO] \U0001f4ca Market monitor started
2025-12-15 15:11:39,453 [INFO] \U0001f534 Refresh loop stopped
2025-12-15 15:15:13,019 [INFO] \U0001f534 Webhook server STOPPED
2025-12-15 15:15:13,019 [INFO] \U0001f534 Webhook server stopped
2025-12-15 15:15:13,020 [INFO] \U0001f534 Stopping background processes...
2025-12-15 15:15:15,024 [INFO] \U0001f534 MT5 connection closed
2025-12-15 15:15:27,761 [INFO] Serving on http://0.0.0.0:5050
2025-12-15 15:15:28,269 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-12-15 15:15:28,714 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-12-15 15:15:29,728 [INFO] \U0001f7e2 Orders processing loop started
2025-12-15 15:15:29,729 [INFO] \U0001f7e2 Orders processing loop started
2025-12-15 15:15:29,733 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:15:59,811 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:16:29,825 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:16:59,839 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:17:22,350 [INFO] \U0001f534 Webhook server STOPPED
2025-12-15 15:17:22,351 [INFO] \U0001f534 Webhook server stopped
2025-12-15 15:17:22,352 [INFO] \U0001f534 Stopping background processes...
2025-12-15 15:17:24,358 [INFO] \U0001f534 MT5 connection closed
2025-12-15 15:19:23,963 [INFO] Serving on http://0.0.0.0:5050
2025-12-15 15:19:24,473 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-12-15 15:19:24,957 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-12-15 15:19:26,171 [INFO] \U0001f7e2 Orders processing loop started
2025-12-15 15:19:26,172 [INFO] \U0001f7e2 Orders processing loop started
2025-12-15 15:19:26,282 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:19:56,298 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:20:26,308 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:20:56,318 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:21:08,743 [INFO] \u274c Try to update TP to BE all: No open positions.
2025-12-15 15:21:22,692 [INFO] \U0001f504 Manual trailing SL calculation started for XAUUSD (ALL) - Type: Fixed
2025-12-15 15:21:22,694 [INFO] \u274c MT5 operation failed: (-4, 'Terminal: Not found')
2025-12-15 15:21:22,695 [INFO] \u2139\ufe0f No ALL positions found or conditions not met for XAUUSD
2025-12-15 15:21:26,328 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:21:40,158 [INFO] \u274c Try to update SL to BE all: No open positions.
2025-12-15 15:21:56,346 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:22:26,360 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:22:56,374 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:23:26,389 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:23:56,403 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:24:26,421 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:24:56,429 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:25:26,440 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:25:56,454 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:26:26,471 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:26:56,477 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:27:26,484 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:27:56,496 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:28:26,502 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:28:56,519 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:29:26,539 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:29:56,549 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:30:26,562 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:30:47,661 [INFO] \U0001f504 Manual trailing SL calculation started for XAUUSD (ALL) - Type: Fixed
2025-12-15 15:30:47,662 [INFO] \u274c MT5 operation failed: (-4, 'Terminal: Not found')
2025-12-15 15:30:47,662 [INFO] \u2139\ufe0f No ALL positions found or conditions not met for XAUUSD
2025-12-15 15:30:56,568 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:31:06,191 [INFO] \u274c Try to update SL to BE all: No open positions.
2025-12-15 15:31:10,655 [INFO] \u274c Try to update SL to BE all: No open positions.
2025-12-15 15:31:16,337 [INFO] \u274c Try to close all-profit: No open positions.
2025-12-15 15:31:18,988 [INFO] \U0001f504 Manual trailing SL calculation started for XAUUSD (BUY) - Type: Fixed
2025-12-15 15:31:18,989 [INFO] \u274c MT5 operation failed: (-4, 'Terminal: Not found')
2025-12-15 15:31:18,990 [INFO] \u2139\ufe0f No BUY positions found or conditions not met for XAUUSD
2025-12-15 15:31:21,340 [INFO] \U0001f504 Manual trailing SL calculation started for XAUUSD (SELL) - Type: Fixed
2025-12-15 15:31:21,343 [INFO] \u274c MT5 operation failed: (-4, 'Terminal: Not found')
2025-12-15 15:31:21,343 [INFO] \u2139\ufe0f No SELL positions found or conditions not met for XAUUSD
2025-12-15 15:31:26,585 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:31:56,603 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:32:26,611 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:32:56,624 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:33:26,632 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:33:56,638 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:34:26,653 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:34:56,658 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:35:26,676 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:35:56,688 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:36:26,696 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:36:56,708 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:37:26,726 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:37:56,740 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:38:26,759 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:38:56,775 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:39:26,789 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:39:56,800 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:40:26,815 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:40:56,829 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:41:26,840 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:41:56,851 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:42:26,866 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:42:56,873 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:43:26,888 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:43:56,904 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:44:26,957 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:44:56,971 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:45:26,983 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:45:56,991 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:46:27,006 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:46:57,017 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:47:27,025 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:47:57,039 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:48:27,044 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:48:57,058 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:49:27,073 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:49:57,088 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:50:27,100 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:50:57,106 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:51:27,117 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:51:57,123 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:52:27,129 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:52:57,148 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:53:27,168 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:53:57,181 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:54:27,189 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:54:57,205 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:55:27,219 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:55:57,225 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:56:18,294 [INFO] \u274c Webhook failed: Missing symbol or action or price - s:XAUUSD a:Buy Limit p:None c:TEST_Comment
2025-12-15 15:56:27,230 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:56:57,244 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:57:03,083 [INFO] \u2705 Webhook Input: s:XAUUSD a:Buy Limit p:3282.11 c:TEST_Comment :: {'a': 'Buy Limit', 'c': 'TEST_Comment', 'p': 3282.11, 'tp': 3312.11, 'sl': 3272.11, 's': 'XAUUSD'}
2025-12-15 15:57:03,085 [INFO] \u274c Failed to get tick info for symbol: XAUUSD
2025-12-15 15:57:13,195 [INFO] \u2705 Webhook Input: s:XU a:Buy Limit p:3282.11 c:TEST_Comment :: {'a': 'Buy Limit', 'c': 'TEST_Comment', 'p': 3282.11, 'tp': 3312.11, 'sl': 3272.11, 's': 'XU'}
2025-12-15 15:57:13,204 [INFO] \u274c Failed to get tick info for symbol: XU
2025-12-15 15:57:27,253 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:57:57,270 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:58:27,281 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:58:57,298 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:59:27,315 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 15:59:47,711 [INFO] \U0001f534 Webhook server STOPPED
2025-12-15 15:59:47,711 [INFO] \U0001f534 Webhook server stopped
2025-12-15 15:59:47,712 [INFO] \U0001f534 Stopping background processes...
2025-12-15 15:59:49,725 [INFO] \U0001f534 MT5 connection closed
2025-12-15 18:40:46,783 [INFO] Serving on http://0.0.0.0:5050
2025-12-15 18:40:47,292 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-12-15 18:40:47,934 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-12-15 18:40:48,998 [INFO] \U0001f7e2 Orders processing loop started
2025-12-15 18:40:49,001 [INFO] \U0001f7e2 Orders processing loop started
2025-12-15 18:40:49,821 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:41:19,847 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:41:41,376 [INFO] \u2705 Webhook Input: s:XAUUSD a:Buy Limit p:3282.11 c:TEST_Comment :: {'a': 'Buy Limit', 'c': 'TEST_Comment', 'p': 3282.11, 'tp': 3312.11, 'sl': 3272.11, 's': 'XAUUSD'}
2025-12-15 18:41:41,389 [INFO] \u274c Failed to get tick info for symbol: XAUUSD
2025-12-15 18:41:49,868 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:42:19,874 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:42:33,647 [INFO] \u2705 Webhook Input: s:XAUUSD a:Buy Limit p:3282.11 c:TEST_Comment :: {'a': 'Buy Limit', 'c': 'TEST_Comment', 'p': 3282.11, 'tp': 3312.11, 'sl': 3272.11, 's': 'XAUUSD'}
2025-12-15 18:42:33,684 [INFO] \u274c Webhook Input error: 'NoneType' object is not iterable
2025-12-15 18:42:49,887 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:43:19,894 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:43:49,900 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:44:07,781 [INFO] \u2705 Webhook Instant: s:XAUUSD a:Buy Limit c:TEST_Comment :: {'a': 'Buy Limit', 'c': 'TEST_Comment', 'p': 3282.11, 'tp': 3312.11, 'sl': 3272.11, 's': 'XAUUSD'}
2025-12-15 18:44:07,814 [INFO] \U0001f504 Preparing order: Buy Limit XAUUSD.s 0.01 lots
2025-12-15 18:44:07,861 [INFO] \U0001f4ca Price info: bid=4339.84, ask=4340.1, using=3282.11
2025-12-15 18:44:07,868 [INFO] \U0001f4cb Order request prepared: {'action': 5, 'symbol': 'XAUUSD.s', 'volume': 0.01, 'type': 2, 'price': 3282.11, 'sl': 3272.11, 'tp': 3312.11, 'deviation': 10, 'magic': 1000, 'comment': 'TEST_Comment', 'type_filling': 0}
2025-12-15 18:44:07,964 [INFO] \U0001f4e4 Sending order request...
2025-12-15 18:44:08,216 [INFO] \U0001f4e4 Order send result: OrderSendResult(retcode=10009, deal=0, order=34900293, volume=0.01, price=0.0, bid=0.0, ask=0.0, comment='Request executed', request_id=955530004, retcode_external=0, request=TradeRequest(action=5, magic=1000, order=0, symbol='XAUUSD.s', volume=0.01, price=3282.11, stoplimit=0.0, sl=3272.11, tp=3312.11, deviation=10, type=2, type_filling=0, type_time=0, expiration=0, comment='TEST_Comment', position=0, position_by=0))
2025-12-15 18:44:08,264 [INFO] \U0001f7e2 Sending Buy Limit: XAUUSD.s lot 0.01 @ 3282.11, SL 3272.11, TP 3312.11 - TEST_Comment
2025-12-15 18:44:08,270 [INFO] \U0001f4ca Preparing Google Form submission for XAUUSD.s Buy Limit
2025-12-15 18:44:09,227 [INFO] \U0001f4ca Order data submitted to Google Form: XAUUSD.s Buy Limit
2025-12-15 18:44:09,228 [DEBUG] Google Form submitted successfully
2025-12-15 18:44:09,234 [INFO] \u2705 Google Form submission successful for XAUUSD.s
2025-12-15 18:44:19,908 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:44:40,484 [INFO] \u2705 Webhook Instant: s:XAUUSD a:Buy Limit c:TEST_Comment :: {'a': 'Buy Limit', 'c': 'TEST_Comment', 'p': 3282.11, 'tp': 3312.11, 'sl': 3272.11, 's': 'XAUUSD'}
2025-12-15 18:44:40,487 [ERROR] Exception on /webhook_instant [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "c:\Users\<USER>\OneDrive\Documents\python\App\tab_webhook.py", line 355, in webhook_instant
    point = mt5.symbol_info(symbol).point
AttributeError: 'NoneType' object has no attribute 'point'
2025-12-15 18:44:49,929 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:44:56,854 [INFO] \u2705 Webhook Instant: s:XAUUSD a:Buy Limit c:TEST_Comment :: {'a': 'Buy Limit', 'c': 'TEST_Comment', 'p': 3282.11, 'tp': 3312.11, 'sl': 3272.11, 's': 'XAUUSD'}
2025-12-15 18:44:56,858 [ERROR] Exception on /webhook_instant [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "c:\Users\<USER>\OneDrive\Documents\python\App\tab_webhook.py", line 355, in webhook_instant
    point = mt5.symbol_info(symbol).point
AttributeError: 'NoneType' object has no attribute 'point'
2025-12-15 18:45:11,161 [INFO] \u2705 Webhook Instant: s:GOLD a:Buy Limit c:TEST_Comment :: {'a': 'Buy Limit', 'c': 'TEST_Comment', 'p': 3282.11, 'tp': 3312.11, 'sl': 3272.11, 's': 'GOLD'}
2025-12-15 18:45:11,183 [INFO] \U0001f504 Preparing order: Buy Limit GOLD 0.01 lots
2025-12-15 18:45:11,399 [INFO] \U0001f4ca Price info: bid=4340.38, ask=4340.85, using=3282.11
2025-12-15 18:45:11,402 [INFO] \U0001f4cb Order request prepared: {'action': 5, 'symbol': 'GOLD', 'volume': 0.01, 'type': 2, 'price': 3282.11, 'sl': 3272.11, 'tp': 3312.11, 'deviation': 10, 'magic': 1000, 'comment': 'TEST_Comment', 'type_filling': 0}
2025-12-15 18:45:11,412 [INFO] \U0001f4e4 Sending order request...
2025-12-15 18:45:11,610 [INFO] \U0001f4e4 Order send result: OrderSendResult(retcode=10009, deal=0, order=632379196, volume=0.01, price=0.0, bid=0.0, ask=0.0, comment='Request executed', request_id=1543945487, retcode_external=0, request=TradeRequest(action=5, magic=1000, order=0, symbol='GOLD', volume=0.01, price=3282.11, stoplimit=0.0, sl=3272.11, tp=3312.11, deviation=10, type=2, type_filling=0, type_time=0, expiration=0, comment='TEST_Comment', position=0, position_by=0))
2025-12-15 18:45:11,876 [INFO] \U0001f7e2 Sending Buy Limit: GOLD lot 0.01 @ 3282.11, SL 3272.11, TP 3312.11 - TEST_Comment
2025-12-15 18:45:11,883 [INFO] \U0001f4ca Preparing Google Form submission for GOLD Buy Limit
2025-12-15 18:45:12,578 [INFO] \U0001f4ca Order data submitted to Google Form: GOLD Buy Limit
2025-12-15 18:45:12,580 [DEBUG] Google Form submitted successfully
2025-12-15 18:45:12,600 [INFO] \u2705 Google Form submission successful for GOLD
2025-12-15 18:45:19,938 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:45:49,958 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:46:19,970 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:46:49,989 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:47:20,008 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:47:50,017 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:48:20,032 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:48:50,041 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:49:20,049 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:49:50,060 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:50:20,079 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:50:50,098 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:51:20,112 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:51:50,127 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:53:02,784 [INFO] Serving on http://0.0.0.0:5050
2025-12-15 18:53:03,282 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-12-15 18:53:03,906 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-12-15 18:53:05,101 [INFO] \U0001f7e2 Orders processing loop started
2025-12-15 18:53:05,103 [INFO] \U0001f7e2 Orders processing loop started
2025-12-15 18:53:05,704 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:53:35,724 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:53:55,258 [INFO] \u2705 Webhook Instant: s:XAUUSD a:Buy Limit c:TEST_Comment :: {'a': 'Buy Limit', 'c': 'TEST_Comment', 'p': 3282.11, 'tp': 3312.11, 'sl': 3272.11, 's': 'XAUUSD'}
2025-12-15 18:53:55,276 [INFO] \U0001f504 Preparing order: Buy Limit XAUUSD.s 0.01 lots
2025-12-15 18:53:55,411 [INFO] \U0001f4ca Price info: bid=4343.23, ask=4343.49, using=3282.11
2025-12-15 18:53:55,416 [INFO] \U0001f4cb Order request prepared: {'action': 5, 'symbol': 'XAUUSD.s', 'volume': 0.01, 'type': 2, 'price': 3282.11, 'sl': 3272.11, 'tp': 3312.11, 'deviation': 10, 'magic': 1000, 'comment': 'TEST_Comment', 'type_filling': 0}
2025-12-15 18:53:55,419 [INFO] \U0001f4e4 Sending order request...
2025-12-15 18:53:55,669 [INFO] \U0001f4e4 Order send result: OrderSendResult(retcode=10009, deal=0, order=34900866, volume=0.01, price=0.0, bid=0.0, ask=0.0, comment='Request executed', request_id=955530007, retcode_external=0, request=TradeRequest(action=5, magic=1000, order=0, symbol='XAUUSD.s', volume=0.01, price=3282.11, stoplimit=0.0, sl=3272.11, tp=3312.11, deviation=10, type=2, type_filling=0, type_time=0, expiration=0, comment='TEST_Comment', position=0, position_by=0))
2025-12-15 18:53:55,673 [INFO] \U0001f7e2 Sending Buy Limit: XAUUSD.s lot 0.01 @ 3282.11, SL 3272.11, TP 3312.11 - TEST_Comment
2025-12-15 18:53:55,677 [INFO] \U0001f4ca Preparing Google Form submission for XAUUSD.s Buy Limit
2025-12-15 18:53:56,450 [INFO] \U0001f4ca Order data submitted to Google Form: XAUUSD.s Buy Limit
2025-12-15 18:53:56,451 [DEBUG] Google Form submitted successfully
2025-12-15 18:53:56,456 [INFO] \u2705 Google Form submission successful for XAUUSD.s
2025-12-15 18:54:05,743 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:54:35,748 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:55:05,760 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:55:35,778 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:56:05,788 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:56:35,807 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:57:05,820 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:57:35,825 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:58:05,842 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:58:10,462 [INFO] \U0001f534 Webhook server STOPPED
2025-12-15 18:58:10,463 [INFO] \U0001f534 Webhook server stopped
2025-12-15 18:58:10,464 [INFO] \U0001f534 Stopping background processes...
2025-12-15 18:58:12,475 [INFO] \U0001f534 MT5 connection closed
2025-12-15 18:58:19,310 [INFO] Serving on http://0.0.0.0:5050
2025-12-15 18:58:19,811 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-12-15 18:58:20,651 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-12-15 18:58:22,020 [INFO] \U0001f7e2 Orders processing loop started
2025-12-15 18:58:22,021 [INFO] \U0001f7e2 Orders processing loop started
2025-12-15 18:58:22,745 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 18:58:31,416 [INFO] \U0001f534 Webhook server STOPPED
2025-12-15 18:58:31,416 [INFO] \U0001f534 Webhook server stopped
2025-12-15 18:58:31,417 [INFO] \U0001f534 Stopping background processes...
2025-12-15 18:58:33,420 [INFO] \U0001f534 MT5 connection closed
2025-12-15 19:04:06,024 [INFO] Serving on http://0.0.0.0:5050
2025-12-15 19:04:06,524 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-12-15 19:04:07,308 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-12-15 19:04:08,420 [INFO] \U0001f7e2 Orders processing loop started
2025-12-15 19:04:08,422 [INFO] \U0001f7e2 Orders processing loop started
2025-12-15 19:04:09,950 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:04:39,974 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:05:09,980 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:05:39,995 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:06:10,012 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:06:40,030 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:07:10,037 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:07:40,045 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:08:10,057 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:08:40,072 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:09:10,086 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:09:40,091 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:10:10,110 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:10:40,127 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:11:10,135 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:11:40,155 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:12:10,164 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:12:40,173 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:13:10,191 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:13:40,202 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:14:10,212 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:14:40,219 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:15:10,223 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:15:40,235 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:16:10,251 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:16:40,260 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:17:10,271 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:17:40,277 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:18:10,296 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:18:40,318 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:19:10,338 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:19:40,354 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:20:10,374 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:20:40,383 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:21:10,399 [DEBUG] No positions found for auto BE/TP processing
2025-12-15 19:21:39,062 [INFO] \U0001f534 Webhook server STOPPED
2025-12-15 19:21:39,063 [INFO] \U0001f534 Webhook server stopped
2025-12-15 19:21:39,063 [INFO] \U0001f534 Stopping background processes...
2025-12-15 19:21:41,066 [INFO] \U0001f534 MT5 connection closed
