#!/usr/bin/env python3
"""
Test script to verify Auto BE works independently of Auto Refresh
"""

import time
import threading
from datetime import datetime

class MockTabOrders:
    """Mock version of TabOrders to test the logic"""
    
    def __init__(self):
        # Simulate the same variables as real TabOrders
        self.loop_running = False
        self.auto_refresh_enabled = False
        self.auto_be_all_enabled = True
        self.auto_be_sig_enabled = False
        self.auto_be_input_enabled = False
        self.time_var_value = 5
        
    def has_auto_be_enabled(self):
        """Check if any auto BE mode is enabled"""
        return (self.auto_be_all_enabled or 
                self.auto_be_sig_enabled or 
                self.auto_be_input_enabled)
    
    def start_loop(self):
        """Start the background loop"""
        if not self.loop_running:
            self.loop_running = True
            thread = threading.Thread(target=self.refresh_loop, daemon=True)
            thread.start()
            print(f"[{datetime.now().strftime('%H:%M:%S')}] ✅ Loop started")
    
    def stop_loop(self):
        """Stop the loop only if both auto refresh and auto BE are disabled"""
        if not self.auto_refresh_enabled and not self.has_auto_be_enabled():
            self.loop_running = False
            print(f"[{datetime.now().strftime('%H:%M:%S')}] 🔴 Loop stopped - all functions disabled")
        else:
            reasons = []
            if self.auto_refresh_enabled:
                reasons.append("auto refresh")
            if self.has_auto_be_enabled():
                reasons.append("auto BE")
            print(f"[{datetime.now().strftime('%H:%M:%S')}] 🟡 Loop continues for: {', '.join(reasons)}")
    
    def refresh_loop(self):
        """Main loop that handles both refresh and auto BE"""
        loop_count = 0
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 🔄 Background loop started")
        
        while self.loop_running:
            try:
                loop_count += 1
                
                # Perform operations based on what's enabled
                if self.auto_refresh_enabled:
                    print(f"[{datetime.now().strftime('%H:%M:%S')}] 📊 Refreshing data (loop {loop_count})")
                
                # Always process auto BE if any mode is enabled
                if self.has_auto_be_enabled():
                    be_types = []
                    if self.auto_be_all_enabled:
                        be_types.append("All")
                    if self.auto_be_sig_enabled:
                        be_types.append("SIG")
                    if self.auto_be_input_enabled:
                        be_types.append("INPUT")
                    print(f"[{datetime.now().strftime('%H:%M:%S')}] 🎯 Processing Auto BE: {'+'.join(be_types)} (loop {loop_count})")
                
                # Adjust sleep time based on what's enabled
                if self.auto_refresh_enabled:
                    sleep_time = max(self.time_var_value, 10)  # Minimum 10 seconds
                else:
                    sleep_time = max(self.time_var_value * 2, 30)  # Minimum 30 seconds for BE only
                
                print(f"[{datetime.now().strftime('%H:%M:%S')}] ⏱️  Sleeping for {sleep_time} seconds...")
                time.sleep(sleep_time)
                
            except Exception as e:
                print(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ Loop error: {e}")
                break
        
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 🛑 Background loop ended")
    
    def toggle_auto_refresh(self, enabled):
        """Toggle auto refresh"""
        self.auto_refresh_enabled = enabled
        
        if self.auto_refresh_enabled or self.has_auto_be_enabled():
            self.start_loop()
        else:
            self.stop_loop()
        
        status = "enabled" if self.auto_refresh_enabled else "disabled"
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 🔄 Auto refresh {status}")
    
    def toggle_auto_be_all(self, enabled):
        """Toggle auto BE for all orders"""
        self.auto_be_all_enabled = enabled
        
        if self.has_auto_be_enabled():
            if not self.loop_running:
                self.start_loop()
        else:
            if not self.auto_refresh_enabled:
                self.stop_loop()
        
        status = "enabled" if self.auto_be_all_enabled else "disabled"
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 🎯 Auto BE (All orders) {status}")

def test_independent_auto_be():
    """Test that Auto BE works independently of Auto Refresh"""
    print("=== Testing Independent Auto BE Functionality ===")
    print(f"Test started at: {datetime.now().strftime('%H:%M:%S')}")
    print()
    
    # Create mock tab orders
    tab = MockTabOrders()
    
    print("Initial state:")
    print(f"  Auto Refresh: {tab.auto_refresh_enabled}")
    print(f"  Auto BE All: {tab.auto_be_all_enabled}")
    print(f"  Loop Running: {tab.loop_running}")
    print()
    
    # Test 1: Auto BE should start loop even with refresh disabled
    print("Test 1: Auto BE enabled, Auto Refresh disabled")
    tab.toggle_auto_be_all(True)  # This should start the loop
    time.sleep(2)
    print(f"  Loop Running: {tab.loop_running} (should be True)")
    print()
    
    # Test 2: Disable auto refresh - loop should continue for auto BE
    print("Test 2: Disable Auto Refresh (loop should continue)")
    tab.toggle_auto_refresh(False)
    time.sleep(2)
    print(f"  Loop Running: {tab.loop_running} (should be True)")
    print()
    
    # Test 3: Enable auto refresh - both should work
    print("Test 3: Enable Auto Refresh (both functions active)")
    tab.toggle_auto_refresh(True)
    time.sleep(5)  # Let it run for a bit
    print()
    
    # Test 4: Disable auto refresh again - only auto BE should run
    print("Test 4: Disable Auto Refresh again (only Auto BE active)")
    tab.toggle_auto_refresh(False)
    time.sleep(5)  # Let it run for a bit
    print()
    
    # Test 5: Disable auto BE - loop should stop
    print("Test 5: Disable Auto BE (loop should stop)")
    tab.toggle_auto_be_all(False)
    time.sleep(2)
    print(f"  Loop Running: {tab.loop_running} (should be False)")
    print()
    
    print("=== Test Completed ===")
    print("If you see 'Auto BE' processing messages even when 'Auto Refresh' is disabled,")
    print("then the independent functionality is working correctly!")

if __name__ == "__main__":
    test_independent_auto_be()
