import MetaTrader5 as mt5
import time
import subprocess
import psutil

MT5_PATH = "C:\\Program Files\\MetaTrader 5\\terminal64.exe"

def is_mt5_running():
    """Check if MetaTrader 5 is already running"""
    for process in psutil.process_iter(attrs=["name"]):
        if "terminal64.exe" in process.info["name"]:
            return True
    return False

def start_mt5():
    """ Start MT5 in the background if it's not running """
    if not is_mt5_running():
        print("Starting MT5...")
        subprocess.Popen(
            MT5_PATH, 
            shell=True, 
            creationflags=subprocess.CREATE_NO_WINDOW  # Hide MT5 window
        )
        time.sleep(5)  # Wait for MT5 to initialize

def fetch_orders():
    return mt5.positions_get() if mt5.initialize() else []

def update_orders():
    """ Auto update orders based on price conditions """
    start_mt5()  # Ensure MT5 is running
    if not mt5.initialize():
        return

    orders = fetch_orders()
    
    for order in orders:
        entry_price = order.price_open
        current_price = order.price_current
        symbol_info = mt5.symbol_info(order.symbol)
        if not symbol_info:
            continue

        point = symbol_info.point
        new_sl = None

        if order.type == mt5.ORDER_TYPE_BUY and current_price >= entry_price + 5:
            new_sl = entry_price + (10 * point)
        elif order.type == mt5.ORDER_TYPE_SELL and current_price <= entry_price - 5:
            new_sl = entry_price - (10 * point)

        if new_sl:
            request = {"action": mt5.TRADE_ACTION_SLTP, "position": order.ticket, "sl": new_sl}
            mt5.order_send(request)

    mt5.shutdown()

if __name__ == "__main__":
    while True:
        update_orders()
        time.sleep(1)  # Check every second