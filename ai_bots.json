{"bot_1": {"name": "My XAUUSD Bot", "symbol": "XU", "timeframes": ["H1", "H4"], "timeframe": "H1", "bars_back": 100, "prompt": "Look for swing trading opportunities with 1-2 day waiting for order and 1-3 day holding periods. Consider 4H and 1H timeframe confluence. \nI bias Buy Limit on XAUUSD, but if the reason for Sell is enough then you can give Sell Signal, I like using middle of the zone that have more than 3 of 1H & 4H Supply/Demand zone overlapped as entry point, and use Support/Resistant and Trendline, EMA Retest (9) (20) (50) (100) (200), Stochastic (9) Smooth K (3) D (3), RSI (14) to make a decision to order. And pay attention on the economic news today on what direction will it go, based on past result.\nfavorable 1:2–1:3", "api_provider": "gpt", "auto_place_order": 0, "tp_selections": {"tp1": false, "tp2": true, "tp3": false, "tp4": true, "tp5": false}, "schedule_check": 0, "schedule_type": "daily", "schedule_custom_minutes": 60, "schedule_daily_time": "09:00", "schedule_weekly_day": "Monday", "schedule_weekly_time": "09:00", "schedule_monthly_day": 1, "schedule_monthly_time": "09:00", "chart_image_path": "", "use_chart_image": 0, "use_signal_format": 1, "multi_timeframe": true, "created_at": "2025-01-01 00:00:00", "last_used": "2025-08-31 14:22:27", "enabled": true}}