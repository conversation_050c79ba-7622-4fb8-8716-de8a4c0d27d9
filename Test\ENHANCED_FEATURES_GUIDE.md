# Enhanced Features Guide

This guide covers all the new enhanced features implemented in the trading application.

## 🔧 Log Level System

### Overview
Replaced the simple quiet mode with a comprehensive 5-level logging system for better message filtering.

### Log Levels
- **Level 1 - Critical**: System failures, connection errors
- **Level 2 - Error**: Operation failures, validation errors  
- **Level 3 - Warning**: Important notices, configuration changes (Default)
- **Level 4 - Info**: General information, successful operations
- **Level 5 - Debug**: Detailed debugging information

### Usage
1. Go to **Account** tab
2. Select desired log level from dropdown
3. Only messages at or below the selected level will be displayed

### Code Implementation
```python
# In util.py
def add_status_frame(self, text, text_color='white', level=3):
    current_log_level = getattr(self.config, 'log_level', 3)
    if level > current_log_level:
        return  # Skip message if level is too low
```

## 📊 Multi-timeframe Chart Data

### Overview
Enhanced webhook endpoint to support retrieving chart data from multiple timeframes in a single request.

### Single Timeframe (Backward Compatible)
```json
{
    "symbol": "XAUUSD",
    "timeframe": "H1",
    "barback": 100
}
```

### Multi-timeframe Request
```json
{
    "symbol": "XAUUSD", 
    "timeframes": ["M15", "H1", "H4"],
    "barback": 50
}
```

### Response Format
**Single Timeframe:**
```json
{
    "error": false,
    "symbol": "XAUUSD",
    "timeframe": "H1", 
    "bars_count": 100,
    "data": [...]
}
```

**Multi-timeframe:**
```json
{
    "error": false,
    "symbol": "XAUUSD",
    "timeframes": ["M15", "H1", "H4"],
    "total_bars": 150,
    "data": {
        "M15": [...],
        "H1": [...], 
        "H4": [...]
    }
}
```

## 🤖 AI API Integration

### Supported Providers
- **OpenAI GPT**: GPT-4 model for advanced analysis
- **Google Gemini**: Gemini-pro model for comprehensive insights

### Configuration
Each AI bot can be configured with:
- **API Provider**: Choose between GPT or Gemini
- **Custom Prompts**: Tailored analysis instructions
- **Chart Image Upload**: Visual chart analysis support

### API Configuration (config.py)
```python
self.ai_api_config = {
    "gpt": {
        "name": "OpenAI GPT",
        "api_key": "",  # Set via environment variable
        "model": "gpt-4",
        "max_tokens": 1000
    },
    "gemini": {
        "name": "Google Gemini", 
        "api_key": "",  # Set via environment variable
        "model": "gemini-pro",
        "max_tokens": 1000
    }
}
```

## 🎯 Auto Trading Features

### Auto Place Order
- **Purpose**: Automatically place trades based on AI analysis
- **Safety**: Includes risk management and validation
- **Configuration**: Toggle per bot in AI Bots tab

### Schedule Check
- **Purpose**: Periodic automated analysis and trading
- **Interval**: Configurable check intervals (minutes)
- **Control**: Individual bot scheduling

### Bot Configuration
```python
bot_config = {
    "auto_place_order": True,      # Enable auto trading
    "schedule_check": True,        # Enable scheduling  
    "check_interval": 30,          # Check every 30 minutes
    "api_provider": "gpt",         # AI provider
    "use_chart_image": True        # Include chart images
}
```

## 📷 Chart Image Upload

### Overview
Upload chart images for enhanced AI visual analysis alongside technical indicator data.

### Supported Formats
- PNG (.png)
- JPEG (.jpg, .jpeg)
- GIF (.gif)
- BMP (.bmp)

### Usage
1. In AI Bots tab, click "📁 Select Image"
2. Choose chart image file
3. Toggle "Use Image" switch
4. AI analysis will include both technical data and visual chart analysis

## 🏆 Default Symbol Change

### Update
- **Previous Default**: EURUSD (EU)
- **New Default**: XAUUSD (XU) - Gold trading pair
- **Impact**: All new AI bots default to Gold analysis

## 🔄 Enhanced AI Bot Management

### New Bot Features
- **API Provider Selection**: Choose GPT or Gemini
- **Auto Trading Controls**: Enable/disable automatic order placement
- **Scheduling Options**: Set check intervals for automated analysis
- **Image Analysis**: Upload chart images for visual AI analysis
- **Enhanced Prompts**: Customized analysis instructions per bot

### Bot Card Display
Shows comprehensive bot information:
- 🤖 API Provider (GPT/GEMINI)
- 🎯 Auto Order (if enabled)
- ⏰ Schedule interval (if enabled)
- 📷 Image analysis (if enabled)

## 🚀 Getting Started

### 1. Update Log Level
1. Go to Account tab
2. Select appropriate log level (3-Warning recommended)

### 2. Create Enhanced AI Bot
1. Go to AI Bots tab
2. Fill in bot details:
   - Name: "Gold Trading Bot"
   - Symbol: XU (XAUUSD)
   - Timeframe: H1
   - Bars Back: 100
   - AI Provider: GPT or Gemini
   - Enable desired features (Auto Order, Schedule, Image)

### 3. Test Multi-timeframe Data
```bash
curl -X POST http://localhost:5000/webhook_chart_data \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token" \
  -H "X-Access-Token: your_access_token" \
  -d '{
    "symbol": "XAUUSD",
    "timeframes": ["H1", "H4", "D1"],
    "barback": 50
  }'
```

### 4. Configure API Keys
Set environment variables for AI APIs:
```bash
export OPENAI_API_KEY="your_openai_key"
export GEMINI_API_KEY="your_gemini_key"
```

## 📝 Testing

Run the comprehensive test suite:
```bash
python test_enhanced_features.py
```

This tests all new features including log levels, multi-timeframe data, AI bot configuration, and webhook integration.

## 🔧 Troubleshooting

### Common Issues
1. **Log messages not filtering**: Check log level setting in Account tab
2. **Multi-timeframe data empty**: Verify symbol and timeframe validity
3. **AI bot creation fails**: Ensure all required fields are filled
4. **Image upload not working**: Check file format and permissions

### Debug Mode
Set log level to 5 (Debug) to see detailed operation logs for troubleshooting.
