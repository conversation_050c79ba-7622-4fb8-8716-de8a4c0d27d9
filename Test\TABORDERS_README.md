# TabOrders - Order Management Interface

This document explains the new TabOrders functionality that provides comprehensive order management with real-time monitoring and grouped order control.

## Overview

TabOrders is a comprehensive order management interface that displays current open positions and pending orders with automatic refresh capabilities. It features three main subtabs for different views and management functions.

## Features

### 1. **Auto-Refresh System**
- **Configurable Interval**: Set refresh interval in seconds (default: 5 seconds)
- **Toggle Control**: Enable/disable auto-refresh with a switch
- **Manual Refresh**: Force immediate refresh with "Refresh Now" button
- **Real-time Updates**: Continuously monitors MT5 for order changes

### 2. **Three Subtabs Interface**

#### **All Orders Tab**
- **Open Positions Table**: Shows all current open positions with:
  - Symbol, Type (BUY/SELL), Volume, Entry Price, Current Price
  - Stop Loss, Take Profit, Current Profit, Comment
- **Pending Orders Table**: Shows all pending orders with:
  - Symbol, Type (Buy/Sell Limit/Stop), Volume, Entry Price
  - Stop Loss, Take Profit, Comment
- **Scrollable Views**: Both tables support vertical scrolling

#### **SIG Orders Tab**
- **Grouped View**: Groups orders by SIG_ID extracted from comments
- **Comment Pattern**: `SIG_XXX_X_X_<SIG_ID>` (extracts last part as SIG_ID)
- **Group Information**:
  - SIG_ID: The unique identifier
  - TP_Count: Number of TP orders in the group
  - Total_Volume: Combined volume of all orders
  - Total_Profit: Combined profit from positions
  - Actions: Shows count of positions and pending orders
- **Close Functionality**: 
  - Double-click to close entire SIG group
  - Button to close selected SIG group
  - Closes both positions and pending orders ending with SIG_ID

#### **INPUT Orders Tab**
- **Grouped View**: Groups orders by INPUT_ID extracted from comments
- **Comment Pattern**: `INPUT_MANUAL_<INPUT_ID>_TP1` (extracts 8-char INPUT_ID)
- **Group Information**: Same structure as SIG orders
- **Close Functionality**: Same as SIG orders but for INPUT groups

### 3. **Auto-Generated INPUT_ID**

The TabInput now automatically generates unique INPUT_IDs:
- **Format**: 8 characters (lowercase a-z + digits 0-9)
- **Pattern**: `INPUT_<comment>_<INPUT_ID>_TP<number>`
- **Example**: `INPUT_MANUAL_0fddd2a0_TP1`
- **Uniqueness**: Each order group gets a unique ID for tracking

## Usage

### **Starting the Interface**
1. Navigate to the "Orders" tab
2. Auto-refresh starts automatically (5-second interval)
3. All three subtabs are immediately available

### **Monitoring Orders**
1. **All Orders**: View complete order status in real-time
2. **SIG Orders**: Monitor grouped SIG orders and their performance
3. **INPUT Orders**: Track INPUT order groups and profitability

### **Managing Order Groups**

#### **Closing SIG Groups**
```
Method 1: Double-click on any SIG group in the table
Method 2: Select SIG group → Click "Close Selected SIG Group" button
```

#### **Closing INPUT Groups**
```
Method 1: Double-click on any INPUT group in the table  
Method 2: Select INPUT group → Click "Close Selected INPUT Group" button
```

### **Customizing Refresh**
1. **Change Interval**: Modify the seconds value in the control panel
2. **Pause Monitoring**: Toggle the "Enable" switch off
3. **Manual Update**: Click "Refresh Now" for immediate update

## Technical Details

### **Comment Pattern Recognition**

#### **SIG Orders**
- **Pattern**: `SIG_XXX_X_X_<SIG_ID>`
- **Extraction**: Takes the last part after final underscore
- **Example**: `SIG_EURUSD_M15_BUY_abc123` → SIG_ID: `abc123`

#### **INPUT Orders**
- **Pattern**: `INPUT_<prefix>_<INPUT_ID>_TP<number>`
- **Extraction**: Finds 8-character lowercase+digit combination
- **Example**: `INPUT_MANUAL_0fddd2a0_TP1` → INPUT_ID: `0fddd2a0`

### **Group Closing Logic**

#### **SIG Groups**
- Closes all orders where `comment.endswith(sig_id)`
- Handles both open positions and pending orders
- Uses proper MT5 close/cancel requests

#### **INPUT Groups**
- Closes all orders where `input_id in comment` and `comment.startswith('INPUT')`
- Ensures only INPUT orders are affected
- Maintains data integrity

### **Data Refresh Process**
1. **Positions**: Calls `mt5.positions_get()` for open positions
2. **Pending Orders**: Calls `mt5.orders_get()` for pending orders
3. **Grouping**: Processes comments to extract IDs and group data
4. **Display**: Updates all treeview tables with current data
5. **Error Handling**: Graceful handling of MT5 connection issues

## Error Handling

### **Common Scenarios**
- **MT5 Disconnection**: Graceful handling with error messages
- **Invalid Comments**: Skips orders with unrecognized comment patterns
- **Close Failures**: Reports failed close attempts with error codes
- **Empty Selections**: Warns user when no group is selected for closing

### **Status Messages**
- **Success**: Green messages for successful operations
- **Warnings**: Yellow messages for user guidance
- **Errors**: Red messages for failures or issues

## Integration

### **With Existing Systems**
- **TabInput**: Automatically generates compatible INPUT_IDs
- **TabWebhook**: Can trigger order creation with proper comment patterns
- **TabControl**: Complementary order management functionality
- **Utility Functions**: Uses existing MT5 wrapper functions

### **Comment Compatibility**
- **Backward Compatible**: Works with existing comment patterns
- **Forward Compatible**: Extensible for new comment formats
- **Pattern Flexible**: Handles variations in comment structure

## Performance

### **Optimization Features**
- **Threaded Refresh**: Non-blocking UI updates
- **Efficient Grouping**: Fast comment parsing and grouping
- **Minimal MT5 Calls**: Optimized data retrieval
- **Memory Management**: Proper cleanup of treeview data

### **Scalability**
- **Large Order Sets**: Handles hundreds of orders efficiently
- **Multiple Groups**: Supports numerous SIG/INPUT groups
- **Real-time Performance**: Maintains responsiveness during updates

This comprehensive order management system provides traders with powerful tools for monitoring and controlling their trading activities with grouped order management and real-time updates.
