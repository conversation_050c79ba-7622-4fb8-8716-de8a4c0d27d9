#!/usr/bin/env python3
"""
Test script for Google Form Debug
Helps debug why Google Form records aren't appearing from webhook orders
"""

import requests
import urllib.parse

def test_google_form_submission():
    """Test Google Form submission directly"""
    
    print("=" * 80)
    print("GOOGLE FORM SUBMISSION DEBUG TEST")
    print("=" * 80)
    
    # Test data
    test_data = {
        "symbol": "XAUUSD",
        "action": "Buy Now",
        "entry": "2650.00",
        "sl": "2640.00",
        "tp": "2680.00",
        "lot": "0.02",
        "signal_id": "test_webhook_001",
        "comment": "WH_Test",
        "reason": "Debug test from webhook integration",
        "risk": "Test risk assessment for debugging"
    }
    
    print("\n🧪 TEST DATA:")
    print("-" * 80)
    for key, value in test_data.items():
        print(f"• {key}: {value}")
    
    # Original URL template
    url_template = "https://docs.google.com/forms/d/e/1FAIpQLSda4_GifbWv-MGp9j-2jdbtCYzUlDN-chjhprEMHUG4DkkH_g/viewform?usp=pp_url&entry.178506718={Symbol}&entry.645304246={Action}&entry.785671803={Entry}&entry.898028705={SL}&entry.1523790774={TP}&entry.381285031={Lot}&entry.1073635155={SignalID}&entry.1148525053={Comment}&entry.398055293={Reason}&entry.305299530={Risk}"
    
    # Format the URL with test data
    formatted_url = url_template.format(
        Symbol=urllib.parse.quote(test_data["symbol"]),
        Action=urllib.parse.quote(test_data["action"]),
        Entry=urllib.parse.quote(test_data["entry"]),
        SL=urllib.parse.quote(test_data["sl"]),
        TP=urllib.parse.quote(test_data["tp"]),
        Lot=urllib.parse.quote(test_data["lot"]),
        SignalID=urllib.parse.quote(test_data["signal_id"]),
        Comment=urllib.parse.quote(test_data["comment"]),
        Reason=urllib.parse.quote(test_data["reason"]),
        Risk=urllib.parse.quote(test_data["risk"])
    )
    
    print(f"\n🔗 FORMATTED URL:")
    print("-" * 80)
    print(formatted_url)
    
    # Extract form ID
    form_id_start = formatted_url.find('/d/e/') + 5
    form_id_end = formatted_url.find('/viewform')
    form_id = formatted_url[form_id_start:form_id_end]
    
    print(f"\n🆔 EXTRACTED FORM ID:")
    print("-" * 80)
    print(f"Form ID: {form_id}")
    
    # Create submission URL
    submit_url = f"https://docs.google.com/forms/d/e/{form_id}/formResponse"
    print(f"Submit URL: {submit_url}")
    
    # Prepare form data
    form_data = {
        "entry.178506718": test_data["symbol"],
        "entry.645304246": test_data["action"],
        "entry.785671803": test_data["entry"],
        "entry.898028705": test_data["sl"],
        "entry.1523790774": test_data["tp"],
        "entry.381285031": test_data["lot"],
        "entry.1073635155": test_data["signal_id"],
        "entry.1148525053": test_data["comment"],
        "entry.398055293": test_data["reason"],
        "entry.305299530": test_data["risk"]
    }
    
    print(f"\n📝 FORM DATA:")
    print("-" * 80)
    for key, value in form_data.items():
        print(f"• {key}: {value}")
    
    # Test 1: GET request (pre-fill)
    print(f"\n🧪 TEST 1: GET REQUEST (PRE-FILL)")
    print("-" * 80)
    try:
        response = requests.get(formatted_url, timeout=10)
        print(f"Status Code: {response.status_code}")
        print(f"Response Length: {len(response.text)} characters")
        if response.status_code == 200:
            print("✅ GET request successful (form pre-filled)")
            print("⚠️  Note: This only pre-fills the form, doesn't submit data")
        else:
            print("❌ GET request failed")
    except Exception as e:
        print(f"❌ GET request error: {e}")
    
    # Test 2: POST request (submission)
    print(f"\n🧪 TEST 2: POST REQUEST (SUBMISSION)")
    print("-" * 80)
    try:
        response = requests.post(submit_url, data=form_data, timeout=10)
        print(f"Status Code: {response.status_code}")
        print(f"Response Length: {len(response.text)} characters")
        
        if response.status_code == 200:
            print("✅ POST request successful")
            print("✅ Data should appear in Google Sheets")
        else:
            print("❌ POST request failed")
            print(f"Response headers: {dict(response.headers)}")
    except Exception as e:
        print(f"❌ POST request error: {e}")
    
    # Test 3: Alternative submission method
    print(f"\n🧪 TEST 3: ALTERNATIVE SUBMISSION")
    print("-" * 80)
    
    # Try with different headers
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    
    try:
        response = requests.post(submit_url, data=form_data, headers=headers, timeout=10)
        print(f"Status Code: {response.status_code}")
        print(f"Response Length: {len(response.text)} characters")
        
        if response.status_code == 200:
            print("✅ Alternative POST request successful")
        else:
            print("❌ Alternative POST request failed")
    except Exception as e:
        print(f"❌ Alternative POST request error: {e}")

def debug_webhook_flow():
    """Debug the webhook to Google Form flow"""
    
    print("\n" + "=" * 80)
    print("WEBHOOK TO GOOGLE FORM FLOW DEBUG")
    print("=" * 80)
    
    print("\n🔍 DEBUGGING CHECKLIST:")
    print("-" * 80)
    print("1. ✅ Webhook receives request")
    print("2. ✅ Webhook extracts reason and signal_id from JSON")
    print("3. ✅ Webhook calls util.send_order() with all parameters")
    print("4. ❓ send_order() places order successfully")
    print("5. ❓ send_order() calls send_to_google_form() after success")
    print("6. ❓ send_to_google_form() submits data to Google Form")
    print("7. ❓ Data appears in Google Sheets")
    
    print("\n🚨 POTENTIAL ISSUES:")
    print("-" * 80)
    print("Issue 1: Order placement fails")
    print("• Google Form only called after successful order")
    print("• Check MT5 connection and order parameters")
    print("• Look for order errors in status messages")
    print()
    
    print("Issue 2: Google Form submission method")
    print("• Original code used GET request (pre-fill only)")
    print("• Updated code uses POST request (actual submission)")
    print("• Check if POST submission works correctly")
    print()
    
    print("Issue 3: Form field mapping")
    print("• Entry IDs might have changed in Google Form")
    print("• Check if field IDs match current form structure")
    print("• Verify form is accepting submissions")
    print()
    
    print("Issue 4: Network/timeout issues")
    print("• Request timeout (10 seconds)")
    print("• Network connectivity problems")
    print("• Google Form rate limiting")
    
    print("\n🔧 DEBUGGING STEPS:")
    print("-" * 80)
    print("Step 1: Test webhook order placement")
    print("• Send webhook request")
    print("• Check if order appears in MT5")
    print("• Look for success/error messages")
    print()
    
    print("Step 2: Check status messages")
    print("• Look for 'Order data submitted to Google Form' message")
    print("• Look for 'Google Form submission failed' message")
    print("• Check debug logs for detailed errors")
    print()
    
    print("Step 3: Test Google Form directly")
    print("• Run this debug script")
    print("• Check if test data appears in Google Sheets")
    print("• Verify form is working correctly")
    print()
    
    print("Step 4: Verify webhook payload")
    print("• Include 'reason' and 'signal_id' in webhook JSON")
    print("• Check webhook logs for parameter extraction")
    print("• Ensure all required fields are present")

def show_webhook_payload_example():
    """Show correct webhook payload format"""
    
    print("\n" + "=" * 80)
    print("CORRECT WEBHOOK PAYLOAD FORMAT")
    print("=" * 80)
    
    print("\n📡 WEBHOOK REQUEST EXAMPLE:")
    print("-" * 80)
    print("POST /webhook_instant")
    print("Content-Type: application/json")
    print("Authorization: Bearer YOUR_ACCESS_TOKEN")
    print("X-Access-Token: YOUR_ACCESS_TOKEN")
    print()
    print("{")
    print("  \"s\": \"XAUUSD\",")
    print("  \"a\": \"Buy Now\",")
    print("  \"lot\": 0.02,")
    print("  \"ptp\": 200,")
    print("  \"psl\": 100,")
    print("  \"c\": \"WH_Test\",")
    print("  \"reason\": \"Debug test: RSI oversold + MACD bullish crossover\",")
    print("  \"signal_id\": \"webhook_debug_001\"")
    print("}")
    
    print("\n🔍 PARAMETER MAPPING:")
    print("-" * 80)
    print("• s → symbol (XAUUSD)")
    print("• a → action (Buy Now)")
    print("• lot → lot size (0.02)")
    print("• ptp → TP in points (200)")
    print("• psl → SL in points (100)")
    print("• c → comment (WH_Test)")
    print("• reason → trade reason (for Google Form)")
    print("• signal_id → signal identifier (for Google Form)")
    
    print("\n✅ EXPECTED FLOW:")
    print("-" * 80)
    print("1. Webhook receives JSON payload")
    print("2. Extracts all parameters including reason and signal_id")
    print("3. Calculates entry, SL, TP prices")
    print("4. Calls util.send_order() with all parameters")
    print("5. send_order() places MT5 order")
    print("6. If successful, calls send_to_google_form()")
    print("7. send_to_google_form() submits data to Google Sheets")
    print("8. Returns success response")
    
    print("\n🎯 TROUBLESHOOTING TIPS:")
    print("-" * 80)
    print("• Check MT5 connection and login")
    print("• Verify symbol is available and market is open")
    print("• Ensure lot size and prices are valid")
    print("• Check webhook authentication tokens")
    print("• Monitor status messages for errors")
    print("• Test with simple payload first")
    print("• Verify Google Form URL and field IDs")

if __name__ == "__main__":
    test_google_form_submission()
    debug_webhook_flow()
    show_webhook_payload_example()
