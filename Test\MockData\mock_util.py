import os
import pandas as pd
import numpy as np 
# TF = "M30" 
# โหลดไฟล์ CSV (ใน path เดียวกับไฟล์ Python)
# current_dir = os.path.dirname(os.path.abspath(__file__))
# df = pd.read_csv(os.path.join(current_dir, "30m.csv"))
# df['time'] = pd.to_datetime(df['time'])

# สร้าง mock data
def generate_mock_data(startTime, periods, freq, seed=42):
    np.random.seed(seed)
    timestamps = pd.date_range(start=startTime, periods=periods, freq=freq)

    opens = []
    closes = []

    # เริ่มต้นที่ราคา 3000
    open_price = 3000
    for _ in range(periods):
        # ราคาปิดแกว่งเบาๆ ประมาณ -1 ถึง +1
        change = np.random.uniform(-1.0, 1.0)
        close_price = open_price + change

        opens.append(open_price)
        closes.append(close_price)

        # ราคาถัดไปเริ่มที่ราคาปิดแท่งก่อนหน้า
        open_price = close_price

    opens = np.array(opens)
    closes = np.array(closes)
    highs = np.maximum(opens, closes) + np.abs(np.random.uniform(0.2, 0.8, periods))
    lows = np.minimum(opens, closes) - np.abs(np.random.uniform(0.2, 0.8, periods))

    df = pd.DataFrame({
        'time': timestamps,
        'open': opens.round(2),
        'high': highs.round(2),
        'low': lows.round(2),
        'close': closes.round(2)
    })

    return df

def get_from_csv(filename):
    current_dir = os.path.dirname(os.path.abspath(__file__))
    df = pd.read_csv(os.path.join(current_dir, filename))
    df['time'] = pd.to_datetime(df['time'], format="%Y-%m-%d %H:%M:%S")
    df = df[['time', 'open', 'high', 'low', 'close']]
    return df
 
def simulate_trades(df, tp, sl, isAllowOverlap=False):
    TP_point = tp
    SL_point = sl

    trades = []  # เก็บผลลัพธ์ของแต่ละเทรด

    position = None
    entry_price = None
    entry_index = None

    for idx in range(len(df)):
        row = df.iloc[idx]

        if position is None or isAllowOverlap:
            if row['longRetest']:
                position = 'long'
                entry_price = row['close']
                entry_index = idx
            elif row['shortRetest']:
                position = 'short'
                entry_price = row['close']
                entry_index = idx

        if position is not None:
            if position == 'long':
                if df.iloc[idx]['high'] - entry_price >= TP_point:
                    profit = TP_point
                    trades.append({'Type': 'Long', 'Result': 'Win', 'Profit': profit, 'EntryIPrc': entry_price, 'EntryIdx': entry_index, 'ExitPrc': row['close'], 'ExitIdx': idx})
                    position = None
                    entry_price = None
                elif entry_price - df.iloc[idx]['low'] >= SL_point:
                    loss = -SL_point
                    trades.append({'Type': 'Long', 'Result': 'Loss', 'Profit': loss, 'EntryIPrc': entry_price, 'EntryIdx': entry_index, 'ExitPrc': row['close'], 'ExitIdx': idx})
                    position = None
                    entry_price = None

            elif position == 'short':
                if entry_price - df.iloc[idx]['low'] >= TP_point:
                    profit = TP_point
                    trades.append({'Type': 'Short', 'Result': 'Win', 'Profit': profit, 'EntryIPrc': entry_price, 'EntryIdx': entry_index, 'ExitPrc': row['close'], 'ExitIdx': idx})
                    position = None
                    entry_price = None
                elif df.iloc[idx]['high'] - entry_price >= SL_point:
                    loss = -SL_point
                    trades.append({'Type': 'Short', 'Result': 'Loss', 'Profit': loss, 'EntryIPrc': entry_price, 'EntryIdx': entry_index, 'ExitPrc': row['close'], 'ExitIdx': idx})
                    position = None
                    entry_price = None
    if trades:
        trades_df = pd.DataFrame(trades)
        summary1 = trades_df.groupby(['Type', 'Result']).agg(
            TradeCount=('Profit', 'count'),
            TotalProfit=('Profit', 'sum'),
            AvgProfit=('Profit', 'mean')
        ).reset_index()

        summary2 = trades_df.groupby(['Type', 'Result']).size().unstack(fill_value=0)

        # เพิ่มคำนวณ Win rate
        summary2['Total'] = summary2.sum(axis=1)
        summary2['WinRate(%)'] = (summary2.get('Win', 0) / summary2['Total']) * 100

    else:
        trades_df = pd.DataFrame()
        summary1 = pd.DataFrame()
        summary2 = pd.DataFrame()
        
    # --- Usage ---
    # trades_df, summary1, summary2 = _util.simulate_trades(df)
    # print(trades_df)
    # print(summary1)
    print(trades_df)
    print(summary1)
    print(summary2)
    return trades_df, summary1, summary2

# app = ctk.CTk()
# app.geometry("900x600")
# app.title("Trading Signal Dashboard")
# chart_frame = ctk.CTkFrame(app)
# chart_frame.pack(pady=20, fill="both", expand=True)

# canvas = FigureCanvasTkAgg(fig, master=chart_frame)
# canvas.draw()
# canvas_widget = canvas.get_tk_widget()
# canvas_widget.pack(fill="both", expand=True)

# toolbar = NavigationToolbar2Tk(canvas, chart_frame)
# toolbar.update()
# toolbar.pack(side="top", fill="x")
