from dotenv import load_dotenv
import MetaTrader5 as mt5

# ===========================
# Class: Config
# ===========================
class Config:
    def __init__(self):
        load_dotenv()
        self.app_name = "My App"
        self.app_key = "myapp"

        self.account_default = "DEMO2 CFX Std"
        # self.account_default = "REAL 2 CFX"
        self.accounts = {
            "DEMO1 IUX Std":"DEMO1",
            "DEMO2 CFX Std":"DEMO2",
            "DEMO3 CFX Ult":"DEMO3",
            "REAL1 XMG Std":"REAL1",
            "REAL2 CFX Std":"REAL2",
        }
        self.symbol_posfix = None
        self.symbol_default = "XU"
        self.symbols = {
            "XU":"XAUUSD", 
            "BU":"BTCUSD", 
            "AU":"AUDUSD", 
            "AJ":"AUDJPY", 
            "ACD":"AUDCAD", 
            "ACF":"AUDCHF", 
            "EU":"EURUSD", 
            "GN":"GBPNZD",
            "GU":"GBPUSD",
            "UCD":"USDCAD",
            "UCF":"USDCHF",
            "UJ":"USDJPY", 
            "NCF":"NZDCHF", 
            "NJ":"NZDJPY", 
            "NU":"NZDUSD", 
        }
        self.order_type_mapping = {
            "Buy Now": mt5.ORDER_TYPE_BUY,
            "Sell Now": mt5.ORDER_TYPE_SELL,
            "Buy Limit": mt5.ORDER_TYPE_BUY_LIMIT,
            "Sell Limit": mt5.ORDER_TYPE_SELL_LIMIT,
            "Buy Stop": mt5.ORDER_TYPE_BUY_STOP,
            "Sell Stop": mt5.ORDER_TYPE_SELL_STOP,
        }
        self.action_type_mapping = {
            "Buy Now": mt5.TRADE_ACTION_DEAL,
            "Sell Now": mt5.TRADE_ACTION_DEAL,
            "Buy Limit": mt5.TRADE_ACTION_PENDING,
            "Sell Limit": mt5.TRADE_ACTION_PENDING,
            "Buy Stop": mt5.TRADE_ACTION_PENDING,
            "Sell Stop": mt5.TRADE_ACTION_PENDING,
        }
        
        self.timeframes = {
            "M5": mt5.TIMEFRAME_M5,
            "M15": mt5.TIMEFRAME_M15,
            "M30": mt5.TIMEFRAME_M30,
            "H1": mt5.TIMEFRAME_H1,
            "H4": mt5.TIMEFRAME_H4,
            # "D1": mt5.TIMEFRAME_D1
        }

        # ไปที่: https://notify-bot.line.me/my/
        # เลือก "Generate token" → ตั้งชื่อ → เลือกกลุ่มที่จะให้แจ้ง
        # คัดลอก Token → เอามาใส่ในโค้ดตรง LINE_TOKEN
        self.LINE_TOKEN = ''

        self.MAX_TP_FIELDS = 5
        self.MAX_ORDERS = 1

        self.RSI1_LEN = 25
        self.SMA1_LEN = 50

        self.RSI2_LEN = 50
        self.SMA2_LEN = 25

        self.RSI3_LEN = 25
        self.SMA3_LEN = 25
         
        self.BE_POINTS = 500
        self.SL_POINTS = 500
        self.TP1_POINTS = 1000
        self.TP2_POINTS = 2000
        self.TP3_POINTS = 3000
        self.status_label_frame = None
        # self.timer_label = None
        self.status_label = None
        self.status_scroll_frame = None
        self.status_scroll_labels = []
