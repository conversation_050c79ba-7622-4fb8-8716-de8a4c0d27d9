
20:00:54 : 📤 Order send result: OrderSendResult(retcode=10009, deal=0, order=319900213, volume=0.1, price=0.0, bid=0.0, ask=0.0, comment='Request executed', request_id=1501763677, retcode_external=0, request=TradeRequest(action=5, magic=161032, order=0, symbol='XAUUSD.iux', volume=0.1, price=3273.0, stoplimit=0.0, sl=3267.0, tp=3288.0, deviation=10, type=2, type_filling=0, type_time=0, expiration=0, comment='ZD_Hum_3_d6243830', position=0, position_by=0))
20:00:54 : 🟢 Sending Buy Limit: XAUUSD.iux lot 0.1 @ 3273, SL 3267, TP 3288.0 - ZD_Hum_3_d6243830
20:00:54 : ✅ Webhook Input: 3/3 orders sent with ID: d6243830
Exception in Tkinter callback
Traceback (most recent call last):
  File "C:\Program Files\Python310\lib\tkinter\__init__.py", line 1921, in __call__
    return self.func(*args)
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\core_widget_classes\ctk_base_class.py", line 188, in _update_dimensions_event
    self._draw(no_color_updates=True)  # faster drawing without color changes
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\ctk_label.py", line 161, in _draw
    requires_recoloring = self._draw_engine.draw_rounded_rect_with_border(self._apply_widget_scaling(self._current_width),
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\core_rendering\draw_engine.py", line 128, in draw_rounded_rect_with_border
    return self.__draw_rounded_rect_with_border_font_shapes(width, height, corner_radius, border_width, inner_corner_radius, ())
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\core_rendering\draw_engine.py", line 248, in __draw_rounded_rect_with_border_font_shapes
    self._canvas.delete("border_parts")
  File "C:\Program Files\Python310\lib\tkinter\__init__.py", line 2852, in delete
    self.tk.call((self._w, 'delete') + args)
_tkinter.TclError: invalid command name ".!ctkframe2.!canvas.!ctkscrollableframe.!ctklabel12.!ctkcanvas"
Exception in Tkinter callback
Traceback (most recent call last):
  File "C:\Program Files\Python310\lib\tkinter\__init__.py", line 1921, in __call__
    return self.func(*args)
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\core_widget_classes\ctk_base_class.py", line 188, in _update_dimensions_event
    self._draw(no_color_updates=True)  # faster drawing without color changes
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\ctk_label.py", line 161, in _draw
    requires_recoloring = self._draw_engine.draw_rounded_rect_with_border(self._apply_widget_scaling(self._current_width),
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\core_rendering\draw_engine.py", line 128, in draw_rounded_rect_with_border
    return self.__draw_rounded_rect_with_border_font_shapes(width, height, corner_radius, border_width, inner_corner_radius, ())
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\core_rendering\draw_engine.py", line 248, in __draw_rounded_rect_with_border_font_shapes
    self._canvas.delete("border_parts")
  File "C:\Program Files\Python310\lib\tkinter\__init__.py", line 2852, in delete
    self.tk.call((self._w, 'delete') + args)
_tkinter.TclError: invalid command name ".!ctkframe2.!canvas.!ctkscrollableframe.!ctklabel18.!ctkcanvas"
20:16:26 : 🧹 Memory cleanup: Removed 23 old status labels
03:55:05 : ✅ Webhook Instant: s:XAUUSD a:Buy Now c:WH_ER2_RSMA_E3 :: {'a': 'Buy Now', 'c': 'WH_ER2_RSMA_E3', 'p': 3313.86, 'stp': 11, 'xtp': 3, 'tp': 3346.86, 'sl': 3302.86, 's': 'XAUUSD'}
03:55:05 : 🔄 Preparing order: Buy Now XAUUSD.iux 0.02 lots
03:55:05 : 📊 Price info: bid=3314.02, ask=3314.16, using=3314.02
03:55:05 : 📋 Order request prepared: {'action': 1, 'symbol': 'XAUUSD.iux', 'volume': 0.02, 'type': 0, 'price': 3314.02, 'sl': 3302.86, 'tp': 3346.86, 'deviation': 10, 'magic': 161032, 'comment': 'WH_ER2_RSMA_E3', 'type_filling': 0}
03:55:05 : 📤 Sending order request...
03:55:05 : 📤 Order send result: OrderSendResult(retcode=10009, deal=119456172, order=320061196, volume=0.02, price=3314.16, bid=3314.02, ask=3314.16, comment='Request executed', request_id=1501763678, retcode_external=0, request=TradeRequest(action=1, magic=161032, order=0, symbol='XAUUSD.iux', volume=0.02, price=3314.02, stoplimit=0.0, sl=3302.86, tp=3346.86, deviation=10, type=0, type_filling=0, type_time=0, expiration=0, comment='WH_ER2_RSMA_E3', position=0, position_by=0))
03:55:05 : 🟢 Sending Buy Now: XAUUSD.iux lot 0.02 @ 3314.16, SL 3302.86, TP 3346.86 - WH_ER2_RSMA_E3
03:55:05 : ✅ Webhook Instant: s:XAUUSD a:Buy Now c:WH_ER2_MS_E3 :: {'a': 'Buy Now', 'c': 'WH_ER2_MS_E3', 'p': 3313.86, 'stp': 11, 'xtp': 3, 'tp': 3346.86, 'sl': 3302.86, 's': 'XAUUSD'}
03:55:05 : 🔄 Preparing order: Buy Now XAUUSD.iux 0.02 lots
03:55:05 : 📊 Price info: bid=3314.02, ask=3314.16, using=3314.02
03:55:05 : 📋 Order request prepared: {'action': 1, 'symbol': 'XAUUSD.iux', 'volume': 0.02, 'type': 0, 'price': 3314.02, 'sl': 3302.86, 'tp': 3346.86, 'deviation': 10, 'magic': 161032, 'comment': 'WH_ER2_MS_E3', 'type_filling': 0}
03:55:05 : 📤 Sending order request...
03:55:05 : 📤 Order send result: OrderSendResult(retcode=10009, deal=119456173, order=320061197, volume=0.02, price=3314.16, bid=3314.02, ask=3314.16, comment='Request executed', request_id=1501763679, retcode_external=0, request=TradeRequest(action=1, magic=161032, order=0, symbol='XAUUSD.iux', volume=0.02, price=3314.02, stoplimit=0.0, sl=3302.86, tp=3346.86, deviation=10, type=0, type_filling=0, type_time=0, expiration=0, comment='WH_ER2_MS_E3', position=0, position_by=0))
03:55:05 : 🟢 Sending Buy Now: XAUUSD.iux lot 0.02 @ 3314.16, SL 3302.86, TP 3346.86 - WH_ER2_MS_E3
Exception in Tkinter callback
Traceback (most recent call last):
  File "C:\Program Files\Python310\lib\tkinter\__init__.py", line 1921, in __call__
    return self.func(*args)
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\core_widget_classes\ctk_base_class.py", line 188, in _update_dimensions_event
    self._draw(no_color_updates=True)  # faster drawing without color changes
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\ctk_label.py", line 161, in _draw
    requires_recoloring = self._draw_engine.draw_rounded_rect_with_border(self._apply_widget_scaling(self._current_width),
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\core_rendering\draw_engine.py", line 128, in draw_rounded_rect_with_border
    return self.__draw_rounded_rect_with_border_font_shapes(width, height, corner_radius, border_width, inner_corner_radius, ())
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\core_rendering\draw_engine.py", line 248, in __draw_rounded_rect_with_border_font_shapes
    self._canvas.delete("border_parts")
  File "C:\Program Files\Python310\lib\tkinter\__init__.py", line 2852, in delete
    self.tk.call((self._w, 'delete') + args)
_tkinter.TclError: invalid command name ".!ctkframe2.!canvas.!ctkscrollableframe.!ctklabel26.!ctkcanvas"
Exception in Tkinter callback
Traceback (most recent call last):
  File "C:\Program Files\Python310\lib\tkinter\__init__.py", line 1921, in __call__
    return self.func(*args)
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\core_widget_classes\ctk_base_class.py", line 188, in _update_dimensions_event
    self._draw(no_color_updates=True)  # faster drawing without color changes
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\ctk_label.py", line 161, in _draw
    requires_recoloring = self._draw_engine.draw_rounded_rect_with_border(self._apply_widget_scaling(self._current_width),
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\core_rendering\draw_engine.py", line 128, in draw_rounded_rect_with_border
    return self.__draw_rounded_rect_with_border_font_shapes(width, height, corner_radius, border_width, inner_corner_radius, ())
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\core_rendering\draw_engine.py", line 248, in __draw_rounded_rect_with_border_font_shapes
    self._canvas.delete("border_parts")
  File "C:\Program Files\Python310\lib\tkinter\__init__.py", line 2852, in delete
    self.tk.call((self._w, 'delete') + args)
_tkinter.TclError: invalid command name ".!ctkframe2.!canvas.!ctkscrollableframe.!ctklabel29.!ctkcanvas"
Exception in Tkinter callback
Traceback (most recent call last):
  File "C:\Program Files\Python310\lib\tkinter\__init__.py", line 1921, in __call__
    return self.func(*args)
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\core_widget_classes\ctk_base_class.py", line 188, in _update_dimensions_event
    self._draw(no_color_updates=True)  # faster drawing without color changes
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\ctk_label.py", line 161, in _draw
    requires_recoloring = self._draw_engine.draw_rounded_rect_with_border(self._apply_widget_scaling(self._current_width),
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\core_rendering\draw_engine.py", line 128, in draw_rounded_rect_with_border
    return self.__draw_rounded_rect_with_border_font_shapes(width, height, corner_radius, border_width, inner_corner_radius, ())
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\core_rendering\draw_engine.py", line 248, in __draw_rounded_rect_with_border_font_shapes
    self._canvas.delete("border_parts")
  File "C:\Program Files\Python310\lib\tkinter\__init__.py", line 2852, in delete
    self.tk.call((self._w, 'delete') + args)
_tkinter.TclError: invalid command name ".!ctkframe2.!canvas.!ctkscrollableframe.!ctklabel31.!ctkcanvas"
Exception in Tkinter callback
Traceback (most recent call last):
  File "C:\Program Files\Python310\lib\tkinter\__init__.py", line 1921, in __call__
    return self.func(*args)
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\core_widget_classes\ctk_base_class.py", line 188, in _update_dimensions_event
    self._draw(no_color_updates=True)  # faster drawing without color changes
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\ctk_label.py", line 161, in _draw
    requires_recoloring = self._draw_engine.draw_rounded_rect_with_border(self._apply_widget_scaling(self._current_width),
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\core_rendering\draw_engine.py", line 128, in draw_rounded_rect_with_border
    return self.__draw_rounded_rect_with_border_font_shapes(width, height, corner_radius, border_width, inner_corner_radius, ())
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\core_rendering\draw_engine.py", line 248, in __draw_rounded_rect_with_border_font_shapes
    self._canvas.delete("border_parts")
  File "C:\Program Files\Python310\lib\tkinter\__init__.py", line 2852, in delete
    self.tk.call((self._w, 'delete') + args)
_tkinter.TclError: invalid command name ".!ctkframe2.!canvas.!ctkscrollableframe.!ctklabel34.!ctkcanvas"
Exception in Tkinter callback
Traceback (most recent call last):
  File "C:\Program Files\Python310\lib\tkinter\__init__.py", line 1921, in __call__
    return self.func(*args)
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\core_widget_classes\ctk_base_class.py", line 188, in _update_dimensions_event
    self._draw(no_color_updates=True)  # faster drawing without color changes
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\ctk_label.py", line 161, in _draw
    requires_recoloring = self._draw_engine.draw_rounded_rect_with_border(self._apply_widget_scaling(self._current_width),
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\core_rendering\draw_engine.py", line 128, in draw_rounded_rect_with_border
    return self.__draw_rounded_rect_with_border_font_shapes(width, height, corner_radius, border_width, inner_corner_radius, ())
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\core_rendering\draw_engine.py", line 248, in __draw_rounded_rect_with_border_font_shapes
    self._canvas.delete("border_parts")
  File "C:\Program Files\Python310\lib\tkinter\__init__.py", line 2852, in delete
    self.tk.call((self._w, 'delete') + args)
_tkinter.TclError: invalid command name ".!ctkframe2.!canvas.!ctkscrollableframe.!ctklabel35.!ctkcanvas"
04:11:46 : 🧹 Memory cleanup: Removed 15 old status labels
08:29:01 : 📦 Set SL to BE: position 320061196 SL 3302.86 >> 3314.2599999999998
08:29:02 : 📦 Set SL to BE: position 320061197 SL 3302.86 >> 3314.2599999999998
12:15:09 : ✅ Webhook Instant: s:XAUUSD a:Buy Now c:WH_HO1_RSMA_1H_OB :: {'a': 'Buy Now', 'c': 'WH_HO1_RSMA_1H_OB', 'p': 3324.11, 'stp': 21, 'xtp': 3, 'tp': 3387.11, 'sl': 3303.11, 's': 'XAUUSD'}
12:15:09 : 🔄 Preparing order: Buy Now XAUUSD.iux 0.02 lots
12:15:09 : 📊 Price info: bid=3324.2, ask=3324.34, using=3324.2
12:15:09 : 📋 Order request prepared: {'action': 1, 'symbol': 'XAUUSD.iux', 'volume': 0.02, 'type': 0, 'price': 3324.2, 'sl': 3303.11, 'tp': 3387.11, 'deviation': 10, 'magic': 161032, 'comment': 'WH_HO1_RSMA_1H_OB', 'type_filling': 0}
12:15:09 : 📤 Sending order request...
12:15:09 : 📤 Order send result: OrderSendResult(retcode=10009, deal=119544228, order=320167758, volume=0.02, price=3324.34, bid=3324.2, ask=3324.34, comment='Request executed', request_id=1501763682, retcode_external=0, request=TradeRequest(action=1, magic=161032, order=0, symbol='XAUUSD.iux', volume=0.02, price=3324.2, stoplimit=0.0, sl=3303.11, tp=3387.11, deviation=10, type=0, type_filling=0, type_time=0, expiration=0, comment='WH_HO1_RSMA_1H_OB', position=0, position_by=0))
12:15:09 : 🟢 Sending Buy Now: XAUUSD.iux lot 0.02 @ 3324.34, SL 3303.11, TP 3387.11 - WH_HO1_RSMA_1H_OB
07:39:21 : 📦 Set SL to BE: position 320167758 SL 3303.11 >> 3324.44
Exception in Tkinter callback
Traceback (most recent call last):
  File "C:\Program Files\Python310\lib\tkinter\__init__.py", line 1921, in __call__
    return self.func(*args)
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\core_widget_classes\ctk_base_class.py", line 188, in _update_dimensions_event
    self._draw(no_color_updates=True)  # faster drawing without color changes
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\ctk_label.py", line 161, in _draw
    requires_recoloring = self._draw_engine.draw_rounded_rect_with_border(self._apply_widget_scaling(self._current_width),
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\core_rendering\draw_engine.py", line 128, in draw_rounded_rect_with_border
    return self.__draw_rounded_rect_with_border_font_shapes(width, height, corner_radius, border_width, inner_corner_radius, ())
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\core_rendering\draw_engine.py", line 248, in __draw_rounded_rect_with_border_font_shapes
    self._canvas.delete("border_parts")
  File "C:\Program Files\Python310\lib\tkinter\__init__.py", line 2852, in delete
    self.tk.call((self._w, 'delete') + args)
_tkinter.TclError: invalid command name ".!ctkframe2.!canvas.!ctkscrollableframe.!ctklabel42.!ctkcanvas"
07:43:22 : 🧹 Memory cleanup: Removed 11 old status labels
Exception in Tkinter callback
Traceback (most recent call last):
  File "C:\Program Files\Python310\lib\tkinter\__init__.py", line 1921, in __call__
    return self.func(*args)
  File "C:\Program Files\Python310\lib\tkinter\__init__.py", line 839, in callit
    func(*args)
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\scaling\scaling_tracker.py", line 187, in check_dpi_scaling
    cls.update_scaling_callbacks_for_window(window)
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\scaling\scaling_tracker.py", line 64, in update_scaling_callbacks_for_window
    set_scaling_callback(cls.window_dpi_scaling_dict[window] * cls.widget_scaling,
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\ctk_label.py", line 108, in _set_scaling
    super()._set_scaling(*args, **kwargs)
  File "C:\inetpub\wwwroot\python\venv\lib\site-packages\customtkinter\windows\widgets\core_widget_classes\ctk_base_class.py", line 232, in _set_scaling
    self._last_geometry_manager_call["function"](**self._apply_argument_scaling(self._last_geometry_manager_call["kwargs"]))
  File "C:\Program Files\Python310\lib\tkinter\__init__.py", line 2425, in pack_configure
    self.tk.call(
_tkinter.TclError: bad window path name ".!ctkframe2.!canvas.!ctkscrollableframe.!ctklabel49"