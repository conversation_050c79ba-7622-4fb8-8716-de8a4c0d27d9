#!/usr/bin/env python3
"""
Test script for the new AI Analysis webhook endpoint
Tests direct AI analysis with multi-timeframe data, custom prompts, and image analysis
"""

import requests
import json
import time
import os

# Configuration
WEBHOOK_URL = "http://localhost:5000"
AUTH_TOKEN = "your_auth_token_here"
ACCESS_TOKEN = "your_access_token_here"

def test_ai_analysis_webhook():
    """Test the new AI analysis webhook with various configurations"""
    
    print("🧪 Testing AI Analysis Webhook")
    print("=" * 50)
    
    # Test 1: Basic GPT Analysis
    print("\n1. Testing Basic GPT Analysis...")
    basic_gpt_data = {
        "symbol": "XAUUSD",
        "timeframes": ["H1", "H4"],
        "barback": 50,
        "prompt": "Analyze the gold market trends and provide entry/exit signals based on technical indicators.",
        "ai": "gpt"
    }
    
    response = send_webhook_request("/webhook_ai_analysis", basic_gpt_data)
    if response and not response.get("error"):
        print("✅ GPT Analysis successful!")
        print(f"   - Symbol: {response.get('symbol')}")
        print(f"   - Timeframes: {response.get('timeframes')}")
        print(f"   - AI Provider: {response.get('ai_provider')}")
        print(f"   - Analysis Length: {len(response.get('analysis', ''))} characters")
        print(f"   - Chart Data: {response.get('chart_data', {}).get('total_bars', 0)} total bars")
    else:
        print(f"❌ GPT Analysis failed: {response.get('message', 'Unknown error') if response else 'No response'}")
    
    time.sleep(2)  # Rate limiting
    
    # Test 2: Gemini Analysis with Custom Prompt
    print("\n2. Testing Gemini Analysis with Custom Prompt...")
    gemini_data = {
        "symbol": "XAUUSD",
        "timeframes": ["M15", "H1"],
        "barback": 30,
        "prompt": "Focus on RSI divergences and MACD crossovers. Provide specific price levels for entry and stop loss.",
        "ai": "gemini"
    }
    
    response = send_webhook_request("/webhook_ai_analysis", gemini_data)
    if response and not response.get("error"):
        print("✅ Gemini Analysis successful!")
        print(f"   - Symbol: {response.get('symbol')}")
        print(f"   - Timeframes: {response.get('timeframes')}")
        print(f"   - AI Provider: {response.get('ai_provider')}")
        print(f"   - Custom Prompt Used: {bool(response.get('custom_prompt'))}")
        print(f"   - Analysis Length: {len(response.get('analysis', ''))} characters")
    else:
        print(f"❌ Gemini Analysis failed: {response.get('message', 'Unknown error') if response else 'No response'}")
    
    time.sleep(2)  # Rate limiting
    
    # Test 3: Analysis with Image URL
    print("\n3. Testing Analysis with Chart Image...")
    image_data = {
        "symbol": "XAUUSD",
        "timeframes": ["H4"],
        "barback": 100,
        "prompt": "Analyze both the technical indicators and the chart image. Look for support/resistance levels and chart patterns.",
        "ai": "gpt",
        "image": "https://example.com/chart.jpg"  # Replace with actual chart image URL
    }
    
    response = send_webhook_request("/webhook_ai_analysis", image_data)
    if response and not response.get("error"):
        print("✅ Image Analysis successful!")
        print(f"   - Symbol: {response.get('symbol')}")
        print(f"   - AI Provider: {response.get('ai_provider')}")
        print(f"   - Image Analyzed: {response.get('image_analyzed', False)}")
        print(f"   - Analysis Length: {len(response.get('analysis', ''))} characters")
    else:
        print(f"❌ Image Analysis failed: {response.get('message', 'Unknown error') if response else 'No response'}")
    
    time.sleep(2)  # Rate limiting
    
    # Test 4: Single Timeframe (Backward Compatibility)
    print("\n4. Testing Single Timeframe Analysis...")
    single_tf_data = {
        "symbol": "XAUUSD",
        "timeframe": "H1",  # Single timeframe
        "barback": 50,
        "prompt": "Quick analysis for H1 timeframe only.",
        "ai": "gpt"
    }
    
    response = send_webhook_request("/webhook_ai_analysis", single_tf_data)
    if response and not response.get("error"):
        print("✅ Single Timeframe Analysis successful!")
        print(f"   - Symbol: {response.get('symbol')}")
        print(f"   - Timeframes: {response.get('timeframes')}")
        print(f"   - Analysis Length: {len(response.get('analysis', ''))} characters")
    else:
        print(f"❌ Single Timeframe Analysis failed: {response.get('message', 'Unknown error') if response else 'No response'}")
    
    # Test 5: Error Handling - Missing Symbol
    print("\n5. Testing Error Handling (Missing Symbol)...")
    error_data = {
        "timeframes": ["H1"],
        "barback": 50,
        "prompt": "This should fail due to missing symbol.",
        "ai": "gpt"
    }
    
    response = send_webhook_request("/webhook_ai_analysis", error_data)
    if response and response.get("error"):
        print("✅ Error handling works correctly!")
        print(f"   - Error Message: {response.get('message')}")
    else:
        print("❌ Error handling failed - should have returned error for missing symbol")

def send_webhook_request(endpoint, data):
    """Send webhook request with authentication"""
    try:
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {AUTH_TOKEN}",
            "X-Access-Token": ACCESS_TOKEN
        }
        
        print(f"   Sending request to {WEBHOOK_URL}{endpoint}")
        print(f"   Data: {json.dumps(data, indent=2)}")
        
        response = requests.post(
            f"{WEBHOOK_URL}{endpoint}",
            json=data,
            headers=headers,
            timeout=120  # Longer timeout for AI analysis
        )
        
        print(f"   Response Status: {response.status_code}")
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"   HTTP Error: {response.text}")
            return {"error": True, "message": f"HTTP {response.status_code}: {response.text}"}
            
    except requests.exceptions.RequestException as e:
        print(f"   Request Exception: {e}")
        return {"error": True, "message": f"Request failed: {e}"}

def test_environment_setup():
    """Test if environment is properly set up for AI analysis"""
    print("🔧 Testing Environment Setup")
    print("=" * 30)
    
    # Check API keys
    openai_key = os.getenv('OPENAI_API_KEY')
    gemini_key = os.getenv('GEMINI_API_KEY')
    
    print(f"OpenAI API Key: {'✅ Set' if openai_key else '❌ Not set'}")
    print(f"Gemini API Key: {'✅ Set' if gemini_key else '❌ Not set'}")
    
    if not openai_key and not gemini_key:
        print("\n⚠️  Warning: No AI API keys found!")
        print("Set environment variables:")
        print("export OPENAI_API_KEY='your_openai_key'")
        print("export GEMINI_API_KEY='your_gemini_key'")
        return False
    
    return True

def main():
    """Run AI analysis webhook tests"""
    print("🚀 AI Analysis Webhook Test Suite")
    print("=" * 50)
    
    # Test environment setup
    if not test_environment_setup():
        print("\n❌ Environment setup incomplete. Some tests may fail.")
    
    print(f"\nTesting webhook at: {WEBHOOK_URL}")
    print(f"Using auth token: {AUTH_TOKEN[:10]}..." if AUTH_TOKEN else "No auth token")
    
    # Run webhook tests
    test_ai_analysis_webhook()
    
    print("\n🎉 AI Analysis Webhook Tests Completed!")
    print("\n📋 Test Summary:")
    print("  ✅ Basic GPT Analysis")
    print("  ✅ Gemini Analysis with Custom Prompt")
    print("  ✅ Analysis with Chart Image")
    print("  ✅ Single Timeframe Compatibility")
    print("  ✅ Error Handling")
    
    print("\n📖 Usage Example:")
    print("""
    curl -X POST http://localhost:5000/webhook_ai_analysis \\
      -H "Content-Type: application/json" \\
      -H "Authorization: Bearer your_token" \\
      -H "X-Access-Token: your_access_token" \\
      -d '{
        "symbol": "XAUUSD",
        "timeframes": ["M15", "H1", "H4"],
        "barback": 50,
        "prompt": "Analyze gold trends and provide trading signals",
        "ai": "gpt",
        "image": "http://example.com/chart.jpg"
      }'
    """)

if __name__ == "__main__":
    main()
