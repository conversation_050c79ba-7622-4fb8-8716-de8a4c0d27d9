import pandas as pd
import numpy as np
import os

def generate_mock_data(start_time, periods, freq, seed=42):
    np.random.seed(seed)
    timestamps = pd.date_range(start=start_time, periods=periods, freq=freq)

    opens = []
    closes = []

    # เริ่มต้นที่ราคา 3000
    open_price = 3000
    for _ in range(periods):
        # ราคาปิดแกว่งเบาๆ ประมาณ -1 ถึง +1
        change = np.random.uniform(-1.0, 1.0)
        close_price = open_price + change

        opens.append(open_price)
        closes.append(close_price)

        # ราคาถัดไปเริ่มที่ราคาปิดแท่งก่อนหน้า
        open_price = close_price

    opens = np.array(opens)
    closes = np.array(closes)
    highs = np.maximum(opens, closes) + np.abs(np.random.uniform(0.2, 0.8, periods))
    lows = np.minimum(opens, closes) - np.abs(np.random.uniform(0.2, 0.8, periods))

    df = pd.DataFrame({
        'time': timestamps,
        'open': opens.round(2),
        'high': highs.round(2),
        'low': lows.round(2),
        'close': closes.round(2)
    })

    return df

# สร้างข้อมูล 5 นาที 100 แท่ง และ 30 นาที 100 แท่ง
df_5m = generate_mock_data('2025-04-19 09:00:00', periods=500, freq='5T')
df_30m = generate_mock_data('2025-04-18 09:00:00', periods=500, freq='30T')

# 📁 ดึง path ปัจจุบันของไฟล์ python
current_dir = os.path.dirname(os.path.abspath(__file__))
print(current_dir)
# 🟡 โหลด CSV จาก path เดียวกับไฟล์ python
# df_5m.to_csv(os.path.join(current_dir, "MockData/5m.csv"), index=False)
# df_30m.to_csv(os.path.join(current_dir, "MockData/30m.csv"), index=False)
df_5m.to_csv(os.path.join(current_dir, "5m.csv"), index=False)
df_30m.to_csv(os.path.join(current_dir, "30m.csv"), index=False)
print(df_5m)
print(df_30m)
print("=================")