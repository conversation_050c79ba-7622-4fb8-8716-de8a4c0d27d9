import numpy as np
import pandas as pd
import talib as ta
import os
import MetaTrader5 as mt5
from time import sleep
from dotenv import load_dotenv

load_dotenv()
folder_dir = os.getenv('DIR')
login = int(os.getenv('LOGIN'))
pwd = os.getenv('PWD')
server = os.getenv('SERVER')
symbol = os.getenv('SYMBOL') 
path = "C:\\Program Files\\MetaTrader 5\\terminal64.exe"
strategy_name = "Trend Bot"

# connect to MetaTrader5 Terminal
if not mt5.initialize(path=path):
    print("Initialize() failed, error code =", mt5.last_error())
    quit()
else:
    if mt5.login(login,pwd,server):
        print("logged in succesffully")
    else: 
        print("login failed, error code: {}".format(mt5.last_error()))

print(f"Logged in to {server} as {login}")

# ตั้งค่าข้อมูล
symbol_info = mt5.symbol_info(symbol)
timeframe = mt5.TIMEFRAME_M1
volume = 0.01
deviation=20
magic=30
stop_loss=0.0
take_profit=0.0
sl = 400
tp = 400
data_length = 500  # จำนวนแท่งเทียนที่ต้องการดึงข้อมูล

print('########################################################################################################\n')

# พารามิเตอร์
multiplier = 1
atr_periods = 5
atr_sl_multiplier = 1


def market_order(order_type, stop_loss, take_profit, strategy_name):
    symbol_info_tick = mt5.symbol_info_tick(symbol)

    order_type_dict = {
        'buy': mt5.ORDER_TYPE_BUY,
        'sell': mt5.ORDER_TYPE_SELL
    }

    price_dict = {
        'buy': symbol_info_tick.ask,
        'sell': symbol_info_tick.bid
    }

    request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": volume,  # FLOAT
        "type": order_type_dict[order_type],
        "price": price_dict[order_type],
        "sl": stop_loss,  # FLOAT
        # "tp": take_profit,  # FLOAT
        "deviation": deviation,  # INTERGER
        "magic": magic,  # INTERGER
        "comment": strategy_name,
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,  # mt5.ORDER_FILLING_FOK if IOC does not work
    }
    print(f"Signal {order_type}, ask price {price_dict[order_type]}, SL {stop_loss}, TP {take_profit}")
    order_result = mt5.order_send(request)
    return (order_result)
         
def close_old_position(new_action): 
    # Retrieve all open positions
    positions = mt5.positions_get()

    if positions is None or len(positions) == 0:
        print("No open positions found.")
    else:
        print(f"Found {len(positions)} open positions.")
        for position in positions:
            symbol = position.symbol
            ticket = position.ticket
            lot_size = position.volume
            action = "sell" if position.type == mt5.ORDER_TYPE_BUY else "buy"

            if new_action != action:
                # Prepare the trade request
                request = {
                    "action": mt5.TRADE_ACTION_DEAL,
                    "symbol": symbol,
                    "volume": lot_size,
                    "type": mt5.ORDER_TYPE_SELL if position.type == mt5.ORDER_TYPE_BUY else mt5.ORDER_TYPE_BUY,
                    "position": ticket,
                    "price": symbol_info_tick.bid if action == "sell" else symbol_info_tick.ask,
                    "deviation": 20,
                    "magic": 123456,
                    "comment": "Close position",
                }

                # Send the trade request
                result = mt5.order_send(request)

                # Check the result
                if result.retcode == mt5.TRADE_RETCODE_DONE:
                    print(f"Successfully closed position {ticket} on {symbol}.")
                else:
                    print(f"Failed to close position {ticket} on {symbol}: {result.comment}")

def get_data():
    """ดึงข้อมูลแท่งเทียนล่าสุด"""
    rates = mt5.copy_rates_from_pos(symbol, timeframe, 1, data_length)
    if rates is None:
        print("Failed to fetch data.")
        mt5.shutdown()
    df = pd.DataFrame(rates)
    df['time'] = pd.to_datetime(df['time'], unit='s')
    df.set_index('time', inplace=True)
    df['hl2'] = (df['high'] + df['low']) / 2
    return df

def calculate_indicators(df):
    """คำนวณตัวชี้วัด"""
    df['atr'] = ta.ATR(df['high'], df['low'], df['close'], timeperiod=atr_periods)

    df['up'] = df['hl2'] - multiplier * df['atr']
    df['dn'] = df['hl2'] + multiplier * df['atr']

    prev_up = df['up'].shift(1)
    prev_up = prev_up if prev_up is not None else df['up']
    prev_dn = df['dn'].shift(1)
    prev_dn = prev_dn if prev_dn is not None else df['dn']

    # df['up'] = np.maximum(df['up'], prev_up) if prev_close > prev_up else  df['up']
    # df['dn'] = np.minimum(df['dn'], prev_dn) if prev_close < prev_dn else  df['dn']
    df['up'] = np.where( df['close'].shift(1) > prev_up , np.minimum(df['up'], prev_up), df['up'])
    df['dn'] = np.where( df['close'].shift(1) < prev_dn , np.minimum(df['dn'], prev_dn), df['dn'])

    df['trend'] = 1
    prev_trend = df['trend'].shift(1)
    
    # new_trend = prev_trend if prev_trend is not None else df['trend']
    new_trend = np.where(prev_trend is not None, prev_trend, df['trend'])
    # new_trend = 1 if new_trend == -1 & df['close'] > prev_dn else (-1 if new_trend == 1 & df['close'] < prev_up else new_trend)
    new_trend = np.where(
        (new_trend == -1) & (df['close'] > prev_dn), 1,
        np.where((new_trend == 1) & (df['close'] < prev_up), -1, prev_trend)
    )

    df['trend'] = new_trend
    df['buySignal'] = (new_trend == 1) & (prev_trend == -1)
    df['sellSignal'] = (new_trend == -1) & (prev_trend == 1)
    return df

# ฟังก์ชันหาจุด Pivot High/Low
def calculate_pivots(data, left_bars, right_bars):
    if len(data) < left_bars + right_bars + 1:
        return None, None
    
    pivot_high = None
    pivot_low = None
    for i in range(left_bars, len(data) - right_bars):
        if data['high'][i] == max(data['high'][i-left_bars:i+right_bars+1]):
            pivot_high = data['high'][i]
        if data['low'][i] == min(data['low'][i-left_bars:i+right_bars+1]):
            pivot_low = data['low'][i]
    return pivot_high, pivot_low


if __name__ == '__main__':
    i = 0 # Loop ตรวจสอบ
    
    # Loop ตรวจสอบและส่งคำสั่งซื้อขาย
    try:
        trading_allowed = True
        while trading_allowed:
            symbol_info_tick = mt5.symbol_info_tick(symbol)
            i += 1

            df = get_data()
            df = calculate_indicators(df)

            latest = df.iloc[-1] 
            print(f"Checking.. {i} trend {latest['trend']} " + ("sell" if latest['trend'] < 0 else "buy" ))
            if latest['buySignal']:
                # stop_loss = latest['dn'] # latest['close'] - atr_sl_multiplier * latest['atr']
                stop_loss = latest['low'] - symbol_info.point # * sl 
                take_profit = symbol_info_tick.ask + symbol_info.point * tp 
                close_old_position('buy')
                market_order('buy', stop_loss, take_profit, strategy_name + " Long")
                # trading_allowed = False
            elif latest['sellSignal']:
                # stop_loss = latest['up'] # latest['close'] + atr_sl_multiplier * latest['atr']
                stop_loss = latest['high'] + symbol_info.point # * sl 
                take_profit = symbol_info_tick.bid - tp * symbol_info.point * tp
                close_old_position('sell')
                market_order('sell', stop_loss, take_profit, strategy_name + " Short")
                # trading_allowed = False 

            sleep(1)  # รอ 1 วินาที
    except KeyboardInterrupt:
        print("Stopped by user")
    finally:
        mt5.shutdown()
