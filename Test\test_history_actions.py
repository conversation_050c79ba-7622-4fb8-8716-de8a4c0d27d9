#!/usr/bin/env python3
"""
Test script for AI Bot History actions: Edit, Delete, Extract to Input Tab
"""

import os
import json
from datetime import datetime, timedelta
import random

def create_test_history_with_signals():
    """Create test history data with structured signal formats for testing extract functionality"""
    
    # Create Logs/Signals directory if it doesn't exist
    logs_dir = os.path.join(os.getcwd(), "Logs", "Signals")
    os.makedirs(logs_dir, exist_ok=True)
    
    bot_name = "Test_Actions_Bot"
    
    # Test cases with structured signal formats
    test_cases = [
        # Structured BUY signal with multiple TPs
        {
            "signal_type": "BUY",
            "entry_price": 2650.50,
            "tp_price": 2680.75,
            "sl_price": 2630.25,
            "analysis": """Signal ID: a7b9c2d4
C.GPT 
Symbol: XAUUSD 
Status: PENDING
Signal: Buy Limit 
Price: 2650.50
SL: 2630.25
TP1: 2660.00
TP2: 2670.00 
TP3: 2680.75 
TP4: 2690.00 
TP5: 2700.00
Reason: Strong bullish momentum with RSI breakout above 60""",
            "signal_data": {
                "signal_id": "a7b9c2d4",
                "symbol": "XAUUSD",
                "signal_type": "Buy Limit",
                "entry_price": 2650.50,
                "sl_price": 2630.25,
                "tp1_price": 2660.00,
                "tp2_price": 2670.00,
                "tp3_price": 2680.75,
                "tp4_price": 2690.00,
                "tp5_price": 2700.00,
                "reason": "Strong bullish momentum with RSI breakout above 60"
            }
        },
        # Structured SELL signal
        {
            "signal_type": "SELL",
            "entry_price": 2665.25,
            "tp_price": 2635.50,
            "sl_price": 2685.75,
            "analysis": """Signal ID: x3y8z1m5
C.GPT 
Symbol: XAUUSD 
Status: PENDING
Signal: Sell Limit 
Price: 2665.25
SL: 2685.75
TP1: 2655.00
TP2: 2645.00 
TP3: 2635.50 
Reason: Bearish divergence with overbought RSI conditions""",
            "signal_data": {
                "signal_id": "x3y8z1m5",
                "symbol": "XAUUSD",
                "signal_type": "Sell Limit",
                "entry_price": 2665.25,
                "sl_price": 2685.75,
                "tp1_price": 2655.00,
                "tp2_price": 2645.00,
                "tp3_price": 2635.50,
                "reason": "Bearish divergence with overbought RSI conditions"
            }
        },
        # EURUSD BUY signal
        {
            "signal_type": "BUY",
            "entry_price": 1.0850,
            "tp_price": 1.0920,
            "sl_price": 1.0800,
            "analysis": """Signal ID: e4r7t2w9
C.GPT 
Symbol: EURUSD 
Status: PENDING
Signal: Buy Limit 
Price: 1.0850
SL: 1.0800
TP1: 1.0880
TP2: 1.0900 
TP3: 1.0920 
Reason: EUR strength with ECB hawkish stance""",
            "signal_data": {
                "signal_id": "e4r7t2w9",
                "symbol": "EURUSD",
                "signal_type": "Buy Limit",
                "entry_price": 1.0850,
                "sl_price": 1.0800,
                "tp1_price": 1.0880,
                "tp2_price": 1.0900,
                "tp3_price": 1.0920,
                "reason": "EUR strength with ECB hawkish stance"
            }
        },
        # HOLD signal (no extraction possible)
        {
            "signal_type": "HOLD",
            "entry_price": None,
            "tp_price": None,
            "sl_price": None,
            "analysis": "Market consolidation phase detected. Mixed signals across timeframes suggest waiting for clearer directional bias before entering positions. Key levels to watch: Support at 2640, Resistance at 2670.",
            "signal_data": {}
        },
        # Analysis with long text for edit testing
        {
            "signal_type": "BUY",
            "entry_price": 2655.00,
            "tp_price": 2675.00,
            "sl_price": 2645.00,
            "analysis": """Comprehensive Technical Analysis Report:

1. TREND ANALYSIS:
   - Primary trend: Bullish on H4 and Daily timeframes
   - Secondary trend: Consolidation on H1
   - Key moving averages: Price above EMA20, EMA50, EMA200

2. TECHNICAL INDICATORS:
   - RSI(14): 58.5 - Neutral to bullish momentum
   - MACD: Bullish crossover confirmed
   - Stochastic: %K above %D, indicating upward momentum
   - Volume: Above average, confirming trend strength

3. SUPPORT & RESISTANCE:
   - Immediate support: 2650.00
   - Strong support: 2640.00 (previous resistance turned support)
   - Immediate resistance: 2670.00
   - Major resistance: 2680.00 (psychological level)

4. PRICE ACTION:
   - Bullish engulfing pattern on H1
   - Higher highs and higher lows formation
   - Break above descending trendline

5. FUNDAMENTAL FACTORS:
   - USD weakness due to dovish Fed expectations
   - Gold demand from central banks
   - Geopolitical tensions supporting safe haven demand

TRADING RECOMMENDATION:
Signal: Buy Limit at 2655.00
Stop Loss: 2645.00 (10 points risk)
Take Profit: 2675.00 (20 points reward)
Risk-Reward Ratio: 1:2

This analysis provides a comprehensive view of the current market conditions and suggests a favorable risk-reward setup for a long position.""",
            "signal_data": {
                "signal_type": "Buy Limit",
                "entry_price": 2655.00,
                "sl_price": 2645.00,
                "tp1_price": 2675.00,
                "reason": "Comprehensive technical analysis shows bullish setup"
            }
        }
    ]
    
    # Create 10 test entries
    for i in range(10):
        # Generate timestamp (last 15 days)
        days_ago = random.randint(0, 15)
        hours_ago = random.randint(0, 23)
        minutes_ago = random.randint(0, 59)
        
        timestamp = datetime.now() - timedelta(days=days_ago, hours=hours_ago, minutes=minutes_ago)
        timestamp_str = timestamp.strftime("%Y%m%d_%H%M%S")
        
        # Select test case
        test_case = random.choice(test_cases)
        
        # Create history entry
        history_entry = {
            "error": False,
            "message": "AI analysis completed successfully",
            "symbol": test_case['signal_data'].get('symbol', 'XAUUSD.iux'),
            "timeframes": random.choice([["H1"], ["M15", "H1"], ["H1", "H4"]]),
            "ai_provider": "GPT",
            "analysis": test_case['analysis'],
            "prompt": f"Analyze the trading data for comprehensive technical analysis.",
            "image_analyzed": False,
            "use_signal_format": 1 if test_case['signal_type'] in ['BUY', 'SELL'] else 0,
            "timestamp": timestamp.isoformat(),
            "signal_data": test_case['signal_data']
        }
        
        # Save to file
        filename = f"{bot_name}_{timestamp_str}.json"
        filepath = os.path.join(logs_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(history_entry, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Created 10 test history entries for {bot_name}")
    print(f"📁 Files saved in: {logs_dir}")
    
    print("\n🎯 Test Cases Created:")
    print("- Structured BUY signals with multiple TPs")
    print("- Structured SELL signals") 
    print("- EURUSD signals for symbol conversion testing")
    print("- HOLD signals (no extract button)")
    print("- Long analysis text for edit testing")
    
    print("\n🔧 Action Buttons to Test:")
    print("1. 📤 Extract to Input - Parses signal format and fills Input tab")
    print("2. ✏️ Edit - Opens text editor for analysis modification")
    print("3. 🗑️ Delete - Removes history file with confirmation")
    print("4. 👁️ View Details - Shows full analysis popup")
    
    print("\n🧪 Testing Scenarios:")
    print("1. Extract BUY/SELL signals to Input tab")
    print("2. Verify symbol conversion (XAUUSD->XU, EURUSD->EU)")
    print("3. Check TP1-TP5 parsing and population")
    print("4. Edit analysis text and save changes")
    print("5. Delete history items with confirmation")
    print("6. Test error handling for missing files")
    
    print("\n🚀 To test:")
    print("1. Run main application")
    print("2. Create bot named 'Test Actions Bot'")
    print("3. Click '📊 History' button")
    print("4. Test all action buttons on different signal types")
    print("5. Verify Input tab gets populated correctly")
    print("6. Check edit functionality saves changes")
    print("7. Confirm delete removes files and refreshes display")

if __name__ == "__main__":
    create_test_history_with_signals()
