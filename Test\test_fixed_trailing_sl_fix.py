#!/usr/bin/env python3
"""
Test script to verify the Fixed trailing SL fix
Tests that Auto SL to BE with trailing_type="Fixed" only triggers at 2/3 of TP distance
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from App.util import Util
from App.config import Config
import MetaTrader5 as mt5

class MockOrder:
    """Mock order object for testing"""
    def __init__(self, ticket, symbol, type_, price_open, sl, tp, magic=155214):
        self.ticket = ticket
        self.symbol = symbol
        self.type = type_
        self.price_open = price_open
        self.sl = sl
        self.tp = tp
        self.magic = magic
        self.price_current = price_open  # Will be updated in tests

class MockSymbolInfo:
    """Mock symbol info for testing"""
    def __init__(self, point=0.00001):
        self.point = point

def test_fixed_trailing_calculation():
    """Test that Fixed trailing calculates point_be correctly as 2/3 of TP distance"""
    print("=== Testing Fixed Trailing SL Calculation ===")
    
    # Test scenario: BUY order
    entry_price = 1.10000
    tp_price = 1.10300  # 300 points TP
    sl_price = 1.09700   # 300 points SL
    
    # Expected: 2/3 of TP distance = 2/3 * 300 points = 200 points
    # So trigger should be at 1.10000 + 0.00200 = 1.10200
    expected_trigger = entry_price + (tp_price - entry_price) * (2.0/3.0)
    
    print(f"Entry: {entry_price:.5f}")
    print(f"TP: {tp_price:.5f}")
    print(f"SL: {sl_price:.5f}")
    print(f"TP Distance: {tp_price - entry_price:.5f}")
    print(f"Expected 2/3 trigger: {expected_trigger:.5f}")
    
    # Test different current prices
    test_prices = [
        (1.10100, False, "Should NOT trigger - only 1/3 of TP"),
        (1.10150, False, "Should NOT trigger - only 1/2 of TP"),
        (1.10199, False, "Should NOT trigger - just under 2/3"),
        (1.10200, True, "Should trigger - exactly 2/3 of TP"),
        (1.10250, True, "Should trigger - over 2/3 of TP"),
    ]
    
    for current_price, should_trigger, description in test_prices:
        print(f"\nTesting: {current_price:.5f} - {description}")
        
        # Calculate point_be using the new Fixed logic
        tp_distance = abs(tp_price - entry_price)
        point_be = tp_distance * (2.0 / 3.0)
        trigger_price = entry_price + point_be
        
        will_trigger = current_price >= trigger_price
        
        print(f"  TP distance: {tp_distance:.5f}")
        print(f"  point_be (2/3): {point_be:.5f}")
        print(f"  Trigger price: {trigger_price:.5f}")
        print(f"  Current price: {current_price:.5f}")
        print(f"  Will trigger: {will_trigger}")
        print(f"  Expected: {should_trigger}")
        
        if will_trigger == should_trigger:
            print(f"  ✅ PASS")
        else:
            print(f"  ❌ FAIL")

def test_sell_order_fixed_trailing():
    """Test Fixed trailing for SELL orders"""
    print("\n=== Testing Fixed Trailing SL for SELL Orders ===")
    
    # Test scenario: SELL order
    entry_price = 1.10000
    tp_price = 1.09700  # 300 points TP (down)
    sl_price = 1.10300   # 300 points SL (up)
    
    # Expected: 2/3 of TP distance = 2/3 * 300 points = 200 points
    # So trigger should be at 1.10000 - 0.00200 = 1.09800
    expected_trigger = entry_price - (entry_price - tp_price) * (2.0/3.0)
    
    print(f"Entry: {entry_price:.5f}")
    print(f"TP: {tp_price:.5f}")
    print(f"SL: {sl_price:.5f}")
    print(f"TP Distance: {entry_price - tp_price:.5f}")
    print(f"Expected 2/3 trigger: {expected_trigger:.5f}")
    
    # Test different current prices
    test_prices = [
        (1.09900, False, "Should NOT trigger - only 1/3 of TP"),
        (1.09850, False, "Should NOT trigger - only 1/2 of TP"),
        (1.09801, False, "Should NOT trigger - just under 2/3"),
        (1.09800, True, "Should trigger - exactly 2/3 of TP"),
        (1.09750, True, "Should trigger - over 2/3 of TP"),
    ]
    
    for current_price, should_trigger, description in test_prices:
        print(f"\nTesting: {current_price:.5f} - {description}")
        
        # Calculate point_be using the new Fixed logic
        tp_distance = abs(tp_price - entry_price)
        point_be = tp_distance * (2.0 / 3.0)
        trigger_price = entry_price - point_be
        
        will_trigger = current_price <= trigger_price
        
        print(f"  TP distance: {tp_distance:.5f}")
        print(f"  point_be (2/3): {point_be:.5f}")
        print(f"  Trigger price: {trigger_price:.5f}")
        print(f"  Current price: {current_price:.5f}")
        print(f"  Will trigger: {will_trigger}")
        print(f"  Expected: {should_trigger}")
        
        if will_trigger == should_trigger:
            print(f"  ✅ PASS")
        else:
            print(f"  ❌ FAIL")

def test_old_vs_new_calculation():
    """Compare old calculation vs new calculation"""
    print("\n=== Comparing Old vs New Calculation ===")
    
    entry_price = 1.10000
    tp_price = 1.10300
    sl_price = 1.09700
    point = 0.00001
    
    # Old calculation (problematic)
    original_sl_points = abs(entry_price - sl_price) / point  # 300 points
    point_tp = (tp_price - entry_price) / point  # 300 points
    factor = (point_tp / original_sl_points) - 1  # (300/300) - 1 = 0
    point_sl = abs(entry_price - sl_price)  # 0.00300
    old_point_be = factor * point_sl  # 0 * 0.00300 = 0.00000
    old_trigger = entry_price + old_point_be  # 1.10000 + 0.00000 = 1.10000
    
    # New calculation (fixed)
    tp_distance = abs(tp_price - entry_price)  # 0.00300
    new_point_be = tp_distance * (2.0 / 3.0)  # 0.00300 * 0.6667 = 0.00200
    new_trigger = entry_price + new_point_be  # 1.10000 + 0.00200 = 1.10200
    
    print(f"Entry: {entry_price:.5f}")
    print(f"TP: {tp_price:.5f}")
    print(f"SL: {sl_price:.5f}")
    print()
    print(f"Old calculation:")
    print(f"  factor: {factor:.5f}")
    print(f"  point_be: {old_point_be:.5f}")
    print(f"  Trigger: {old_trigger:.5f} (WRONG - triggers immediately!)")
    print()
    print(f"New calculation:")
    print(f"  TP distance: {tp_distance:.5f}")
    print(f"  point_be (2/3): {new_point_be:.5f}")
    print(f"  Trigger: {new_trigger:.5f} (CORRECT - triggers at 2/3 of TP)")

def main():
    """Run all tests"""
    print("Testing Fixed Trailing SL Fix")
    print("=" * 50)
    
    test_fixed_trailing_calculation()
    test_sell_order_fixed_trailing()
    test_old_vs_new_calculation()
    
    print("\n" + "=" * 50)
    print("Test completed. The fix ensures Fixed trailing only triggers at 2/3 of TP distance.")

if __name__ == "__main__":
    main()
