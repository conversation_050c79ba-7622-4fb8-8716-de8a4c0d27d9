import os.path

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
 
# If modifying these scopes, delete the file token.json.
# SCOPES = ["https://www.googleapis.com/auth/spreadsheets.readonly"]
SCOPES = ["https://www.googleapis.com/auth/spreadsheets"]

# The ID and range of a sample spreadsheet.
BACKTEST_SPREADSHEET_ID = "1E9iq27_oDG8Ea31lObcXVmCRi0TothhCom8zM6FqTUY"
BACKTEST_RANGE_NAME = "Backtesting!A28:K"


def main():
  """Shows basic usage of the Sheets API.
  Prints values from a sample spreadsheet.
  """
  creds = None
  # The file token.json stores the user's access and refresh tokens, and is
  # created automatically when the authorization flow completes for the first
  # time.
  if os.path.exists("token.json"):
    creds = Credentials.from_authorized_user_file("token.json", SCOPES)
  # If there are no (valid) credentials available, let the user log in.
  if not creds or not creds.valid:
    if creds and creds.expired and creds.refresh_token:
      creds.refresh(Request())
    else:
      flow = InstalledAppFlow.from_client_secrets_file(
          "credentials.json", SCOPES
      )
      creds = flow.run_local_server(port=0)
    # Save the credentials for the next run
    with open("token.json", "w") as token:
      token.write(creds.to_json())

  try:
    service = build("sheets", "v4", credentials=creds)

    # Call the Sheets API
    sheet = service.spreadsheets()
    result = (
        sheet.values()
        .get(spreadsheetId=BACKTEST_SPREADSHEET_ID, range=BACKTEST_RANGE_NAME)
        .execute()
    )
    values = result.get("values", [])

    if not values or len(values) == 0:
      print("No data found.")
      return

    print("Name, Major:")
    for row in values:
      # Print columns A and E, which correspond to indices 0 and 4.
      print(f"{row[1]} {row[2]}")
    list = [
        [
            'XAUUSD',   # ASSET
            'SELL',     # Position
            '1/8/2023', # Date
            '1970.22',  # Entry
            '',         # SL
            '1965.12',  # TP1
            '1960.17',  # TP2
            '1000'      # Points
        ]
    ]
    resource = {
        "majorDimension": "ROWS",
        "values": list
    }
    sheet.values().append(
        spreadsheetId=BACKTEST_SPREADSHEET_ID,
        range=BACKTEST_RANGE_NAME,
        valueInputOption='USER_ENTERED',
        body=resource
    ).execute()

    print("Added a new row to your Google sheet")
  except HttpError as err:
    print(err)


if __name__ == "__main__":
  main()