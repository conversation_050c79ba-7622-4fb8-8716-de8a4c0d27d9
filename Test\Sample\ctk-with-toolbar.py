import customtkinter as ctk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk

# ตั้งค่า customtkinter
ctk.set_appearance_mode("light")
ctk.set_default_color_theme("blue")

# สร้างหน้าต่างหลัก
app = ctk.CTk()
app.geometry("700x500")
app.title("Interactive Plot in CTk")

# สร้างเฟรมสำหรับวางกราฟ
frame = ctk.CTkFrame(app)
frame.pack(padx=20, pady=20, fill="both", expand=True)

# สร้าง figure และ axes ของ matplotlib
fig, ax = plt.subplots()
ax.plot([0, 1, 2, 3, 4], [10, 20, 25, 30, 40], marker='o')
ax.set_title("กราฟแบบ Interactive")

# สร้าง FigureCanvasTkAgg และฝังใน CTkFrame
canvas = FigureCanvasTkAgg(fig, master=frame)
canvas.draw()
canvas_widget = canvas.get_tk_widget()
canvas_widget.pack(fill="both", expand=True)

# สร้าง Navigation Toolbar และฝังใน CTkFrame ด้านบนหรือด้านล่างกราฟ
toolbar = NavigationToolbar2Tk(canvas, frame)
toolbar.update()
toolbar.pack(side="top", fill="x")

app.mainloop()
