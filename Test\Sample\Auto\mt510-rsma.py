import MetaTrader5 as mt5
import numpy as np
import pandas as pd
# import pandas_ta as ta
import talib as ta
import time
from datetime import datetime

# Config
SYMBOL = "XAUUSD.iux"
TIMEFRAME = mt5.TIMEFRAME_M15
LOT = 0.02
SL_POINTS = 500
TP_POINTS = 1000
MAGIC = 123456
RSI_LEN = 25
SMA_LEN = 50
# Maximum number of TP fields
MAX_TP_FIELDS = 5

# Initialize connection
if not mt5.initialize():
    print("Initialize failed:", mt5.last_error())
    quit()

def get_data(timeframe, bars=100):
    rates = mt5.copy_rates_from_pos(SYMBOL, timeframe, 0, bars)
    df = pd.DataFrame(rates)
    df['time'] = pd.to_datetime(df['time'], unit='s')
    return df

def send_order(order_type):
    point = mt5.symbol_info(SYMBOL).point
    price = mt5.symbol_info_tick(SYMBOL).ask if order_type == mt5.ORDER_TYPE_BUY else mt5.symbol_info_tick(SYMBOL).bid
    sl = price - SL_POINTS * point if order_type == mt5.ORDER_TYPE_BUY else price + SL_POINTS * point
    tp = price + TP_POINTS * point if order_type == mt5.ORDER_TYPE_BUY else price - TP_POINTS * point

    request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": SYMBOL,
        "volume": LOT,
        "type": order_type,
        "price": price,
        "sl": sl,
        "tp": tp,
        "deviation": 10,
        "magic": MAGIC,
        "comment": "RSI2_SMA_Strategy",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }

    result = mt5.order_send(request)
    print(f"Order result: {result}")
    return result

def crossover(series1, series2):
    return series1.iloc[-2] < series2.iloc[-2] and series1.iloc[-1] > series2.iloc[-1]

def crossunder(series1, series2):
    return series1.iloc[-2] > series2.iloc[-2] and series1.iloc[-1] < series2.iloc[-1]

def compute_indicators():
    df = get_data(TIMEFRAME)
    df['rsi'] = ta.RSI(df['close'], timeperiod=RSI_LEN)
    df['sma'] = ta.SMA(df['rsi'], timeperiod=SMA_LEN)

    if crossover(df['rsi'], df['sma']):
        print(f"{datetime.now()}: BUY Signal ")
        send_order(mt5.ORDER_TYPE_BUY)

    elif crossunder(df['rsi'], df['sma']):
        print(f"{datetime.now()}: SELL Signal ")
        send_order(mt5.ORDER_TYPE_SELL)
     
    return 'BULL 🟢' if df['rsi'].iloc[-1] > df['sma'].iloc[-1] else 'BEAR 🔴'
 
def update_orders():
    """ Auto update orders based on price conditions """
    orders = mt5.positions_get() if mt5.initialize() else []
    
    for order in orders:
        entry_price = order.price_open
        current_price = order.price_current
        current_tp = order.tp
        symbol_info = mt5.symbol_info(order.symbol)
        if not symbol_info:
            continue

        point = symbol_info.point
        new_sl = None

        if order.type == mt5.ORDER_TYPE_BUY and current_price >= entry_price + 5:
            new_sl = entry_price + (10 * point)
        elif order.type == mt5.ORDER_TYPE_SELL and current_price <= entry_price - 5:
            new_sl = entry_price - (10 * point)

        if new_sl:
            request = {"action": mt5.TRADE_ACTION_SLTP, "position": order.ticket, "sl": new_sl, "tp": current_tp}
            mt5.order_send(request)
    return orders

# Main loop: check every 5 mins
try:
    i = 0 # Loop ตรวจสอบ
    while True:
        i += 1
        orders = update_orders()
        if len(orders) < MAX_TP_FIELDS :
            trend = compute_indicators()
        print(f"{datetime.now()}: Checking.. {i} {trend}")
        time.sleep(60)  # 60 sec = 1 min /  300 sec = 5 mins
except KeyboardInterrupt:
    print("Stopped by user")
finally:
    mt5.shutdown()
