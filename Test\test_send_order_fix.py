#!/usr/bin/env python3
"""
Test the send_order fix
"""

import MetaTrader5 as mt5
import sys
import os

# Add the App directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'App'))

def test_send_order_fix():
    """Test the fixed send_order method"""
    print("=== Testing Send Order Fix ===")
    
    # Initialize MT5
    if not mt5.initialize():
        print("❌ MT5 initialization failed")
        return False
    
    try:
        from config import Config
        from util import Util
        
        config = Config()
        util = Util(config)
        
        # Test parameters (very small lot for safety)
        order_type = "Buy Now"
        symbol = "EURUSD"
        lot = 0.01
        
        # Get current price
        tick = mt5.symbol_info_tick(symbol)
        if not tick:
            print("❌ Cannot get current price")
            return False
        
        entry = tick.ask
        sl = entry - 0.0050  # 50 pips SL
        tp = entry + 0.0050  # 50 pips TP
        
        print(f"Testing: {order_type} {symbol} {lot} lots")
        print(f"Entry: {entry}, SL: {sl}, TP: {tp}")
        print()
        
        # Test the send_order method
        print("Calling send_order...")
        result = util.send_order(order_type, symbol, lot, entry, sl, tp, "TEST_FIX")
        
        print(f"\nResult: {result}")
        
        if result is None:
            print("❌ Still returning None - check the error messages above")
            return False
        else:
            print("✅ send_order now returns a result!")
            if hasattr(result, 'retcode'):
                print(f"Return code: {result.retcode}")
                if result.retcode == mt5.TRADE_RETCODE_DONE:
                    print("✅ Order executed successfully!")
                    print("⚠️ WARNING: A real order was placed!")
                else:
                    print(f"Order failed (expected): {result.comment}")
                    print("✅ This is normal for testing - the method is working correctly")
            return True
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        return False
    finally:
        mt5.shutdown()

if __name__ == "__main__":
    print("Send Order Fix Test")
    print("=" * 30)
    print("⚠️ WARNING: This test may place a real order if conditions are met!")
    print("Make sure you're on a demo account or prepared for a small trade.")
    print()
    
    response = input("Continue with test? (y/N): ")
    if response.lower() != 'y':
        print("Test cancelled")
        sys.exit(0)
    
    if test_send_order_fix():
        print("\n✅ Fix successful! send_order is now working properly.")
    else:
        print("\n❌ Fix needs more work. Check the error messages above.")
    
    input("Press Enter to exit...")
