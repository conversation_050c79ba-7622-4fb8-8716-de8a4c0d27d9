#!/usr/bin/env python3
"""
Test script to verify TabOrders controls are working properly
This script helps debug the Auto SL to BE toggles and refresh functionality
"""

import sys
import os

# Add the App directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'App'))

def test_taborders_imports():
    """Test if TabOrders can be imported and initialized"""
    print("=== Testing TabOrders Imports ===")
    
    try:
        # Test imports
        import customtkinter as ctk
        from tab_orders import TabOrders
        from config import Config
        from util import Util
        
        print("✅ All imports successful")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_taborders_initialization():
    """Test TabOrders initialization"""
    print("\n=== Testing TabOrders Initialization ===")
    
    try:
        import customtkinter as ctk
        from tab_orders import TabOrders
        from config import Config
        from util import Util
        
        # Create a minimal test environment
        root = ctk.CTk()
        root.withdraw()  # Hide the window
        
        # Create tabview
        tabview = ctk.CTkTabview(root)
        
        # Create config and util (mock objects)
        config = Config()
        util = Util(config)
        
        # Initialize TabOrders
        tab_orders = TabOrders(tabview, config, util)
        
        print("✅ TabOrders initialized successfully")
        
        # Test the controls
        print(f"📊 Auto BE All enabled: {tab_orders.auto_be_all_enabled}")
        print(f"📊 Auto BE SIG enabled: {tab_orders.auto_be_sig_enabled}")
        print(f"📊 Auto BE INPUT enabled: {tab_orders.auto_be_input_enabled}")
        print(f"📊 Loop running: {tab_orders.loop_running}")
        
        # Test toggle methods
        print("\n🔄 Testing toggle methods...")
        
        # Test auto BE toggles
        original_all = tab_orders.auto_be_all_enabled
        tab_orders.toggle_auto_be_all()
        new_all = tab_orders.auto_be_all_enabled
        print(f"   Auto BE All toggle: {original_all} → {new_all}")
        
        original_sig = tab_orders.auto_be_sig_enabled
        tab_orders.toggle_auto_be_sig()
        new_sig = tab_orders.auto_be_sig_enabled
        print(f"   Auto BE SIG toggle: {original_sig} → {new_sig}")
        
        original_input = tab_orders.auto_be_input_enabled
        tab_orders.toggle_auto_be_input()
        new_input = tab_orders.auto_be_input_enabled
        print(f"   Auto BE INPUT toggle: {original_input} → {new_input}")
        
        # Test refresh toggle
        original_refresh = tab_orders.loop_running
        if hasattr(tab_orders, 'refresh_switch'):
            tab_orders.refresh_switch.select()
            tab_orders.toggle_refresh()
            new_refresh = tab_orders.loop_running
            print(f"   Refresh toggle: {original_refresh} → {new_refresh}")
        else:
            print("   ⚠️ Refresh switch not found")
        
        # Test debug method
        if hasattr(tab_orders, 'debug_status'):
            print("\n🔍 Running debug status...")
            tab_orders.debug_status()
        else:
            print("   ⚠️ Debug method not found")
        
        # Cleanup
        root.destroy()
        
        print("✅ All tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Initialization error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_control_visibility():
    """Test if controls are properly created and visible"""
    print("\n=== Testing Control Visibility ===")
    
    try:
        import customtkinter as ctk
        from tab_orders import TabOrders
        from config import Config
        from util import Util
        
        # Create test environment
        root = ctk.CTk()
        root.withdraw()
        
        tabview = ctk.CTkTabview(root)
        config = Config()
        util = Util(config)
        
        # Initialize TabOrders
        tab_orders = TabOrders(tabview, config, util)
        
        # Check if controls exist
        controls_to_check = [
            ('refresh_switch', 'Refresh Switch'),
            ('time_entry', 'Time Entry'),
            ('auto_be_all_switch', 'Auto BE All Switch'),
            ('auto_be_sig_switch', 'Auto BE SIG Switch'),
            ('auto_be_input_switch', 'Auto BE INPUT Switch'),
            ('status_label', 'Status Label'),
        ]
        
        print("🔍 Checking control existence:")
        all_exist = True
        
        for attr_name, display_name in controls_to_check:
            if hasattr(tab_orders, attr_name):
                control = getattr(tab_orders, attr_name)
                print(f"   ✅ {display_name}: Found ({type(control).__name__})")
            else:
                print(f"   ❌ {display_name}: Missing")
                all_exist = False
        
        # Check switch states
        if hasattr(tab_orders, 'auto_be_all_switch'):
            all_state = tab_orders.auto_be_all_switch.get()
            print(f"   📊 Auto BE All switch state: {all_state}")
        
        if hasattr(tab_orders, 'auto_be_sig_switch'):
            sig_state = tab_orders.auto_be_sig_switch.get()
            print(f"   📊 Auto BE SIG switch state: {sig_state}")
        
        if hasattr(tab_orders, 'auto_be_input_switch'):
            input_state = tab_orders.auto_be_input_switch.get()
            print(f"   📊 Auto BE INPUT switch state: {input_state}")
        
        if hasattr(tab_orders, 'refresh_switch'):
            refresh_state = tab_orders.refresh_switch.get()
            print(f"   📊 Refresh switch state: {refresh_state}")
        
        root.destroy()
        
        if all_exist:
            print("✅ All controls found")
        else:
            print("❌ Some controls missing")
        
        return all_exist
        
    except Exception as e:
        print(f"❌ Control visibility test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("TabOrders Controls Test Script")
    print("=" * 50)
    
    # Run tests
    test1_passed = test_taborders_imports()
    test2_passed = test_taborders_initialization()
    test3_passed = test_control_visibility()
    
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS:")
    print(f"   Imports: {'✅ PASS' if test1_passed else '❌ FAIL'}")
    print(f"   Initialization: {'✅ PASS' if test2_passed else '❌ FAIL'}")
    print(f"   Control Visibility: {'✅ PASS' if test3_passed else '❌ FAIL'}")
    
    if all([test1_passed, test2_passed, test3_passed]):
        print("\n🎉 ALL TESTS PASSED!")
        print("\nThe TabOrders controls should be visible and working.")
        print("If you still don't see them in the UI, try:")
        print("1. Restart the application")
        print("2. Check the Orders tab")
        print("3. Look for the control panel at the top")
        print("4. Click the Debug button to see current status")
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("Please check the error messages above.")
    
    print("\n💡 USAGE TIPS:")
    print("- Auto Refresh is disabled by default (as requested)")
    print("- Auto SL to BE (All) is enabled by default (as requested)")
    print("- Use the Debug button in the UI to check current status")
    print("- Toggle switches should be visible in the control panel")

if __name__ == "__main__":
    main()
