#!/usr/bin/env python3
"""
Test script to verify .env file loading and OpenAI API key availability
"""

import os
import sys

def test_env_loading():
    """Test environment variable loading"""
    print("🧪 Testing Environment Variable Loading")
    print("=" * 50)
    
    # Test 1: Check if .env file exists
    print("\n1. Checking .env file...")
    env_path = os.path.join(os.getcwd(), '.env')
    if os.path.exists(env_path):
        print(f"✅ .env file found: {env_path}")
        
        # Read and display .env content (masked)
        with open(env_path, 'r') as f:
            lines = f.readlines()
        
        print(f"   - File size: {os.path.getsize(env_path)} bytes")
        print(f"   - Number of lines: {len(lines)}")
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if line and not line.startswith('#'):
                if '=' in line:
                    key, value = line.split('=', 1)
                    if 'API_KEY' in key.upper():
                        masked_value = f"{value[:10]}...{value[-4:]}" if len(value) > 14 else "***"
                        print(f"   - Line {i}: {key}={masked_value}")
                    else:
                        print(f"   - Line {i}: {key}={value}")
                else:
                    print(f"   - Line {i}: {line}")
    else:
        print(f"❌ .env file not found: {env_path}")
        return False
    
    # Test 2: Try loading with python-dotenv
    print("\n2. Testing python-dotenv loading...")
    try:
        from dotenv import load_dotenv
        result = load_dotenv()
        print(f"✅ python-dotenv available, load result: {result}")
    except ImportError:
        print("⚠️ python-dotenv not installed, using manual loading")
        
        # Manual loading
        if os.path.exists(env_path):
            with open(env_path, 'r') as f:
                for line in f:
                    if '=' in line and not line.strip().startswith('#'):
                        key, value = line.strip().split('=', 1)
                        os.environ[key] = value
            print("✅ Manual .env loading completed")
    
    # Test 3: Check environment variables
    print("\n3. Checking environment variables...")
    
    # Check for OpenAI API key
    openai_key = os.getenv('OPENAI_API_KEY')
    if openai_key:
        masked_key = f"{openai_key[:10]}...{openai_key[-4:]}" if len(openai_key) > 14 else "***"
        print(f"✅ OPENAI_API_KEY found: {masked_key}")
        print(f"   - Length: {len(openai_key)} characters")
        print(f"   - Starts with: {openai_key[:7]}")
    else:
        print("❌ OPENAI_API_KEY not found in environment")
    
    # Check for other API keys
    api_keys = [k for k in os.environ.keys() if 'API_KEY' in k.upper()]
    if api_keys:
        print(f"🔍 Found API keys in environment: {api_keys}")
    else:
        print("🔍 No API keys found in environment")
    
    # Test 4: Test OpenAI API connection
    print("\n4. Testing OpenAI API connection...")
    if openai_key:
        try:
            import requests
            
            headers = {
                "Authorization": f"Bearer {openai_key}",
                "Content-Type": "application/json"
            }
            
            # Simple test request
            test_payload = {
                "model": "gpt-3.5-turbo",
                "messages": [
                    {"role": "user", "content": "Test connection - respond with 'OK'"}
                ],
                "max_tokens": 10
            }
            
            print("   Sending test request to OpenAI API...")
            response = requests.post(
                "https://api.openai.com/v1/chat/completions",
                headers=headers,
                json=test_payload,
                timeout=30
            )
            
            print(f"   Response status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if 'choices' in result and len(result['choices']) > 0:
                    ai_response = result['choices'][0]['message']['content']
                    print(f"✅ OpenAI API connection successful!")
                    print(f"   AI Response: {ai_response}")
                else:
                    print("⚠️ Unexpected response format")
            elif response.status_code == 401:
                print("❌ OpenAI API authentication failed - invalid API key")
            elif response.status_code == 429:
                print("⚠️ OpenAI API rate limit exceeded")
            else:
                print(f"❌ OpenAI API error: {response.status_code}")
                print(f"   Error details: {response.text}")
                
        except ImportError:
            print("⚠️ requests library not available for API testing")
        except Exception as e:
            print(f"❌ OpenAI API test failed: {e}")
    else:
        print("⚠️ Skipping API test - no API key available")
    
    # Test 5: Environment summary
    print("\n5. Environment Summary...")
    print(f"   - Current working directory: {os.getcwd()}")
    print(f"   - Python executable: {sys.executable}")
    print(f"   - Python version: {sys.version}")
    
    # Check if we're in a virtual environment
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("   - Virtual environment: ✅ Active")
    else:
        print("   - Virtual environment: ❌ Not detected")
    
    return bool(openai_key)

def test_app_integration():
    """Test integration with the main application"""
    print("\n🧪 Testing Application Integration")
    print("=" * 40)
    
    try:
        # Import the main application components
        from App.config import Config
        from App.util import Util
        
        print("✅ Application imports successful")
        
        # Create config and util instances
        config = Config()
        util = Util(config)
        
        print("✅ Application components initialized")
        
        # Test API key access through util
        api_key = os.getenv('OPENAI_API_KEY') or config.ai_api_config.get('gpt', {}).get('api_key', '')
        if api_key:
            masked_key = f"{api_key[:10]}...{api_key[-4:]}"
            print(f"✅ API key accessible through application: {masked_key}")
        else:
            print("❌ API key not accessible through application")
        
        return True
        
    except Exception as e:
        print(f"❌ Application integration test failed: {e}")
        return False

def main():
    """Run all environment tests"""
    print("🚀 Environment Variable Test Suite")
    print("=" * 50)
    
    # Run environment loading tests
    env_success = test_env_loading()
    
    # Run application integration tests
    app_success = test_app_integration()
    
    print("\n📋 Test Results Summary:")
    print(f"  - Environment Loading: {'✅ PASS' if env_success else '❌ FAIL'}")
    print(f"  - Application Integration: {'✅ PASS' if app_success else '❌ FAIL'}")
    
    if env_success and app_success:
        print("\n🎉 All tests passed! Your OpenAI API key should work correctly.")
    else:
        print("\n⚠️ Some tests failed. Check the issues above.")
        
    print("\n📖 Troubleshooting Tips:")
    print("1. Make sure .env file is in the root directory")
    print("2. Check that OPENAI_API_KEY is correctly set in .env")
    print("3. Restart the application after modifying .env")
    print("4. Install python-dotenv: pip install python-dotenv")

if __name__ == "__main__":
    main()
