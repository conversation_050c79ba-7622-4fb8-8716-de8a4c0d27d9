#!/usr/bin/env python3
"""
Test script to demonstrate the AI Bot History feature
Creates sample history data for testing
"""

import os
import json
from datetime import datetime, timedelta
import random

def create_sample_history():
    """Create sample history data for testing"""
    
    # Create Logs/Signals directory if it doesn't exist
    logs_dir = os.path.join(os.getcwd(), "Logs", "Signals")
    os.makedirs(logs_dir, exist_ok=True)
    
    bot_name = "My_XAUUSD_Bot"
    
    # Sample data for different signal types
    signal_types = ['BUY', 'SELL', 'HOLD']
    symbols = ['XAUUSD.iux', 'EURUSD.iux']
    timeframes = [['H1'], ['M15', 'H1'], ['H1', 'H4']]
    ai_providers = ['GPT', 'GEMINI']
    
    # Create 50 sample history entries over the last 3 months
    for i in range(50):
        # Generate timestamp (last 3 months)
        days_ago = random.randint(0, 90)
        hours_ago = random.randint(0, 23)
        minutes_ago = random.randint(0, 59)
        
        timestamp = datetime.now() - timedelta(days=days_ago, hours=hours_ago, minutes=minutes_ago)
        timestamp_str = timestamp.strftime("%Y%m%d_%H%M%S")
        
        # Generate random signal data
        signal_type = random.choice(signal_types)
        symbol = random.choice(symbols)
        tf = random.choice(timeframes)
        ai_provider = random.choice(ai_providers)
        
        # Generate prices based on symbol
        if 'XAUUSD' in symbol:
            base_price = random.uniform(2600, 2700)
        else:  # EURUSD
            base_price = random.uniform(1.0800, 1.1200)
        
        # Create signal data
        signal_data = {}
        if signal_type in ['BUY', 'SELL']:
            entry_price = base_price
            
            if signal_type == 'BUY':
                tp_price = entry_price + random.uniform(10, 50)
                sl_price = entry_price - random.uniform(5, 20)
            else:  # SELL
                tp_price = entry_price - random.uniform(10, 50)
                sl_price = entry_price + random.uniform(5, 20)
            
            signal_data = {
                "signal_type": signal_type,
                "symbol": symbol,
                "entry_price": round(entry_price, 5),
                "tp_price": round(tp_price, 5),
                "sl_price": round(sl_price, 5),
                "reason": f"AI analysis suggests {signal_type} signal based on technical indicators"
            }
        
        # Create analysis text
        analysis_texts = [
            "Based on technical analysis, the market shows strong bullish momentum with RSI above 60 and MACD crossing above signal line.",
            "Bearish divergence detected on multiple timeframes. Price action suggests potential downward movement.",
            "Market consolidation observed. Recommend waiting for clearer directional signals before entering positions.",
            "Strong support level holding at current price. Bullish reversal pattern forming on H1 timeframe.",
            "Resistance level tested multiple times. Expecting potential breakout or reversal in coming sessions."
        ]
        
        analysis = random.choice(analysis_texts)
        
        # Create history entry
        history_entry = {
            "error": False,
            "message": "AI analysis completed successfully",
            "symbol": symbol,
            "timeframes": tf,
            "ai_provider": ai_provider,
            "analysis": analysis,
            "prompt": f"Analyze the {symbol} trading data for timeframes {tf}.",
            "image_analyzed": False,
            "use_signal_format": 1 if signal_type in ['BUY', 'SELL'] else 0,
            "timestamp": timestamp.isoformat(),
            "signal_data": signal_data
        }
        
        # Save to file
        filename = f"{bot_name}_{timestamp_str}.json"
        filepath = os.path.join(logs_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(history_entry, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Created 50 sample history entries for {bot_name}")
    print(f"📁 Files saved in: {logs_dir}")
    
    # Show summary
    print("\n📊 Sample Data Summary:")
    print(f"- Signal Types: {', '.join(signal_types)}")
    print(f"- Symbols: {', '.join(symbols)}")
    print(f"- Timeframes: {timeframes}")
    print(f"- AI Providers: {', '.join(ai_providers)}")
    print(f"- Date Range: Last 90 days")
    
    print("\n🎯 To test the history feature:")
    print("1. Run the main application")
    print("2. Go to AI Bots tab")
    print("3. Find 'My XAUUSD Bot' (or create one with this name)")
    print("4. Click the '📊 History' button")
    print("5. View paginated history with performance summary")

if __name__ == "__main__":
    create_sample_history()
