import logging
import os
from datetime import datetime
from typing import Optional, Any

class DebugLogger:
    """
    Debug logging utility that handles both DEBUG=true (detailed) and DEBUG=false (compact) modes
    """
    
    def __init__(self, config, util=None):
        self.config = config
        self.util = util
        self.debug_mode = config.DEBUG
        
        # Setup file logger
        self.setup_file_logger()
        
    def setup_file_logger(self):
        """Setup file logger for error tracking"""
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
            
        log_file = os.path.join(log_dir, f"trading_app_{datetime.now().strftime('%Y%m%d')}.log")
        
        # Configure file logger
        self.file_logger = logging.getLogger('trading_app')
        self.file_logger.setLevel(logging.DEBUG)
        
        # Remove existing handlers to avoid duplicates
        for handler in self.file_logger.handlers[:]:
            self.file_logger.removeHandler(handler)
        
        # File handler
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        file_handler.setFormatter(formatter)
        self.file_logger.addHandler(file_handler)
        
    def log_toggle_action(self, action_type: str, group_name: str, enabled: bool):
        """Log toggle actions (SL/TP enable/disable)"""
        status = "enabled" if enabled else "disabled"
        
        if self.debug_mode:
            # Detailed debug mode
            message = f"🔄 Toggle {action_type} for {group_name}: {status}"
            details = f"DEBUG: Toggle action - Type: {action_type}, Group: {group_name}, Status: {enabled}"
            
            print(details)
            self.file_logger.info(details)
            if self.util:
                self.util.add_status_frame(message)
        else:
            # Compact mode
            message = f"🔄 {action_type} {group_name}: {status}"
            if self.util:
                self.util.add_status_frame(message)
            self.file_logger.info(f"Toggle {action_type} {group_name}: {status}")
    
    def log_order_processing(self, symbol: str, group_prefix: str, total_orders: int, 
                           processed_orders: int, action_type: str = "SL/TP"):
        """Log order processing results"""
        if self.debug_mode:
            # Detailed debug mode
            message = f"📦 {action_type} Processing: {symbol} ({group_prefix or 'All'}) - {processed_orders}/{total_orders} orders"
            details = f"DEBUG: Order processing - Symbol: {symbol}, Group: {group_prefix}, Total: {total_orders}, Processed: {processed_orders}, Action: {action_type}"
            
            print(details)
            self.file_logger.info(details)
            if self.util:
                self.util.add_status_frame(message)
        else:
            # Compact mode
            if processed_orders > 0:
                group_display = group_prefix if group_prefix else "All"
                message = f"📦 {group_display}: {processed_orders}/{total_orders} {action_type}"
                if self.util:
                    self.util.add_status_frame(message)
            self.file_logger.info(f"{action_type} {symbol} {group_prefix}: {processed_orders}/{total_orders}")
    
    def log_order_action(self, ticket: int, action: str, old_value: float, new_value: float, 
                        value_type: str = "SL", success: bool = True):
        """Log individual order actions (SL/TP changes)"""
        if self.debug_mode:
            # Detailed debug mode
            status = "✅" if success else "❌"
            message = f"{status} {action}: #{ticket} {value_type} {old_value:.5f} → {new_value:.5f}"
            details = f"DEBUG: Order action - Ticket: {ticket}, Action: {action}, {value_type}: {old_value} -> {new_value}, Success: {success}"
            
            print(details)
            self.file_logger.info(details)
            if self.util:
                self.util.add_status_frame(message)
        else:
            # Compact mode - only log to file, no status frame for individual orders
            self.file_logger.info(f"{action} #{ticket} {value_type}: {old_value} -> {new_value} {'OK' if success else 'FAIL'}")
    
    def log_magic_number_info(self, ticket: int, magic_number: int, original_sl_points: int, 
                             calculated_distance: float, symbol: str):
        """Log magic number usage for SL distance calculation"""
        if self.debug_mode:
            details = f"DEBUG: Magic number usage - Ticket: {ticket}, Magic: {magic_number}, SL Points: {original_sl_points}, Distance: {calculated_distance:.5f}, Symbol: {symbol}"
            print(details)
            self.file_logger.debug(details)
            if self.util:
                self.util.add_status_frame(f"🔢 #{ticket}: Magic={magic_number}, SL Points={original_sl_points}")
        else:
            # Only log to file in compact mode
            self.file_logger.debug(f"Magic #{ticket}: {magic_number} -> {original_sl_points}pts = {calculated_distance:.5f}")
    
    def log_error(self, error: Exception, context: str = "", ticket: Optional[int] = None):
        """Log errors with full details"""
        error_msg = f"ERROR in {context}: {str(error)}"
        
        if ticket:
            error_msg = f"#{ticket} - {error_msg}"
        
        # Always log errors to file with full details
        self.file_logger.error(error_msg, exc_info=True)
        
        if self.debug_mode:
            # Show detailed error in debug mode
            print(f"❌ {error_msg}")
            if self.util:
                self.util.add_status_frame(f"❌ {error_msg}", "red")
        else:
            # Compact error message
            compact_msg = f"❌ Error: {context}"
            if ticket:
                compact_msg = f"❌ #{ticket}: {context} failed"
            if self.util:
                self.util.add_status_frame(compact_msg, "red")
    
    def log_info(self, message: str, show_in_status: bool = True):
        """Log general information"""
        self.file_logger.info(message)
        
        if self.debug_mode:
            print(f"INFO: {message}")
        
        if show_in_status and self.util:
            self.util.add_status_frame(message)
    
    def debug_print(self, message: str):
        """Print debug message only in debug mode"""
        if self.debug_mode:
            print(f"DEBUG: {message}")
            self.file_logger.debug(message)
