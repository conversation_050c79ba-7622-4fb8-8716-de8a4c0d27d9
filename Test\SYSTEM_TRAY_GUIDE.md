# System Tray Functionality Guide

## Problem with RBTray.exe

When you use RBTray.exe to minimize your app to the system tray, it causes several issues:

1. **Webhook Server Disconnection** - The Flask webhook server stops working
2. **Threading Issues** - Background threads get interrupted
3. **Application Unresponsiveness** - The app becomes unresponsive when restored
4. **Memory Issues** - Can cause memory leaks and crashes

## Solution: Built-in System Tray

I've implemented a proper built-in system tray functionality that:

- ✅ **Preserves webhook server** - Flask continues running in background
- ✅ **Maintains all threads** - No interruption to background processes
- ✅ **Stays responsive** - App remains fully functional when restored
- ✅ **Shows webhook status** - Right-click tray icon to check webhook status
- ✅ **Clean exit** - Proper cleanup when exiting from tray

## Installation

### Step 1: Install Dependencies
Run the installation script:
```bash
# Double-click this file:
install_tray_support.bat
```

Or install manually:
```bash
# Activate your virtual environment first
venv\Scripts\activate
pip install pystray Pillow
```

### Step 2: Restart Your Application
After installing dependencies, restart your app using:
```bash
# Double-click:
start_app.bat
```

## How to Use

### 1. **Minimize to Tray**
- Click the "📍 Minimize to Tray" button in your app
- The app window will disappear and appear in the system tray (bottom-right corner)
- Your webhook server continues running in the background

### 2. **Restore from Tray**
- Right-click the tray icon
- Select "Show/Restore"
- Your app window will reappear exactly as you left it

### 3. **Check Webhook Status**
- Right-click the tray icon
- Select "Webhook Status"
- A notification will show if webhook is enabled/disabled

### 4. **Exit from Tray**
- Right-click the tray icon
- Select "Exit"
- App will close properly with full cleanup

## Features

### **Tray Icon**
- Uses your app's icon (App/icon.png) if available
- Falls back to a simple blue/white icon if no icon found
- Shows app name when you hover over it

### **Tray Menu**
- **Show/Restore**: Bring the app window back
- **Webhook Status**: Check if webhook is running
- **Exit**: Close the app completely

### **Background Operation**
- All background processes continue running
- Webhook server stays active and responsive
- Order refresh loops continue working
- MT5 connection remains stable

## Advantages over RBTray.exe

| Feature | RBTray.exe | Built-in Tray |
|---------|------------|---------------|
| Webhook Server | ❌ Stops working | ✅ Continues running |
| Background Threads | ❌ Gets interrupted | ✅ Keeps running |
| App Responsiveness | ❌ Becomes unresponsive | ✅ Stays responsive |
| Memory Management | ❌ Can cause leaks | ✅ Proper cleanup |
| Integration | ❌ External tool | ✅ Built-in feature |
| Webhook Status | ❌ No visibility | ✅ Shows status |
| Clean Exit | ❌ May not cleanup | ✅ Proper cleanup |

## Troubleshooting

### **"System tray not available" Error**
- Run `install_tray_support.bat` to install dependencies
- Or manually install: `pip install pystray Pillow`

### **Tray Icon Not Appearing**
- Check Windows system tray settings
- Look in the "hidden icons" area (click the up arrow in system tray)
- Some antivirus software may block tray icons

### **Can't Restore from Tray**
- Try double-clicking the tray icon
- If that doesn't work, right-click and select "Show/Restore"
- As a last resort, right-click and "Exit", then restart the app

### **Webhook Still Not Working**
- Check if the webhook was enabled before minimizing
- Look at the status messages in the app
- Test the webhook endpoint manually

## Best Practices

1. **Always use the built-in tray** instead of RBTray.exe
2. **Check webhook status** from the tray menu before important operations
3. **Exit properly** from the tray menu rather than killing the process
4. **Keep the app icon** (App/icon.png) for better visual identification

## Technical Details

The built-in system tray:
- Runs in a separate daemon thread
- Doesn't interfere with the main GUI thread
- Preserves all background processes
- Uses proper thread-safe operations
- Handles cleanup automatically

This solution eliminates all the problems you experienced with RBTray.exe while providing better functionality and integration with your application.
