from flask import Flask, request, jsonify
import MetaTrader5 as mt5
from dotenv import load_dotenv
import os

app = Flask(__name__)


# Initialize MetaTrader 5
load_dotenv()
login = int(os.getenv('LOGIN'))
pwd = os.getenv('PWD')
server = os.getenv('SERVER')
symbol = os.getenv('SYMBOL') 
path = "C:\\Program Files\\MetaTrader 5\\terminal64.exe"
strategy_name = "GUI"

# connect to MetaTrader5 Terminal
if not mt5.initialize(path=path):
    print("Initialize() failed, error code =", mt5.last_error())
    quit()
else:
    if mt5.login(login,pwd,server):
        print("logged in succesffully")
    else: 
        print("login failed, error code: {}".format(mt5.last_error()))

print(f"Logged in to {server} as {login}")

# ====================================================
# ====================================================


def place_trade(symbol, action, volume):
    
    # Check if the symbol is available in the market watch
    if not mt5.symbol_select(symbol, True): 
        mt5.shutdown()
        return
    symbol_info_tick = mt5.symbol_info_tick(symbol)

    """Place a trade based on action and volume."""
    # Determine trade type
    trade_type = mt5.ORDER_TYPE_BUY if action.lower() == "buy" else mt5.ORDER_TYPE_SELL
    price = symbol_info_tick.ask if trade_type == mt5.ORDER_TYPE_BUY else symbol_info_tick.bid
    sl = price - 5 if trade_type == mt5.ORDER_TYPE_BUY else price + 5
    tp = price + 10 if trade_type == mt5.ORDER_TYPE_BUY else price - 10

    # Create trade request
    request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": volume,
        "type": trade_type,
        "price": price,
        "sl": sl,
        "tp": tp,
        "deviation": 10,
        "magic": 123456,
        "comment": "Alert Trade",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }


    # Send order
    result = mt5.order_send(request)
    return result

@app.route('/webhook', methods=['POST'])
def webhook():
    """Handle incoming TradingView webhook alerts."""
    data = request.get_json(silent=True, force=True)
    if not data:
        return jsonify({"error": "Invalid data"}), 400
 
    symbol = data.get("symbol")
    action = data.get("action")
    volume = data.get("volume", 0.01)

    if not symbol or not action:
        return jsonify({"error": "Missing symbol or action"}), 400

    # Place trade
    result = place_trade(symbol, action, volume)
    if result.retcode != mt5.TRADE_RETCODE_DONE:
        return jsonify({"error": "Trade failed", "details": result._asdict()}), 400

    return jsonify({"message": "Trade executed", "details": result._asdict()}), 200

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)