import numpy as np
import pandas as pd
import talib as ta
import os
import MetaTrader5 as mt5
from time import sleep
from dotenv import load_dotenv

load_dotenv()
folder_dir = os.getenv('DIR')
login = int(os.getenv('LOGIN'))
pwd = os.getenv('PWD')
server = os.getenv('SERVER')
symbol = os.getenv('SYMBOL') 
path = "C:\\Program Files\\MetaTrader 5\\terminal64.exe"
strategy_name = "Candlestick Bot"

# connect to MetaTrader5 Terminal
if not mt5.initialize(path=path):
    print("Initialize() failed, error code =", mt5.last_error())
    quit()
else:
    if mt5.login(login,pwd,server):
        print("logged in succesffully")
    else: 
        print("login failed, error code: {}".format(mt5.last_error()))

print(f"Logged in to {server} as {login}")

# ตั้งค่าข้อมูล
symbol_info_tick = mt5.symbol_info_tick(symbol)
symbol_info = mt5.symbol_info(symbol)
timeframe = mt5.TIMEFRAME_M5
volume = 0.01
deviation=20
magic=30
stop_loss=0.0
take_profit=0.0
sl = 400
tp = 400
data_length = 500  # จำนวนแท่งเทียนที่ต้องการดึงข้อมูล

print('########################################################################################################\n')

def market_order(order_type, stop_loss, take_profit, strategy_name):
    symbol_info_tick = mt5.symbol_info_tick(symbol)

    order_type_dict = {
        'buy': mt5.ORDER_TYPE_BUY,
        'sell': mt5.ORDER_TYPE_SELL
    }

    price_dict = {
        'buy': symbol_info_tick.ask,
        'sell': symbol_info_tick.bid
    }

    request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": volume,  # FLOAT
        "type": order_type_dict[order_type],
        "price": price_dict[order_type],
        "sl": stop_loss,  # FLOAT
        "tp": take_profit,  # FLOAT
        "deviation": deviation,  # INTERGER
        "magic": magic,  # INTERGER
        "comment": strategy_name,
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,  # mt5.ORDER_FILLING_FOK if IOC does not work
    }
 
    print(f"Signal {order_type}, ask price {price_dict[order_type]}, SL {stop_loss}, TP {take_profit}")
    order_result = mt5.order_send(request)
    return (order_result)
         
         
def count_old_position():
    # Retrieve all open positions
    positions = mt5.positions_get()


def close_old_position(new_action): 
    # Retrieve all open positions
    positions = mt5.positions_get()

    if positions is None or len(positions) == 0:
        print("No open positions found.")
    else:
        print(f"Found {len(positions)} open positions.")
        for position in positions:
            symbol = position.symbol
            ticket = position.ticket
            lot_size = position.volume
            action = "sell" if position.type == mt5.ORDER_TYPE_BUY else "buy"

            if new_action != action:
                # Prepare the trade request
                request = {
                    "action": mt5.TRADE_ACTION_DEAL,
                    "symbol": symbol,
                    "volume": lot_size,
                    "type": mt5.ORDER_TYPE_SELL if position.type == mt5.ORDER_TYPE_BUY else mt5.ORDER_TYPE_BUY,
                    "position": ticket,
                    "price": symbol_info_tick.bid if action == "sell" else symbol_info_tick.ask,
                    "deviation": 20,
                    "magic": 123456,
                    "comment": "Close position",
                }

                # Send the trade request
                result = mt5.order_send(request)

                # Check the result
                if result.retcode == mt5.TRADE_RETCODE_DONE:
                    print(f"Successfully closed position {ticket} on {symbol}.")
                else:
                    print(f"Failed to close position {ticket} on {symbol}: {result.comment}")

def get_signal(symbol, timeframe):
    global strategy_name
    
    rates = mt5.copy_rates_from_pos(symbol, timeframe, 1, data_length)

    # Create a DataFrame
    df = pd.DataFrame(rates)
    df['time'] = pd.to_datetime(df['time'], unit='s')
    df.set_index('time', inplace=True)
    df['rsi'] = ta.RSI(df['close'], 14)
    rsi = df.iloc[-1];
 
    ohlc = mt5.copy_rates_from_pos(symbol, timeframe, 1, 3)
    ohlc_df = pd.DataFrame(ohlc)[['time', 'open', 'high', 'low', 'close']]
    ohlc_df['time'] = pd.to_datetime(ohlc_df['time'], unit='s')

    ohlc_df['open2'] = ohlc_df['open'].shift(2)     
    ohlc_df['high2'] = ohlc_df['high'].shift(2)     
    ohlc_df['low2'] = ohlc_df['low'].shift(2)       
    ohlc_df['close2'] = ohlc_df['close'].shift(2)   
    
    
    ohlc_df['open1'] = ohlc_df['open'].shift(1)     
    ohlc_df['high1'] = ohlc_df['high'].shift(1)     
    ohlc_df['low1'] = ohlc_df['low'].shift(1)       
    ohlc_df['close1'] = ohlc_df['close'].shift(1)   
    ohlc_df['rsi'] = rsi['rsi']
    
   
    candle = ohlc_df.iloc[-1];
    
    if candle['high1'] - candle['low1'] == 0:
        return False
    

    is_buy = False
    is_sell = False
    

    print(f"----------------------------------------")
    print(f" Check All ")
    print(f"----------------------------------------")
    
    buy_patterns = {
    "morning_star": is_morning_star(candle)
    }

    
    for key, pattern in buy_patterns.items():
        print(f"{key}: {pattern}")
        if pattern == True:
            is_buy = True
            strategy_name = key
            break
            
    sell_patterns = {
    "bearish_evening_star": is_bearish_evening_star(candle)
    }

    
    for key, pattern in sell_patterns.items():
        print(f"{key}: {pattern}")
        if pattern == True:
            is_sell = True
            strategy_name = key
            break        

    print(f"----------------------------------------")
    print(f" BUY: {is_buy}  |  SELL: {is_sell}  |  RSI: {candle['rsi']}")
    print(f"----------------------------------------")
    

    #print(candle)
    print(ohlc_df)
    
    if is_buy:
        return 'buy'
    elif is_sell:
        return 'sell'
    else:
        return False
   
#sell
def is_bearish_evening_star(df):
    # เงื่อนไขสำหรับแท่งเทียนแต่ละวัน
    first_candle_bullish = df['close2'] > df['open2']
    small_body_second_day = abs(df['close1'] - df['open1']) <= (df['high1'] - df['low1']) / 3
    third_candle_bearish = df['close'] < df['open']
    
    # ตำแหน่งของแท่งเทียนวันที่สองและสาม
    second_candle_gap_up = df['open1'] > df['close2']
    third_candle_closes_below_first_midpoint = df['close'] < (df['open2'] + (df['close2'] - df['open2']) / 2)
    
    # ตรวจสอบทั้งสามแท่งเทียน
    evening_star = first_candle_bullish & small_body_second_day & second_candle_gap_up & third_candle_bearish & third_candle_closes_below_first_midpoint
    return evening_star
    
#buy
def is_morning_star(df):

    # เงื่อนไขสำหรับแท่งเทียนแต่ละวัน
    first_candle_bearish = df['close2'] > df['open2']
    small_body_second_day = abs(df['close1'] - df['open1']) <= (df['high1'] - df['low1']) / 3
    third_candle_bullish = df['close'] > df['open']
    
    # ตำแหน่งของแท่งเทียนวันที่สองและสาม
    second_candle_gap_down = df['open1'] < df['close2']
    third_candle_closes_above_first_midpoint = df['close'] > (df['open2'] + (df['close2'] - df['open2']) / 2)
    
    # ตรวจสอบทั้งสามแท่งเทียน
    morning_star = first_candle_bearish & small_body_second_day & second_candle_gap_down & third_candle_bullish & third_candle_closes_above_first_midpoint
    return morning_star
    
if __name__ == '__main__': 

    trading_allowed = True
    while trading_allowed:
        symbol_info_tick = mt5.symbol_info_tick(symbol)

        # Careful! Loop can open infinite positions!

        signal = get_signal(symbol, timeframe)
        if signal == 'buy': # and mt5.positions_total() == 0:
            price = symbol_info_tick.ask
            point = symbol_info.point
            stop_loss = price - sl * point
            take_profit = price + tp * point
            res = market_order('buy', stop_loss, take_profit, strategy_name)
            trading_allowed = False 

        elif signal == 'sell': # and mt5.positions_total() == 0:
            point = symbol_info.point
            price = symbol_info_tick.bid
            stop_loss = price + sl * point
            take_profit = price - tp * point
            res = market_order('sell', stop_loss, take_profit, strategy_name)
            trading_allowed = False 

        else :
            print('Signal', signal)

        print('########################################################################################################\n')

        sleep(1)
