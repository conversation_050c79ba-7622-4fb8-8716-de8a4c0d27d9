#!/usr/bin/env python3
"""
Debug script to identify the webhook AI analysis error
"""

import os
import sys
import traceback

# Load environment variables first
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ Environment variables loaded from .env file")
except ImportError:
    # Manual loading
    env_path = os.path.join(os.getcwd(), '.env')
    if os.path.exists(env_path):
        with open(env_path, 'r') as f:
            for line in f:
                if '=' in line and not line.strip().startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
        print("✅ Environment variables loaded manually from .env file")

def test_mt5_connection():
    """Test MT5 connection"""
    print("🔌 Testing MT5 Connection...")
    try:
        import MetaTrader5 as mt5
        
        if not mt5.initialize():
            print("❌ MT5 initialization failed")
            return False
        
        # Test getting some basic info
        account_info = mt5.account_info()
        if account_info is None:
            print("❌ MT5 account info failed")
            return False
        
        print(f"✅ MT5 connected - Account: {account_info.login}")
        return True
        
    except Exception as e:
        print(f"❌ MT5 connection error: {e}")
        return False

def test_chart_data_retrieval():
    """Test chart data retrieval"""
    print("\n📊 Testing Chart Data Retrieval...")
    try:
        from App.config import Config
        from App.util import Util
        
        config = Config()
        util = Util(config)
        
        # Test the exact same parameters from the failing request
        symbol = "XAUUSD"
        timeframes = ["H1"]
        barback = 50
        
        print(f"   Testing: {symbol} {timeframes} {barback} bars")
        
        chart_data_result = util.get_multi_timeframe_data(symbol, timeframes, barback)
        
        if chart_data_result:
            print("✅ Chart data retrieval successful")
            print(f"   - Symbol: {chart_data_result.get('symbol')}")
            print(f"   - Timeframes: {chart_data_result.get('timeframes')}")
            print(f"   - Total bars: {chart_data_result.get('total_bars')}")
            return True
        else:
            print("❌ Chart data retrieval failed")
            return False
            
    except Exception as e:
        print(f"❌ Chart data retrieval error: {e}")
        traceback.print_exc()
        return False

def test_openai_api():
    """Test OpenAI API connection"""
    print("\n🤖 Testing OpenAI API...")
    try:
        from App.config import Config
        from App.util import Util
        
        config = Config()
        util = Util(config)
        
        # Test simple GPT API call
        test_prompt = "Test connection - respond with 'OK'"
        
        result = util.call_gpt_api(test_prompt)
        
        if result.get("error"):
            print(f"❌ OpenAI API failed: {result.get('message')}")
            return False
        else:
            print("✅ OpenAI API connection successful")
            print(f"   - Response: {result.get('analysis', '')[:50]}...")
            return True
            
    except Exception as e:
        print(f"❌ OpenAI API error: {e}")
        traceback.print_exc()
        return False

def test_full_ai_analysis():
    """Test the complete AI analysis flow"""
    print("\n🧠 Testing Full AI Analysis Flow...")
    try:
        from App.config import Config
        from App.util import Util
        
        config = Config()
        util = Util(config)
        
        # Test with the exact same parameters from the failing request
        symbol = "XAUUSD"
        timeframes = ["H1"]
        barback = 50
        custom_prompt = "Look for swing trading opportunities with 1-3 day holding periods. Consider daily and 4H timeframe confluence."
        ai_provider = "gpt"
        image_url = ""
        use_signal_format = True
        
        print(f"   Testing full analysis: {symbol} {timeframes}")
        
        # Step 1: Get chart data
        chart_data_result = util.get_multi_timeframe_data(symbol, timeframes, barback)
        if not chart_data_result:
            print("❌ Step 1 failed: Chart data retrieval")
            return False
        
        print("✅ Step 1 passed: Chart data retrieved")
        
        # Step 2: Perform AI analysis
        analysis_result = util.perform_ai_analysis(
            chart_data_result,
            custom_prompt,
            ai_provider,
            image_url,
            symbol,
            timeframes,
            use_signal_format
        )
        
        if analysis_result.get("error"):
            print(f"❌ Step 2 failed: AI analysis - {analysis_result.get('message')}")
            return False
        
        print("✅ Step 2 passed: AI analysis completed")
        print(f"   - AI Provider: {analysis_result.get('ai_provider')}")
        print(f"   - Analysis length: {len(analysis_result.get('analysis', ''))} chars")
        print(f"   - Structured signal: {analysis_result.get('structured_signal', False)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Full AI analysis error: {e}")
        traceback.print_exc()
        return False

def test_webhook_simulation():
    """Simulate the webhook request"""
    print("\n🌐 Simulating Webhook Request...")
    try:
        import requests
        import json
        
        # The exact data from the failing request
        webhook_data = {
            "symbol": "XAUUSD",
            "timeframes": ["H1"],
            "barback": 50,
            "prompt": "Look for swing trading opportunities with 1-3 day holding periods. Consider daily and 4H timeframe confluence.",
            "ai": "gpt",
            "image": "",
            "use_signal_format": True
        }
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer test_token",
            "X-Access-Token": "test_access_token"
        }
        
        print("   Sending request to localhost:5050...")
        
        response = requests.post(
            "http://localhost:5050/webhook_ai_analysis",
            json=webhook_data,
            headers=headers,
            timeout=120
        )
        
        print(f"   Response status: {response.status_code}")
        print(f"   Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Webhook request successful")
            print(f"   - Error: {result.get('error', 'N/A')}")
            print(f"   - Message: {result.get('message', 'N/A')}")
            return True
        else:
            print(f"❌ Webhook request failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Webhook simulation error: {e}")
        traceback.print_exc()
        return False

def check_dependencies():
    """Check if all required dependencies are available"""
    print("📦 Checking Dependencies...")
    
    required_modules = [
        'MetaTrader5',
        'requests',
        'pandas',
        'talib',
        'numpy',
        'flask',
        'customtkinter'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"   ✅ {module}")
        except ImportError:
            print(f"   ❌ {module} - MISSING")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n❌ Missing modules: {missing_modules}")
        return False
    else:
        print("✅ All dependencies available")
        return True

def main():
    """Run comprehensive webhook debugging"""
    print("🔍 Webhook AI Analysis Error Debugging")
    print("=" * 50)
    
    # Run all diagnostic tests
    tests = [
        ("Dependencies Check", check_dependencies),
        ("MT5 Connection", test_mt5_connection),
        ("Chart Data Retrieval", test_chart_data_retrieval),
        ("OpenAI API", test_openai_api),
        ("Full AI Analysis", test_full_ai_analysis),
        ("Webhook Simulation", test_webhook_simulation)
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            traceback.print_exc()
            results[test_name] = False
    
    # Summary
    print("\n📋 Diagnostic Results:")
    print("=" * 30)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  - {test_name}: {status}")
    
    # Identify likely cause
    print("\n🔍 Likely Causes of 500 Error:")
    
    if not results.get("Dependencies Check", False):
        print("  🎯 Missing Python dependencies")
    
    if not results.get("MT5 Connection", False):
        print("  🎯 MT5 connection or initialization issue")
    
    if not results.get("Chart Data Retrieval", False):
        print("  🎯 Chart data retrieval failing")
    
    if not results.get("OpenAI API", False):
        print("  🎯 OpenAI API key or quota issue")
    
    if not results.get("Full AI Analysis", False):
        print("  🎯 AI analysis pipeline error")
    
    print("\n💡 Recommended Actions:")
    print("1. Check the webhook server logs for detailed error messages")
    print("2. Ensure MT5 is running and connected")
    print("3. Verify OpenAI API key and quota")
    print("4. Check if all required Python packages are installed")
    print("5. Test individual components before running full analysis")

if __name__ == "__main__":
    main()
