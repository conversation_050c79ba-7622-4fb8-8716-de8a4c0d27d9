#!/usr/bin/env python3
"""
Test script for Trailing SL Function Move and Webhook Integration
Demonstrates the moved set_trailing_sl function and webhook support
"""

import json
import requests

def demonstrate_trailing_sl_integration():
    """Demonstrate the trailing SL function move and webhook integration"""
    
    print("=" * 80)
    print("TRAILING SL FUNCTION MOVED TO UTIL.PY + WEBHOOK SUPPORT")
    print("=" * 80)
    
    print("\n📋 CHANGES MADE:")
    print("-" * 80)
    print("1. ✅ Moved set_trailing_sl() function from tab_control.py to util.py")
    print("2. ✅ Updated Control tab to use util.set_trailing_sl() wrapper")
    print("3. ✅ Added webhook support for trailing SL via /webhook_control")
    print("4. ✅ Enhanced function with symbol conversion and error handling")
    
    print("\n🔧 FUNCTION LOCATION:")
    print("-" * 80)
    print("File: App/util.py")
    print("Function: set_trailing_sl(symbol, order_type='all')")
    print()
    print("Parameters:")
    print("  • symbol: Trading symbol (e.g., 'XAUUSD', 'XU')")
    print("  • order_type: 'all', 'buy', or 'sell'")
    print()
    print("Returns:")
    print("  • Result dictionary from update_SL_to_BE_by_point()")
    print("  • None if error occurred")
    
    print("\n🎯 CONTROL TAB INTEGRATION:")
    print("-" * 80)
    print("File: App/tab_control.py")
    print("Function: set_trailing_sl(order_type='all') - Wrapper function")
    print()
    print("Implementation:")
    print("```python")
    print("def set_trailing_sl(self, order_type='all'):")
    print("    symbol = self.util.get_symbol(self.symbol_var)")
    print("    return self.util.set_trailing_sl(symbol, order_type)")
    print("```")
    print()
    print("Buttons:")
    print("  🟣 Set Trailing SL        → calls util.set_trailing_sl(symbol, 'all')")
    print("  🟢 Set BUY Trailing SL    → calls util.set_trailing_sl(symbol, 'buy')")
    print("  🔴 Set SELL Trailing SL   → calls util.set_trailing_sl(symbol, 'sell')")
    
    print("\n🌐 WEBHOOK INTEGRATION:")
    print("-" * 80)
    print("Endpoint: POST /webhook_control")
    print("Action: 'trailing-sl'")
    print()
    print("Request Format:")
    print("```json")
    print("{")
    print('  "s": "XU",           // Symbol (short form)')
    print('  "a": "trailing-sl",  // Action')
    print('  "c": "all"           // Condition: all, buy, sell')
    print("}")
    print("```")
    print()
    print("Implementation in webhook_control():")
    print("```python")
    print("elif a == 'trailing-sl':")
    print("    result = self.util.set_trailing_sl(symbol, condition)")
    print("    if result:")
    print("        processed_count = result.get('All_FT', 0)")
    print("        # Show success message")
    print("    else:")
    print("        # Show warning message")
    print("```")
    
    print("\n💡 WEBHOOK EXAMPLES:")
    print("-" * 80)
    
    print("Example 1 - Apply trailing SL to all XAUUSD positions:")
    webhook_example_1 = {
        "s": "XU",
        "a": "trailing-sl", 
        "c": "all"
    }
    print("POST /webhook_control")
    print("Headers: Authorization: Bearer YOUR_TOKEN")
    print("         X-Access-Token: YOUR_ACCESS_TOKEN")
    print("Body:", json.dumps(webhook_example_1, indent=2))
    print()
    
    print("Example 2 - Apply trailing SL to BUY positions only:")
    webhook_example_2 = {
        "s": "EU",
        "a": "trailing-sl",
        "c": "buy"
    }
    print("POST /webhook_control")
    print("Body:", json.dumps(webhook_example_2, indent=2))
    print()
    
    print("Example 3 - Apply trailing SL to SELL positions only:")
    webhook_example_3 = {
        "s": "GU", 
        "a": "trailing-sl",
        "c": "sell"
    }
    print("POST /webhook_control")
    print("Body:", json.dumps(webhook_example_3, indent=2))
    
    print("\n🔍 FUNCTION FEATURES:")
    print("-" * 80)
    print("✅ Symbol Conversion:")
    print("   • Converts short symbols (XU → XAUUSD)")
    print("   • Handles full symbols (XAUUSD → XAUUSD)")
    print()
    print("✅ Trailing Type Detection:")
    print("   • Auto-detects from Orders tab")
    print("   • Falls back to 'Fixed' if not available")
    print("   • Supports both Fixed and ATR trailing")
    print()
    print("✅ Comprehensive Status Messages:")
    print("   • Start message (cyan)")
    print("   • Success message (green)")
    print("   • Warning message (yellow)")
    print("   • Error message (red)")
    print()
    print("✅ Error Handling:")
    print("   • Try-catch for all operations")
    print("   • Graceful fallbacks")
    print("   • Detailed error reporting")
    
    print("\n📊 STATUS MESSAGE EXAMPLES:")
    print("-" * 80)
    print("Start Message:")
    print("  🔄 Manual trailing SL calculation started for XAUUSD (ALL) - Type: ATR")
    print()
    print("Success Message:")
    print("  ✅ Trailing SL applied to 3 ALL positions for XU")
    print()
    print("Warning Message:")
    print("  ℹ️ No ALL positions found or conditions not met for XU")
    print()
    print("Error Message:")
    print("  ❌ Error in trailing SL calculation for XU: Connection timeout")
    print()
    print("Webhook Success Message:")
    print("  ✅ Webhook Control: Trailing SL applied to 2 BUY positions for XAUUSD")
    
    print("\n⚙️ TECHNICAL IMPLEMENTATION:")
    print("-" * 80)
    print("Function Flow:")
    print("1. Detect trailing stop type from Orders tab")
    print("2. Show start status message")
    print("3. Convert symbol if needed (XU → XAUUSD)")
    print("4. Call update_SL_to_BE_by_point() with trailing enabled")
    print("5. Process results and show appropriate status")
    print("6. Return result dictionary")
    print()
    print("Symbol Conversion:")
    print("  • Uses get_symbol_from_short() for 2-character symbols")
    print("  • Passes through longer symbols unchanged")
    print("  • Handles common pairs: XU, EU, GU, etc.")
    print()
    print("Trailing Type Detection:")
    print("  • Checks config.orders_tab_ref.trailing_stop_type")
    print("  • Falls back to 'Fixed' if Orders tab not available")
    print("  • Works with both UI and webhook calls")
    
    print("\n🌐 WEBHOOK AUTHENTICATION:")
    print("-" * 80)
    print("Required Headers:")
    print("  Authorization: Bearer YOUR_BEARER_TOKEN")
    print("  X-Access-Token: YOUR_ACCESS_TOKEN")
    print()
    print("Both tokens must be valid for webhook to process the request.")
    print("Invalid authentication returns 401 Unauthorized.")
    
    print("\n🧪 TESTING SCENARIOS:")
    print("-" * 80)
    print("1. Control Tab Testing:")
    print("   • Click each trailing SL button")
    print("   • Verify status messages appear")
    print("   • Check Orders tab for SL updates")
    print("   • Test with different symbols")
    print()
    print("2. Webhook Testing:")
    print("   • Send POST requests with different conditions")
    print("   • Test authentication")
    print("   • Verify response format")
    print("   • Check status messages in app")
    print()
    print("3. Integration Testing:")
    print("   • Test both Fixed and ATR trailing types")
    print("   • Mix Control tab and webhook calls")
    print("   • Verify symbol conversion works")
    print("   • Test error handling")
    
    print("\n✅ BENEFITS OF CENTRALIZED FUNCTION:")
    print("-" * 80)
    print("• Single source of truth for trailing SL logic")
    print("• Consistent behavior across UI and webhook")
    print("• Easier maintenance and updates")
    print("• Shared error handling and status messages")
    print("• Unified symbol conversion")
    print("• Better code organization")
    
    print("\n⚠️ CURRENT LIMITATIONS:")
    print("-" * 80)
    print("• BUY/SELL filtering not fully implemented yet")
    print("• Currently processes all positions regardless of order_type")
    print("• TODO: Add proper order type filtering")
    print("• Webhook condition parameter ready for future enhancement")
    
    print("\n🚀 FUTURE ENHANCEMENTS:")
    print("-" * 80)
    print("1. Implement proper BUY/SELL filtering")
    print("2. Add more granular control options")
    print("3. Support for comment-based filtering")
    print("4. Batch processing for multiple symbols")
    print("5. Advanced webhook parameters")
    
    print("\n" + "=" * 80)
    print("TRAILING SL FUNCTION SUCCESSFULLY MOVED AND WEBHOOK ENABLED!")
    print("=" * 80)
    
    print("\n💡 Now you can trigger trailing SL calculations from:")
    print("   • Control tab buttons (manual)")
    print("   • Webhook calls (automated)")
    print("   • Both use the same centralized function!")

def show_webhook_curl_examples():
    """Show curl command examples for webhook testing"""
    
    print("\n" + "=" * 80)
    print("WEBHOOK CURL COMMAND EXAMPLES")
    print("=" * 80)
    
    print("\n🌐 Basic Curl Commands:")
    print("-" * 80)
    
    print("1. Apply trailing SL to all XAUUSD positions:")
    print("```bash")
    print("curl -X POST http://localhost:5000/webhook_control \\")
    print("  -H 'Content-Type: application/json' \\")
    print("  -H 'Authorization: Bearer YOUR_BEARER_TOKEN' \\")
    print("  -H 'X-Access-Token: YOUR_ACCESS_TOKEN' \\")
    print("  -d '{")
    print('    "s": "XU",')
    print('    "a": "trailing-sl",')
    print('    "c": "all"')
    print("  }'")
    print("```")
    print()
    
    print("2. Apply trailing SL to BUY positions only:")
    print("```bash")
    print("curl -X POST http://localhost:5000/webhook_control \\")
    print("  -H 'Content-Type: application/json' \\")
    print("  -H 'Authorization: Bearer YOUR_BEARER_TOKEN' \\")
    print("  -H 'X-Access-Token: YOUR_ACCESS_TOKEN' \\")
    print("  -d '{")
    print('    "s": "EU",')
    print('    "a": "trailing-sl",')
    print('    "c": "buy"')
    print("  }'")
    print("```")
    print()
    
    print("3. Apply trailing SL to SELL positions only:")
    print("```bash")
    print("curl -X POST http://localhost:5000/webhook_control \\")
    print("  -H 'Content-Type: application/json' \\")
    print("  -H 'Authorization: Bearer YOUR_BEARER_TOKEN' \\")
    print("  -H 'X-Access-Token: YOUR_ACCESS_TOKEN' \\")
    print("  -d '{")
    print('    "s": "GU",')
    print('    "a": "trailing-sl",')
    print('    "c": "sell"')
    print("  }'")
    print("```")
    
    print("\n📝 Expected Response:")
    print("-" * 80)
    print("Success Response (200 OK):")
    print("```json")
    print("{")
    print('  "error": false,')
    print('  "message": "Success"')
    print("}")
    print("```")
    print()
    print("Error Response (401 Unauthorized):")
    print("```json")
    print("{")
    print('  "error": true,')
    print('  "message": "Access denied: Invalid token"')
    print("}")
    print("```")

if __name__ == "__main__":
    demonstrate_trailing_sl_integration()
    show_webhook_curl_examples()
