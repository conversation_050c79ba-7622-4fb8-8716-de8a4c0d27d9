import pandas as pd
from opencage.geocoder import OpenCageGeocode
import time
import os

# === SETTINGS ===
api_key = 'aca111fc28134bd58ea0e60ace5dfad3'  # Replace with your API key
current_dir = os.path.dirname(os.path.abspath(__file__))
file_path = os.path.join(current_dir, "addresses.xlsx")  # Change this to your actual file path
sheet_name = "Sheet1"         # Change if needed
address_column = 'address'         # Change to match your column name
output_file = os.path.join(current_dir, "geocoded-opencage.xlsx") 

# === INIT ===
geocoder = OpenCageGeocode(api_key)

# === Load Excel ===
df = pd.read_excel(file_path, sheet_name=sheet_name)

# === Geocoding Function with Retry ===
def get_lat_lon(address, retries=3):
    for i in range(retries):
        try:
            result = geocoder.geocode(address)
            if result and len(result):
                lat = result[0]['geometry']['lat']
                lon = result[0]['geometry']['lng']
                return pd.Series([lat, lon])
            else:
                return pd.Series([None, None])
        except Exception as e:
            print(f"Error for '{address}': {e} (retry {i+1})")
            time.sleep(2 ** i)  # exponential backoff
    return pd.Series([None, None])

# === Apply to all addresses ===
df[['lat', 'lon']] = df[address_column].apply(get_lat_lon)

# === Save Results ===
df.to_excel(output_file, index=False)
print(f"Saved to: {output_file}")
