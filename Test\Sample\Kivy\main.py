from kivy.app import App
from kivy.lang import Builder
from kivy.properties import StringProperty
from dotenv import load_dotenv
import os
import MetaTrader5 as mt5

load_dotenv()

accounts = {
    "Demo 1": "DEMO1",
    "Real 1": "REAL1",
    "Real 2": "REAL2"
}

SYMBOL = os.getenv("SYMBOL", "EURUSD")
PATH = "C:\\Program Files\\MetaTrader 5\\terminal64.exe"

class TradingApp(App):
    status_text = StringProperty("🔌 Status: Not connected")
    result_text = StringProperty("")
    symbol_text = StringProperty(f"Symbol: {SYMBOL}")

    def build(self):
        return Builder.load_file("trading.kv")

    def connect_to_mt5(self, account_label):
        prefix = accounts[account_label]
        login = int(os.getenv(f"{prefix}_LOGIN"))
        pwd = os.getenv(f"{prefix}_PWD")
        server = os.getenv(f"{prefix}_SERVER")

        if not mt5.initialize(path=PATH):
            self.status_text = f"❌ Init failed: {mt5.last_error()}"
            return

        if mt5.login(login, pwd, server):
            self.status_text = f"✅ Connected to: {account_label}"
        else:
            self.status_text = f"❌ Login failed: {mt5.last_error()}"

    def trade(self, action):
        try:
            volume = float(self.root.ids.volume_input.text)
        except ValueError:
            self.result_text = "⚠️ Invalid volume"
            return

        price = mt5.symbol_info_tick(SYMBOL).ask if action == "buy" else mt5.symbol_info_tick(SYMBOL).bid
        order_type = mt5.ORDER_TYPE_BUY if action == "buy" else mt5.ORDER_TYPE_SELL

        request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": SYMBOL,
            "volume": volume,
            "type": order_type,
            "price": price,
            "deviation": 10,
            "magic": ********,
            "comment": "Kivy Trade",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_IOC,
        }

        result = mt5.order_send(request)
        if result.retcode == mt5.TRADE_RETCODE_DONE:
            self.result_text = f"✅ {action.upper()} OK: {result.order}"
        else:
            self.result_text = f"❌ {action.upper()} FAILED: {result.retcode}"

if __name__ == '__main__':
    TradingApp().run()
