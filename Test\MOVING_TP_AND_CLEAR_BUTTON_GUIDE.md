# Moving TP Functionality & Clear Button Guide

## Clear Button Added ✅

I've added a **"Clear All"** button to the Input tab that allows you to easily reset all TP fields.

### **Location:**
- **Input Tab** → <PERSON><PERSON> row → **"Clear All"** (orange button, left side of "Get Entry")

### **What it does:**
1. **Removes all existing TP fields**
2. **Clears all TP/Lot values**
3. **Adds one default TP field**
4. **Recalculates SL and TP with default values**
5. **Shows confirmation message**

### **How to use:**
1. Go to **Input Tab**
2. Click **"Clear All"** button (orange)
3. All TP fields are cleared and reset to default
4. You'll see: "🧹 All TP fields cleared and reset to default"

## Moving TP Functionality Explained

The `is_moving_tp = True` parameter in `update_SL_to_BE_by_point()` enables **advanced trailing stop functionality**.

### **How Moving TP Works:**

#### **Normal Mode (is_moving_tp = False):**
- **Simple Break Even**: Moves SL to entry price + small buffer when profitable
- **Static TP**: Take Profit stays at original level
- **One-time action**: SL moves once to break even, then stops

#### **Moving TP Mode (is_moving_tp = True):**
- **Progressive SL Movement**: SL moves up/down as price moves favorably
- **Dynamic TP Movement**: TP also moves to capture more profit
- **Continuous tracking**: Keeps adjusting as price moves

### **Technical Implementation:**

#### **For BUY Orders:**
```python
if is_moving_tp:
    while current_price >= entry_price + (factor * point_sl):
        new_sl = entry_price + ((factor - 1) * point_sl + point_10) 
        new_tp = entry_price + ((factor + 1) * point_sl + point_50) 
        factor += 1
```

#### **For SELL Orders:**
```python
if is_moving_tp:
    while current_price <= entry_price - (factor * point_sl):
        new_sl = entry_price - ((factor - 1) * point_sl + point_10) 
        new_tp = entry_price - ((factor + 1) * point_sl + point_50)
        factor += 1
```

### **Step-by-Step Example (BUY Order):**

**Initial Setup:**
- Entry Price: 1.1000
- SL Points: 100 (10 pips)
- Current SL: 1.0900
- Current TP: 1.1100

**Moving TP Progression:**

| Current Price | Factor | New SL | New TP | Action |
|---------------|--------|--------|--------|---------|
| 1.1200 | 2 | 1.1010 | 1.1350 | First move |
| 1.1300 | 3 | 1.1110 | 1.1450 | Second move |
| 1.1400 | 4 | 1.1210 | 1.1550 | Third move |
| 1.1500 | 5 | 1.1310 | 1.1650 | Fourth move |

### **Key Features:**

#### **1. Progressive Movement:**
- **SL moves up** by `(factor-1) * SL_points + 10 points`
- **TP moves up** by `(factor+1) * SL_points + 50 points`
- **Factor increases** with each profitable move

#### **2. Safety Buffers:**
- **SL Buffer**: +10 points above calculated level
- **TP Buffer**: +50 points above calculated level
- **Prevents premature exits** due to small price fluctuations

#### **3. Continuous Tracking:**
- **While loop** continues as long as price keeps moving favorably
- **Automatic adjustment** without manual intervention
- **Maximizes profit potential** in trending markets

### **When Moving TP is Useful:**

#### **✅ Good for:**
- **Trending markets** - Captures extended moves
- **Breakout trades** - Follows momentum
- **Swing trading** - Maximizes multi-day moves
- **Strong directional moves** - Rides the trend

#### **❌ Not ideal for:**
- **Ranging markets** - May give back profits
- **Scalping** - Too complex for quick trades
- **News trading** - Volatile price action
- **Low volatility periods** - Minimal movement

### **Current Usage in Your App:**

The moving TP functionality is **currently disabled** in your auto BE system:

```python
# In tab_orders.py process_auto_be method:
self.util.update_SL_to_BE_by_point(symbol, "", False)  # ❌ is_moving_tp = False
```

### **How to Enable Moving TP:**

#### **Option 1: Always Enable (Aggressive):**
```python
# Change in tab_orders.py:
self.util.update_SL_to_BE_by_point(symbol, "", True)  # ✅ Enable moving TP
```

#### **Option 2: Add Toggle Control (Recommended):**
Add a switch in the Orders tab to enable/disable moving TP:

```python
# Add to tab_orders.py build_control_panel:
self.moving_tp_switch = ctk.CTkSwitch(bottom_frame, text="Moving TP", command=self.toggle_moving_tp)
self.moving_tp_switch.pack(side="left", padx=5)

# Then use in process_auto_be:
self.util.update_SL_to_BE_by_point(symbol, "", self.moving_tp_enabled)
```

### **Performance Considerations:**

#### **Benefits:**
- **Maximizes profits** in trending markets
- **Automatic management** - no manual intervention
- **Risk reduction** - SL moves with price
- **Profit protection** - Locks in gains progressively

#### **Risks:**
- **May give back profits** in choppy markets
- **More MT5 API calls** - increased server load
- **Complex logic** - harder to debug
- **Overtrading** - frequent position modifications

### **Recommendation:**

1. **Test on demo first** to understand behavior
2. **Use with trending pairs** (EUR/USD, GBP/USD during trends)
3. **Monitor performance** compared to simple BE
4. **Consider market conditions** before enabling
5. **Add manual override** for specific trades

The moving TP feature is powerful but should be used carefully based on market conditions and trading strategy!
