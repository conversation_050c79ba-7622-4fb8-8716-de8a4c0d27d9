# AI Analysis Webhook Guide

## Overview

The new `/webhook_ai_analysis` endpoint provides direct AI-powered trading analysis by:
1. Retrieving multi-timeframe chart data from MT5
2. Processing technical indicators (EMA, RSI, MACD, etc.)
3. Sending data to AI APIs (GPT or Gemini) for analysis
4. Returning comprehensive trading insights

## Endpoint Details

**URL:** `POST /webhook_ai_analysis`
**Authentication:** Dual header authentication required
- `Authorization: Bearer <token>`
- `X-Access-Token: <access_token>`

## Request Format

```json
{
    "symbol": "XAUUSD",
    "timeframes": ["M15", "H1", "H4"],
    "barback": 50,
    "prompt": "Analyze gold trends and provide entry/exit signals",
    "ai": "gpt",
    "image": "http://example.com/chart.jpg"
}
```

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `symbol` | string | ✅ | Trading symbol (e.g., "XAUUSD", "EURUSD") |
| `timeframes` | array | ❌ | List of timeframes ["M15", "H1", "H4"] |
| `timeframe` | string | ❌ | Single timeframe (backward compatibility) |
| `barback` | integer | ❌ | Number of bars to analyze (default: 100) |
| `prompt` | string | ❌ | Custom analysis instructions |
| `ai` | string | ❌ | AI provider: "gpt" or "gemini" (default: "gpt") |
| `image` | string | ❌ | URL to chart image for visual analysis |

## Response Format

### Successful Response

```json
{
    "error": false,
    "message": "AI analysis completed successfully",
    "symbol": "XAUUSD",
    "timeframes": ["M15", "H1", "H4"],
    "ai_provider": "GPT",
    "analysis": "Based on the technical analysis of XAUUSD across multiple timeframes...",
    "chart_data": {
        "symbol": "XAUUSD",
        "timeframes": ["M15", "H1", "H4"],
        "total_bars": 150,
        "data": {
            "M15": [...],
            "H1": [...],
            "H4": [...]
        }
    },
    "custom_prompt": "Analyze gold trends and provide entry/exit signals",
    "image_analyzed": true,
    "timestamp": "2025-07-31T10:30:00"
}
```

### Error Response

```json
{
    "error": true,
    "message": "Missing symbol parameter"
}
```

## AI Providers

### OpenAI GPT
- **Model:** GPT-4 (configurable)
- **Features:** Text and image analysis
- **Setup:** Set `OPENAI_API_KEY` environment variable
- **Image Support:** GPT-4 Vision for chart image analysis

### Google Gemini
- **Model:** Gemini-Pro / Gemini-Pro-Vision
- **Features:** Text and image analysis
- **Setup:** Set `GEMINI_API_KEY` environment variable
- **Image Support:** Gemini-Pro-Vision for visual analysis

## Technical Indicators Included

The AI receives comprehensive technical analysis data:

- **Price Data:** OHLCV for each timeframe
- **EMA 20:** Exponential Moving Average
- **RSI 14:** Relative Strength Index (14 periods)
- **MACD:** Moving Average Convergence Divergence
- **RSI 25 + SMA 50:** RSI(25) with SMA(50) overlay
- **RSI 50 + SMA 25:** RSI(50) with SMA(25) overlay

## Usage Examples

### 1. Basic GPT Analysis

```bash
curl -X POST http://localhost:5000/webhook_ai_analysis \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token" \
  -H "X-Access-Token: your_access_token" \
  -d '{
    "symbol": "XAUUSD",
    "timeframes": ["H1", "H4"],
    "barback": 50,
    "ai": "gpt"
  }'
```

### 2. Gemini Analysis with Custom Prompt

```bash
curl -X POST http://localhost:5000/webhook_ai_analysis \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token" \
  -H "X-Access-Token: your_access_token" \
  -d '{
    "symbol": "XAUUSD",
    "timeframes": ["M15", "H1", "H4"],
    "barback": 100,
    "prompt": "Focus on RSI divergences and provide specific entry/exit levels",
    "ai": "gemini"
  }'
```

### 3. Analysis with Chart Image

```bash
curl -X POST http://localhost:5000/webhook_ai_analysis \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token" \
  -H "X-Access-Token: your_access_token" \
  -d '{
    "symbol": "XAUUSD",
    "timeframes": ["H4"],
    "barback": 50,
    "prompt": "Analyze both technical indicators and chart patterns",
    "ai": "gpt",
    "image": "https://example.com/xauusd_chart.jpg"
  }'
```

### 4. Python Integration

```python
import requests

def get_ai_analysis(symbol, timeframes, custom_prompt=""):
    url = "http://localhost:5000/webhook_ai_analysis"
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer your_token",
        "X-Access-Token": "your_access_token"
    }
    
    data = {
        "symbol": symbol,
        "timeframes": timeframes,
        "barback": 50,
        "prompt": custom_prompt,
        "ai": "gpt"
    }
    
    response = requests.post(url, json=data, headers=headers)
    return response.json()

# Usage
analysis = get_ai_analysis("XAUUSD", ["H1", "H4"], "Provide trading signals")
print(analysis["analysis"])
```

## Environment Setup

### 1. Set API Keys

```bash
# For OpenAI GPT
export OPENAI_API_KEY="sk-your-openai-key-here"

# For Google Gemini
export GEMINI_API_KEY="your-gemini-key-here"
```

### 2. Configure in Application

API keys can also be set in the application configuration:

```python
# In config.py
self.ai_api_config = {
    "gpt": {
        "api_key": "your_openai_key",
        "model": "gpt-4",
        "max_tokens": 1000
    },
    "gemini": {
        "api_key": "your_gemini_key", 
        "model": "gemini-pro",
        "max_tokens": 1000
    }
}
```

## Error Handling

Common error scenarios and responses:

| Error | HTTP Code | Description |
|-------|-----------|-------------|
| Missing symbol | 400 | Symbol parameter is required |
| Authentication failed | 401 | Invalid auth tokens |
| Webhook disabled | 200 | Webhook service is disabled |
| Chart data failed | 500 | MT5 data retrieval error |
| AI API error | 500 | AI service unavailable or API key invalid |
| Server error | 500 | Internal application error |

## Performance Considerations

- **Timeout:** 120 seconds for AI analysis
- **Rate Limiting:** Respect AI API rate limits
- **Caching:** Consider caching chart data for repeated requests
- **Concurrent Requests:** Limit concurrent AI API calls

## Security Notes

- **API Keys:** Store securely as environment variables
- **Authentication:** Always use dual header authentication
- **Image URLs:** Validate image URLs to prevent SSRF attacks
- **Input Validation:** All parameters are validated before processing

## Testing

Run the comprehensive test suite:

```bash
python test_ai_analysis_webhook.py
```

This tests:
- ✅ Basic GPT analysis
- ✅ Gemini analysis with custom prompts
- ✅ Image analysis capabilities
- ✅ Single timeframe compatibility
- ✅ Error handling scenarios

## Integration with Trading Bots

The AI analysis webhook can be integrated with automated trading systems:

1. **Scheduled Analysis:** Call endpoint periodically for market updates
2. **Signal Generation:** Use AI insights to generate trading signals
3. **Risk Management:** Incorporate AI analysis into risk assessment
4. **Multi-timeframe Confirmation:** Use multiple timeframes for signal confirmation

## Troubleshooting

### Common Issues

1. **"AI API key not configured"**
   - Set environment variables for API keys
   - Check config.py for hardcoded keys

2. **"Failed to retrieve chart data"**
   - Verify MT5 connection
   - Check symbol validity and market hours

3. **"Image download failed"**
   - Verify image URL accessibility
   - Check image format (PNG, JPG supported)

4. **Timeout errors**
   - AI analysis can take 30-60 seconds
   - Increase client timeout settings

### Debug Mode

Set log level to 5 (Debug) in the Account tab to see detailed operation logs for troubleshooting AI analysis requests.
