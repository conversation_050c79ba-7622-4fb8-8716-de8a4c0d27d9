#!/usr/bin/env python3
"""
Test webhook server connectivity and basic functionality
"""

import requests
import json
import time

def test_webhook_server_health():
    """Test if webhook server is running and responding"""
    print("🌐 Testing Webhook Server Health")
    print("=" * 35)
    
    # Test different possible endpoints and ports
    test_urls = [
        "http://localhost:5050",
        "http://localhost:5000",
        "http://127.0.0.1:5050",
        "http://127.0.0.1:5000"
    ]
    
    for base_url in test_urls:
        print(f"\n🔍 Testing: {base_url}")
        
        try:
            # Test basic connectivity
            response = requests.get(f"{base_url}/", timeout=5)
            print(f"   ✅ Server responding: {response.status_code}")
            
            # Test webhook endpoint
            try:
                webhook_response = requests.post(
                    f"{base_url}/webhook_ai_analysis",
                    json={"test": "ping"},
                    headers={"Content-Type": "application/json"},
                    timeout=5
                )
                print(f"   ✅ Webhook endpoint responding: {webhook_response.status_code}")
                
                if webhook_response.status_code == 401:
                    print(f"   ℹ️ Authentication required (expected)")
                elif webhook_response.status_code == 400:
                    print(f"   ℹ️ Bad request (expected for test data)")
                
                return base_url  # Found working server
                
            except requests.exceptions.Timeout:
                print(f"   ⚠️ Webhook endpoint timeout")
            except requests.exceptions.ConnectionError:
                print(f"   ❌ Webhook endpoint connection failed")
                
        except requests.exceptions.Timeout:
            print(f"   ⚠️ Server timeout")
        except requests.exceptions.ConnectionError:
            print(f"   ❌ Server connection failed")
    
    return None

def test_webhook_authentication():
    """Test webhook authentication"""
    print("\n🔐 Testing Webhook Authentication")
    print("=" * 35)
    
    base_url = "http://localhost:5050"
    
    # Test without authentication
    print("   Testing without auth headers...")
    try:
        response = requests.post(
            f"{base_url}/webhook_ai_analysis",
            json={"symbol": "XAUUSD"},
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        print(f"   Response: {response.status_code}")
        if response.status_code == 401:
            print("   ✅ Authentication properly required")
        else:
            print(f"   ⚠️ Unexpected response: {response.text[:100]}")
    except Exception as e:
        print(f"   ❌ Request failed: {e}")
    
    # Test with authentication
    print("   Testing with auth headers...")
    try:
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer test_token",
            "X-Access-Token": "test_access_token"
        }
        
        response = requests.post(
            f"{base_url}/webhook_ai_analysis",
            json={"symbol": "XAUUSD"},
            headers=headers,
            timeout=10
        )
        print(f"   Response: {response.status_code}")
        
        if response.status_code == 400:
            print("   ✅ Authentication passed, bad request (expected)")
        elif response.status_code == 500:
            print("   ⚠️ Server error - this is the issue we're debugging")
        else:
            print(f"   Response: {response.text[:200]}")
            
    except Exception as e:
        print(f"   ❌ Request failed: {e}")

def test_minimal_webhook_request():
    """Test with minimal valid data"""
    print("\n📝 Testing Minimal Webhook Request")
    print("=" * 38)
    
    base_url = "http://localhost:5050"
    
    # Minimal valid request
    minimal_data = {
        "symbol": "XAUUSD",
        "timeframes": ["H1"],
        "barback": 5,  # Very small number
        "prompt": "Test",
        "ai": "gpt",
        "image": "",
        "use_signal_format": False  # Disable signal format to avoid parsing issues
    }
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer test_token",
        "X-Access-Token": "test_access_token"
    }
    
    print(f"   Sending minimal request...")
    print(f"   Data: {json.dumps(minimal_data, indent=2)}")
    
    try:
        response = requests.post(
            f"{base_url}/webhook_ai_analysis",
            json=minimal_data,
            headers=headers,
            timeout=60  # Longer timeout for AI processing
        )
        
        print(f"   Response status: {response.status_code}")
        print(f"   Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print("   ✅ Success! Response keys:")
                for key in result.keys():
                    print(f"     - {key}: {str(result[key])[:50]}...")
                return True
            except json.JSONDecodeError:
                print(f"   ❌ Invalid JSON response: {response.text[:200]}")
                return False
        else:
            print(f"   ❌ Error response:")
            if 'application/json' in response.headers.get('content-type', ''):
                try:
                    error_data = response.json()
                    print(f"     Error: {error_data}")
                except:
                    pass
            print(f"     Raw: {response.text[:300]}")
            return False
            
    except requests.exceptions.Timeout:
        print("   ❌ Request timeout - server taking too long")
        return False
    except Exception as e:
        print(f"   ❌ Request failed: {e}")
        return False

def test_chart_data_only():
    """Test just chart data retrieval without AI"""
    print("\n📊 Testing Chart Data Only")
    print("=" * 30)
    
    # This would be a custom endpoint just for chart data
    # For now, let's test if we can isolate the chart data issue
    
    try:
        from App.config import Config
        from App.util import Util
        
        config = Config()
        util = Util(config)
        
        print("   Testing chart data retrieval directly...")
        
        symbol = "XAUUSD"
        timeframes = ["H1"]
        barback = 5
        
        chart_data_result = util.get_multi_timeframe_data(symbol, timeframes, barback)
        
        if chart_data_result:
            print("   ✅ Chart data retrieval works in isolation")
            print(f"     Symbol: {chart_data_result.get('symbol')}")
            print(f"     Actual: {chart_data_result.get('actual_symbol')}")
            print(f"     Bars: {chart_data_result.get('total_bars')}")
            
            # Test JSON serialization
            try:
                json_str = json.dumps(chart_data_result, indent=2)
                print("   ✅ Chart data can be serialized to JSON")
                return True
            except Exception as e:
                print(f"   ❌ Chart data JSON serialization failed: {e}")
                return False
        else:
            print("   ❌ Chart data retrieval failed in isolation")
            return False
            
    except Exception as e:
        print(f"   ❌ Chart data test failed: {e}")
        return False

def main():
    """Run webhook server tests"""
    print("🔧 Webhook Server Test Suite")
    print("=" * 35)
    
    # Find working server
    working_server = test_webhook_server_health()
    
    if not working_server:
        print("\n❌ No webhook server found!")
        print("💡 Make sure to:")
        print("  1. Start your Python application")
        print("  2. Go to Webhook tab")
        print("  3. Enable webhook server")
        print("  4. Check the port number")
        return
    
    print(f"\n✅ Found working server: {working_server}")
    
    # Run additional tests
    tests = [
        ("Authentication", test_webhook_authentication),
        ("Minimal Request", test_minimal_webhook_request),
        ("Chart Data Only", test_chart_data_only)
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n📋 Test Results:")
    print("=" * 20)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  - {test_name}: {status}")
    
    if results.get("Chart Data Only", False) and not results.get("Minimal Request", False):
        print("\n🎯 Issue identified:")
        print("  - Chart data works in isolation")
        print("  - Webhook request fails")
        print("  - Likely issue: Threading or server context problem")
        
        print("\n💡 Solutions to try:")
        print("  1. Restart the webhook server")
        print("  2. Check server logs for detailed errors")
        print("  3. Try with use_signal_format: false")
        print("  4. Check if MT5 connection is shared properly")

if __name__ == "__main__":
    main()
