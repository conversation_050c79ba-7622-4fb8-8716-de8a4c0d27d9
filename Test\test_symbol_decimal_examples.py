#!/usr/bin/env python3
"""
Test script to demonstrate how decimal formatting would work with real MT5 symbol data
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from App.config import Config

def simulate_mt5_symbol_info():
    """Simulate MT5 symbol info for different trading instruments"""
    
    # Common point values for different trading instruments
    symbol_points = {
        "XAUUSD": 0.01,      # Gold - 2 decimal places
        "EURUSD": 0.00001,   # Major forex - 5 decimal places  
        "GBPUSD": 0.00001,   # Major forex - 5 decimal places
        "USDJPY": 0.001,     # JPY pairs - 3 decimal places
        "EURJPY": 0.001,     # JPY pairs - 3 decimal places
        "BTCUSD": 0.01,      # Crypto - 2 decimal places
        "US30cash": 0.1,     # Indices - 1 decimal place
        "US500cash": 0.01,   # Indices - 2 decimal places
        "USDCAD": 0.00001,   # Major forex - 5 decimal places
        "AUDCAD": 0.00001,   # Cross pairs - 5 decimal places
    }
    
    return symbol_points

def test_real_world_decimal_formatting():
    """Test decimal formatting with realistic MT5 point values"""
    print("🧪 Testing Real-World Decimal Formatting")
    print("=" * 45)
    
    config = Config()
    symbol_points = simulate_mt5_symbol_info()
    
    for symbol, point_value in symbol_points.items():
        print(f"\n📊 Symbol: {symbol}")
        print(f"   Point Value: {point_value}")
        
        # Calculate decimal places
        decimal_places = config.get_decimal_places_from_point(point_value)
        print(f"   Decimal Places: {decimal_places}")
        
        # Show example price formatting
        if "XAU" in symbol or "GOLD" in symbol:
            base_price = 2045.50
        elif "JPY" in symbol:
            base_price = 110.50
        elif "BTC" in symbol:
            base_price = 45000.00
        elif "US30" in symbol:
            base_price = 35000.0
        elif "US500" in symbol:
            base_price = 4500.00
        else:
            base_price = 1.0850
        
        formatted_price = f"{base_price:.{decimal_places}f}"
        print(f"   Example Price: {formatted_price}")
        
        # Show what the format prompt would specify
        print(f"   Format Instruction: 'Price with {decimal_places} decimal places'")

def test_format_prompt_examples():
    """Test format prompt generation with simulated symbol data"""
    print("\n🧪 Testing Format Prompt Examples")
    print("=" * 40)
    
    config = Config()
    
    # Test key symbols
    test_symbols = ["XAUUSD", "EURUSD", "USDJPY", "US30cash"]
    
    for symbol in test_symbols:
        print(f"\n📋 Format Prompt for {symbol}:")
        print("-" * 30)
        
        try:
            format_prompt = config.get_format_prompt_for_symbol(symbol)
            
            # Extract key parts of the format prompt
            lines = format_prompt.split('\n')
            for line in lines:
                if 'decimal places' in line.lower():
                    print(f"   {line.strip()}")
                elif 'example:' in line.lower():
                    print(f"   {line.strip()}")
                    # Print next few lines for the example
                    idx = lines.index(line)
                    for i in range(1, min(6, len(lines) - idx)):
                        if lines[idx + i].strip():
                            print(f"   {lines[idx + i].strip()}")
                        if 'price:' in lines[idx + i].lower():
                            break
                    break
                    
        except Exception as e:
            print(f"   ❌ Error: {e}")

def demonstrate_ai_prompt_improvement():
    """Demonstrate how the AI prompt improvement works"""
    print("\n🎯 AI Prompt Improvement Demonstration")
    print("=" * 45)
    
    print("BEFORE (Fixed 2 decimals):")
    print("   Price: [entry price with 2 decimals]")
    print("   Example: Price: 2045.50")
    print("   Problem: USDJPY would show 110.50 (should be 110.500)")
    print("   Problem: EURUSD would show 1.08 (should be 1.08500)")
    
    print("\nAFTER (Dynamic decimals based on symbol):")
    
    config = Config()
    symbol_examples = {
        "XAUUSD": (0.01, 2, "2045.50"),
        "USDJPY": (0.001, 3, "110.500"), 
        "EURUSD": (0.00001, 5, "1.08500")
    }
    
    for symbol, (point, decimals, example) in symbol_examples.items():
        print(f"   {symbol} (point={point}):")
        print(f"     Instruction: Price with {decimals} decimal places")
        print(f"     Example: Price: {example}")

def main():
    """Run all demonstrations"""
    print("🚀 Symbol Decimal Formatting Demonstration")
    print("=" * 55)
    
    try:
        test_real_world_decimal_formatting()
        test_format_prompt_examples()
        demonstrate_ai_prompt_improvement()
        
        print("\n✅ Demonstration completed successfully!")
        print("\n📝 Summary:")
        print("   - Decimal places are now calculated from symbol point values")
        print("   - Format prompts are dynamically generated per symbol")
        print("   - AI will receive appropriate decimal precision instructions")
        print("   - Examples in prompts match the symbol's typical price range")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Demonstration failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
