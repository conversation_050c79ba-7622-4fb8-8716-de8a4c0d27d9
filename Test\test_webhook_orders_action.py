#!/usr/bin/env python3
"""
Test script for the new webhook_orders_action functionality
Tests the new "close_position" and "cancel_order" actions
"""

import requests
import json
import time

# Configuration
WEBHOOK_URL = "http://localhost:5000/webhook_orders_action"
BEARER_TOKEN = "your_bearer_token_here"  # Replace with actual token
ACCESS_TOKEN = "your_access_token_here"  # Replace with actual token

def test_close_position():
    """Test closing a position by ticket"""
    print("Testing close_position action...")
    
    data = {
        "action": "close_position",
        "ticket": 12345,  # Replace with actual position ticket
        "orderType": "position"
    }
    
    headers = {
        "Authorization": f"Bearer {BEARER_TOKEN}",
        "X-Access-Token": ACCESS_TOKEN,
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(WEBHOOK_URL, json=data, headers=headers)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_cancel_order():
    """Test canceling a pending order by ticket"""
    print("\nTesting cancel_order action...")
    
    data = {
        "action": "cancel_order",
        "ticket": 67890,  # Replace with actual pending order ticket
        "orderType": "pending"
    }
    
    headers = {
        "Authorization": f"Bearer {BEARER_TOKEN}",
        "X-Access-Token": ACCESS_TOKEN,
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(WEBHOOK_URL, json=data, headers=headers)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_invalid_action():
    """Test with invalid action to ensure proper error handling"""
    print("\nTesting invalid action...")
    
    data = {
        "action": "invalid_action",
        "ticket": 12345,
        "orderType": "position"
    }
    
    headers = {
        "Authorization": f"Bearer {BEARER_TOKEN}",
        "X-Access-Token": ACCESS_TOKEN,
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(WEBHOOK_URL, json=data, headers=headers)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 400  # Should return error for invalid action
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_missing_parameters():
    """Test with missing required parameters"""
    print("\nTesting missing parameters...")
    
    # Test close_position without ticket
    data = {
        "action": "close_position",
        "orderType": "position"
        # Missing ticket
    }
    
    headers = {
        "Authorization": f"Bearer {BEARER_TOKEN}",
        "X-Access-Token": ACCESS_TOKEN,
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(WEBHOOK_URL, json=data, headers=headers)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 400  # Should return error for missing ticket
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_wrong_order_type():
    """Test with wrong orderType for action"""
    print("\nTesting wrong orderType...")
    
    # Test close_position with pending orderType (should be position)
    data = {
        "action": "close_position",
        "ticket": 12345,
        "orderType": "pending"  # Wrong type for close_position
    }
    
    headers = {
        "Authorization": f"Bearer {BEARER_TOKEN}",
        "X-Access-Token": ACCESS_TOKEN,
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(WEBHOOK_URL, json=data, headers=headers)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 400  # Should return error for wrong orderType
    except Exception as e:
        print(f"Error: {e}")
        return False

def main():
    """Run all tests"""
    print("=== Testing Webhook Orders Action ===")
    print("Note: Update BEARER_TOKEN, ACCESS_TOKEN, and ticket numbers before running")
    print("Make sure the webhook server is running on localhost:5000")
    print()
    
    tests = [
        ("Close Position", test_close_position),
        ("Cancel Order", test_cancel_order),
        ("Invalid Action", test_invalid_action),
        ("Missing Parameters", test_missing_parameters),
        ("Wrong Order Type", test_wrong_order_type)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"Running {test_name} test...")
        result = test_func()
        results.append((test_name, result))
        time.sleep(1)  # Small delay between tests
    
    print("\n=== Test Results ===")
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")

if __name__ == "__main__":
    main()
