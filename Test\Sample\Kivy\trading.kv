BoxLayout:
    orientation: 'vertical'
    padding: 20
    spacing: 10

    Label:
        text: app.status_text
        font_size: 16

    Spinner:
        id: account_spinner
        text: 'Real 2'
        values: ['Demo 1', 'Real 1', 'Real 2']
        on_text: app.connect_to_mt5(self.text)

    Label:
        text: app.symbol_text
        font_size: 16

    BoxLayout:
        size_hint_y: None
        height: 40
        spacing: 10

        Label:
            text: "Volume:"
            size_hint_x: 0.3

        TextInput:
            id: volume_input
            multiline: False
            hint_text: "0.1"
            size_hint_x: 0.7
            input_filter: 'float'

    BoxLayout:
        size_hint_y: None
        height: 50
        spacing: 20

        Button:
            text: "BUY"
            background_color: (0,1,0,1)
            on_press: app.trade("buy")

        Button:
            text: "SELL"
            background_color: (1,0,0,1)
            on_press: app.trade("sell")

    Label:
        text: app.result_text
        font_size: 14
        color: (1,1,1,1)
