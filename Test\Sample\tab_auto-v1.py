
import MetaTrader5 as mt5
import customtkinter as ctk
import pandas as pd
import numpy as np
import talib as ta
import threading
import time
from datetime import datetime

# ===========================
# Class: TabAuto
# ===========================
class TabAuto:
    def __init__(self, master, config, util):
        self.frame = master.add("Auto")
        self.config = config
        self.util = util
        self.side_var = ctk.StringVar(value="BOTH")
        self.symbol_var = ctk.StringVar(value="XU")
        self.lot_var = ctk.DoubleVar(value=0.02)
        self.point_bsl_var = ctk.IntVar(value=self.config.SL_POINTS)
        self.point_btp_var = ctk.IntVar(value=self.config.TP1_POINTS)
        self.point_ssl_var = ctk.IntVar(value=self.config.SL_POINTS)
        self.point_stp_var = ctk.IntVar(value=self.config.TP1_POINTS)
        self.time_var = ctk.IntVar(value=60)
        self.checkbox_tf_vars = {}
        self.count_tf = {}
        self.last_htf_time = {}
        self.last_htf_text = {}
        self.count_orders = 0
        self.loop_running = False
        self.thread1 = None
        self.thread2 = None

        self.form1 = ctk.CTkFrame(self.frame)
        self.form1.pack(pady=10) 
        self.form2 = ctk.CTkFrame(self.frame)
        self.form2.pack(pady=10, padx=20) 
        self.form3 = ctk.CTkFrame(self.frame)
        self.form3.pack(pady=10) 

        self.build_ui()

    def build_ui(self):
        self.lot_var.set(0.02)  # Default lot size
        self.lot_label = ctk.CTkLabel(self.form1, text="L Size:")
        self.lot_label.grid(row=1, column=0, padx=10, pady=5)
        self.lot_val = ctk.CTkEntry(self.form1, textvariable=self.lot_var)
        self.lot_val.grid(row=1, column=1, padx=10, pady=5)

        self.symbol_label = ctk.CTkLabel(self.form1, text="Symbol:")
        self.symbol_label.grid(row=1, column=2, padx=10, pady=5)
        self.symbol_var.set("XU")  # Default symbol
        self.symbol_dropdown = ctk.CTkOptionMenu(self.form1, values=list(self.config.symbols), variable=self.symbol_var)
        self.symbol_dropdown.grid(row=1, column=3, padx=10, pady=5)
        
        self.time_var.set(1)  # Default 5 min
        self.time_label = ctk.CTkLabel(self.form1, text="Loop (min):")
        self.time_label.grid(row=2, column=0, padx=10, pady=5)
        self.time_val = ctk.CTkEntry(self.form1, textvariable=self.time_var)
        self.time_val.grid(row=2, column=1, padx=10, pady=5)

        self.side_label = ctk.CTkLabel(self.form1, text="Side:")
        self.side_label.grid(row=2, column=2, padx=10, pady=5)
        self.side_var.set("BOTH")  # Default symbol
        self.side_dropdown = ctk.CTkOptionMenu(self.form1, values=["BOTH","BUY","SELL"], variable=self.side_var)
        self.side_dropdown.grid(row=2, column=3, padx=10, pady=5)
 
        self.point_bsl_var.set(self.config.SL_POINTS)  # Default sl size
        self.point_bsl_label = ctk.CTkLabel(self.form1, text="B.SL Point:")
        self.point_bsl_label.grid(row=3, column=0, padx=10, pady=5)
        self.point_bsl_val = ctk.CTkEntry(self.form1, textvariable=self.point_bsl_var)
        self.point_bsl_val.grid(row=3, column=1, padx=10, pady=5)

        self.point_btp_var.set(self.config.TP2_POINTS)  # Default tp size
        self.point_btp_label = ctk.CTkLabel(self.form1, text="B.TP Point:")
        self.point_btp_label.grid(row=3, column=2, padx=10, pady=5)
        self.point_btp_val = ctk.CTkEntry(self.form1, textvariable=self.point_btp_var)
        self.point_btp_val.grid(row=3, column=3, padx=10, pady=5)

        self.point_ssl_var.set(self.config.SL_POINTS)  # Default sl size
        self.point_ssl_label = ctk.CTkLabel(self.form1, text="S.SL Point:")
        self.point_ssl_label.grid(row=4, column=0, padx=10, pady=5)
        self.point_ssl_val = ctk.CTkEntry(self.form1, textvariable=self.point_ssl_var)
        self.point_ssl_val.grid(row=4, column=1, padx=10, pady=5)

        self.point_stp_var.set(self.config.TP1_POINTS)  # Default tp size
        self.point_stp_label = ctk.CTkLabel(self.form1, text="S.TP Point:")
        self.point_stp_label.grid(row=4, column=2, padx=10, pady=5)
        self.point_stp_val = ctk.CTkEntry(self.form1, textvariable=self.point_stp_var)
        self.point_stp_val.grid(row=4, column=3, padx=10, pady=5)

        # ctk.CTkLabel(self.form2, text="🕒 Select Timeframes:").grid(row=0, column=0, columnspan=6, padx=10, pady=(0, 5), sticky="w")

        self.status_label = ctk.CTkLabel(self.form3, text="🔘 Not Monitoring")
        self.status_label.pack(pady=1)
        self.status_label_0_tf = {}
        self.status_label_1_tf = {}
        self.status_label_2_tf = {}
        self.status_label_3_tf = {}
        self.status_label_4_tf = {}
        self.status_label_5_tf = {}
        font = ("Arial", 10)
        pady = 2
        for i, tf_name in enumerate(self.config.timeframes):
            var = ctk.BooleanVar()
            checkbox = ctk.CTkCheckBox(self.form2, text=tf_name, variable=var)
            checkbox.grid(row=0, column=i, padx=0, pady=pady, sticky="w")
            self.checkbox_tf_vars[tf_name] = var
            self.status_label_0_tf[tf_name] = ctk.CTkLabel(self.form2, text="", font=font)
            self.status_label_0_tf[tf_name].grid(row=1, column=i, padx=0, pady=pady, sticky="w")
            self.status_label_1_tf[tf_name] = ctk.CTkLabel(self.form2, text="", font=font)
            self.status_label_1_tf[tf_name].grid(row=2, column=i, padx=0, pady=pady, sticky="w")
            self.status_label_2_tf[tf_name] = ctk.CTkLabel(self.form2, text="", font=font)
            self.status_label_2_tf[tf_name].grid(row=3, column=i, padx=0, pady=pady, sticky="w")
            self.status_label_3_tf[tf_name] = ctk.CTkLabel(self.form2, text="", font=font)
            self.status_label_3_tf[tf_name].grid(row=4, column=i, padx=0, pady=pady, sticky="w")
            self.status_label_4_tf[tf_name] = ctk.CTkLabel(self.form2, text="", font=font)
            self.status_label_4_tf[tf_name].grid(row=5, column=i, padx=0, pady=pady, sticky="w")
            self.status_label_5_tf[tf_name] = ctk.CTkLabel(self.form2, text="", font=font)
            self.status_label_5_tf[tf_name].grid(row=6, column=i, padx=0, pady=pady, sticky="w")
            self.last_htf_time[tf_name] = None
            self.last_htf_text[tf_name] = None

        stop_btn = ctk.CTkButton(self.frame, text="⏹ Stop", fg_color="red", command=self.stop_loop)
        stop_btn.pack(side="left", pady=10)
        
        start_btn = ctk.CTkButton(self.frame, text="▶ Start", fg_color="green", command=self.start_loop)
        start_btn.pack(side="right", pady=10)

    def get_selected_timeframes(self):
        selected = [self.config.timeframes[name] for name, var in self.checkbox_tf_vars.items() if var.get()]
        # selected = [self.config.timeframes[name] for name, var in self.checkbox_tf_vars if var]
        print("✅ Selected Timeframes:", selected)
        return selected
    
    def start_loop(self):
        if not self.loop_running:
            self.loop_running = True
            self.thread1 = threading.Thread(target=self.checking_loop, daemon=True)
            self.thread1.start()
            self.thread2 = threading.Thread(target=self.compute_loop, daemon=True)
            self.thread2.start()
        self.util.add_status_frame(f"🟢 Monitoring {self.side_var.get()} side started {self.symbol_var.get()}")
        self.status_label.configure(text=f"🟢 Monitoring {self.side_var.get()} side started {self.symbol_var.get()}")

    def stop_loop(self):
        self.loop_running = False
        self.util.add_status_frame(f"🔴 Monitoring {self.side_var.get()} side stopped {self.symbol_var.get()}")
        self.status_label.configure(text=f"🔴 Monitoring {self.side_var.get()} side stopped {self.symbol_var.get()}")

    def checking_loop(self):
        try:
            symbol = self.util.get_symbol(self.symbol_var)
            i = 0 # Loop ตรวจสอบ
            while self.loop_running:
                if self.util.is_in_restricted_time():
                    self.util.set_status_label(f"{time.strftime('%H:%M:%S')} ⏳ Restricted time (21:00 - 22:00 UTC). Skipping...", "yellow")
                    time.sleep(60*60) # 1 hour
                else:
                    i += 1
                    self.count_tf = self.util.update_SL_to_BE_by_point(symbol, "A2_", False) 
                    self.util.set_status_label(f"{time.strftime('%H:%M:%S')} 🕒 Checking {i}: curently {self.count_tf["All_FT"]} orders", "yellow")
                    # time.sleep(self.time_var.get()*60)  # 60 sec = 1 min /  300 sec = 5 mins
                    time.sleep(60)  # 60 sec = 1 min /  300 sec = 5 mins
        except KeyboardInterrupt:
            print("Stopped by user")
        finally:
            mt5.shutdown()
    
    def compute_loop(self):
        # while self.loop_running: 
        try:

            symbol = self.util.get_symbol(self.symbol_var)
            lot = self.lot_var.get()
            i = 0 # Loop ตรวจสอบ
            while self.loop_running:
                if self.util.is_in_restricted_time():
                    # self.util.set_status_label(f"{time.strftime('%H:%M:%S')} ⏳ Restricted time (21:00 - 22:00 UTC). Skipping...", "yellow")
                    time.sleep(60*60) # 1 hour
                else:
                    i += 1 
                    # self.count_orders = self.util.update_SL_to_BE_by_point() 
                    # self.util.set_status_label(f"{time.strftime('%H:%M:%S')} 🕒 Checking {i}: curently {self.count_orders} orders", "yellow")
                    for tf_name, tf_check in self.checkbox_tf_vars.items():
                        if tf_check.get(): 
                            result = {}
                            # Set time range
                            rates_AHTF = mt5.copy_rates_from(symbol, self.config.timeframes[tf_name], datetime.now(), 1000)
                            df_AHTF = pd.DataFrame(rates_AHTF)
                            df_AHTF['time'] = pd.to_datetime(df_AHTF['time'], unit='s')
                            
                            self.status_label_0_tf[tf_name].configure(text=f"📦 {self.count_tf[tf_name]['B']} / {self.count_tf[tf_name]['S']}\n{df_AHTF['time'].iloc[-1].strftime('%H:%M')}", text_color="yellow")
                             
                            current_time = datetime.now()

                            # หาแท่ง AHTF ล่าสุด
                            list_AHTF = df_AHTF[df_AHTF['time'] <= current_time] 
                            cur_AHTF  = list_AHTF.iloc[-1] 

                            # กันสัญญาณซ้ำ
                            if cur_AHTF['time'] == self.last_htf_time[tf_name]:
                                result["message"] = f"{current_time} - {self.last_htf_text[tf_name]} ⏳ ยังเป็นแท่ง HTF เดิม ข้าม {tf_name}"
                                print(result["message"])
                                time.sleep(60)
                                continue

                            self.last_htf_time[tf_name] = cur_AHTF['time']
                            self.last_htf_text[tf_name] = self.last_htf_time[tf_name].strftime('%H:%M')
                            
                            src = df_AHTF['close']
                            ema1 = ta.EMA(src, timeperiod=9) 
                            ema2 = ta.EMA(src, timeperiod=20) 
                            ema3 = ta.EMA(src, timeperiod=50) 
                            ema4 = ta.EMA(src, timeperiod=100) 
                            ema5 = ta.EMA(src, timeperiod=200) 
                            # ema6 = ta.EMA(src, timeperiod=300) 
                            
                            trend1 = self.compute_ema_retest_indicators(list_AHTF, symbol, lot, i, tf_name, ema1, ema4, tf_name+"_E1")
                            trend2 = self.compute_ema_retest_indicators(list_AHTF, symbol, lot, i, tf_name, ema2, ema4, tf_name+"_E2")
                            trend3 = self.compute_ema_retest_indicators(list_AHTF, symbol, lot, i, tf_name, ema3, ema4, tf_name+"_E3")
                            trend4 = self.compute_ema_retest_indicators(list_AHTF, symbol, lot, i, tf_name, ema4, ema5, tf_name+"_E4")
                            trend5 = self.compute_ema_retest_indicators(list_AHTF, symbol, lot, i, tf_name, ema5, None, tf_name+"_E5")

                            # trend1 = self.compute_ema_retest_indicators(list_AHTF, symbol, lot, 1, tf_name,   9, 100, tf_name+"_E1")
                            # trend2 = self.compute_ema_retest_indicators(list_AHTF, symbol, lot, 2, tf_name,  20, 100, tf_name+"_E2")
                            # trend3 = self.compute_ema_retest_indicators(list_AHTF, symbol, lot, 3, tf_name,  50, 100, tf_name+"_E3")
                            # trend4 = self.compute_ema_retest_indicators(list_AHTF, symbol, lot, 4, tf_name, 100, 200, tf_name+"_E4")
                            # trend5 = self.compute_ema_retest_indicators(list_AHTF, symbol, lot, 5, tf_name, 200, 300, tf_name+"_E5")
                    
                            if trend1:
                                self.status_label_1_tf[tf_name].configure(text=f"🕒 {trend1['trend']}", text_color=trend1['color']) 
                            if trend2:
                                self.status_label_2_tf[tf_name].configure(text=f"🕒 {trend2['trend']}", text_color=trend2['color']) 
                            if trend3:
                                self.status_label_3_tf[tf_name].configure(text=f"🕒 {trend3['trend']}", text_color=trend3['color']) 
                            if trend4:
                                self.status_label_4_tf[tf_name].configure(text=f"🕒 {trend4['trend']}", text_color=trend4['color']) 
                            if trend5:
                                self.status_label_5_tf[tf_name].configure(text=f"🕒 {trend5['trend']}", text_color=trend5['color']) 
                            # self.status_label_3_tf[tf_name].configure(text=f"🕒 {trend3['trend']}", text_color=trend3['color'])
                            
                    # label.config(text=f"HTF ล่าสุด: {last_ahtf_time.strftime('%Y-%m-%d %H:%M:%S')}")
                    self.status_label.configure(text=f"🟢 Monitoring {self.side_var.get()} side started {self.symbol_var.get()} {time.strftime('%H:%M:%S')} 🕒 Checking indicators {i} time(s)")

                    # self.util.set_status_label(f"{time.strftime('%H:%M:%S')} 🕒 Checking {i}: curently {count} orders", "yellow")
                    # time.sleep(self.config.TIMEFRAME1)  # 60 sec = 1 min /  300 sec = 5 mins
                    time.sleep(self.time_var.get()*60)  # 60 sec = 1 min /  300 sec = 5 mins
        except KeyboardInterrupt:
            print("Stopped by user")
        finally:
            mt5.shutdown()

                
    def compute_ema_retest_indicators(self, df_AHTF, symbol, lot, tp_multiply, tf_name, emaRetest, emaTrend, comment):
        order_type = "NO" 
        count = self.count_tf[tf_name]
        point = mt5.symbol_info(symbol).point 
        df_AHTF['emaRetest'] = emaRetest # ta.EMA(df_AHTF['close'], timeperiod=emaRetest)
        df_AHTF['emaTrend'] = emaTrend #ta.EMA(df_AHTF['close'], timeperiod=emaTrend)
        # df_AHTF['emaLast'] = ta.EMA(df_AHTF['close'], timeperiod=200)
        cur_AHTF  = df_AHTF.iloc[-1] 
        prev_AHTF = df_AHTF.iloc[-2]
        trend = (df_AHTF['close'] > df_AHTF['emaRetest']) 

        longRetest  = False
        shortRetest = False 
        # latest_df = df.iloc[-1]  # ดึงแท่งล่าสุด
        # current_time = latest_df['time']

        candleSize = abs(cur_AHTF['high'] - cur_AHTF['low'])
        bodySize = abs(cur_AHTF['open'] - cur_AHTF['close'])
        bodyPercent = (bodySize * 100 / candleSize) if candleSize != 0 else 0
        haftPrice = cur_AHTF['low'] + (candleSize/2) if candleSize != 0 else 0

        prevCandleSize = abs(prev_AHTF['high'] - prev_AHTF['low'])
        prevHaftPrice = prev_AHTF['low'] + (prevCandleSize/2) if prevCandleSize != 0 else 0

        signal = None

        # เงื่อนไขหลัก
        if (not cur_AHTF['emaTrend'] or abs(cur_AHTF['emaRetest'] - cur_AHTF['emaTrend']) > bodySize) and bodyPercent > 50:

            prevIsOverlap = prev_AHTF['low'] <= prev_AHTF['emaRetest'] and prev_AHTF['emaRetest'] <= prev_AHTF['high']
            validTrend = True if not cur_AHTF['emaTrend'] else (cur_AHTF['low'] >= cur_AHTF['emaTrend'] if cur_AHTF['close'] > cur_AHTF['open'] else cur_AHTF['high'] <= cur_AHTF['emaTrend'])
            # ---- Long ----
            if (
                prevIsOverlap and
                # prev_AHTF['close'] < prev_AHTF['open'] and
                # cur_AHTF['high'] > prev_AHTF['high'] and
                # cur_AHTF['high'] > cur_AHTF['emaRetest'] and
                prevHaftPrice > prev_AHTF['emaRetest'] and
                haftPrice > cur_AHTF['emaRetest'] and
                cur_AHTF['close'] > cur_AHTF['open'] and
                validTrend
                # cur_AHTF['low'] >= cur_AHTF['emaTrend']
            ):
                longRetest  = True
                signal = f"📈 Long Retest Signal @ {cur_AHTF['time']}"

            # ---- Short ----
            elif (
                prevIsOverlap and
                # prev_AHTF['close'] > prev_AHTF['open'] and
                # cur_AHTF['low'] < prev_AHTF['low'] and
                # cur_AHTF['low'] < cur_AHTF['emaRetest'] and
                prevHaftPrice < prev_AHTF['emaRetest'] and
                haftPrice < cur_AHTF['emaRetest'] and
                cur_AHTF['close'] < cur_AHTF['open'] and
                validTrend
                # cur_AHTF['high'] <= cur_AHTF['emaTrend']
            ): 
                shortRetest  = True
                signal = f"📉 Short Retest Signal @ {cur_AHTF['time']}"

        if signal:
            print(signal)
            # self.send_line_message(signal)

        if longRetest  :
            entry = mt5.symbol_info_tick(symbol).ask
            sl = entry - self.point_bsl_var.get() * point 
            tp = entry + (self.point_btp_var.get()*tp_multiply) * point 
            order_type = "Buy Now"
            if (self.side_var.get() == "BUY" or self.side_var.get() == "BOTH") and (count['B'] < self.config.MAX_ORDERS):
                self.util.close_orders_by_condition(symbol, "all-sell")
                self.util.send_order(order_type, symbol, lot, entry, sl, tp, "A2_" + comment)

        elif shortRetest  :
            entry = mt5.symbol_info_tick(symbol).bid
            sl =  entry + self.point_ssl_var.get() * point
            tp =  entry - (self.point_stp_var.get()*tp_multiply) * point
            order_type = "Sell Now"
            if (self.side_var.get() == "SELL" or self.side_var.get() == "BOTH") and (count['S'] < self.config.MAX_ORDERS) :
                self.util.close_orders_by_condition(symbol, "all-buy")
                self.util.send_order(order_type, symbol, lot, entry, sl, tp, "A2_" + comment)
        else:
            print(f"✅ ไม่มีสัญญาณใหม่ @ {cur_AHTF['time']} {comment}")
 
        return {
            "color" : ('lime' if trend.iloc[-1] else 'red'),
            "trend" : ('BULL' if trend.iloc[-1] else 'BEAR'),
            "order_type" : order_type,
            "data" : signal
        }
    