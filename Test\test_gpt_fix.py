#!/usr/bin/env python3
"""
Test script to verify GPT API fixes for empty content issue
"""

import sys
import os
sys.path.append('App')

from config import Config
from util import Util

def test_gpt_api_fix():
    """Test the GPT API with the new fixes"""
    print("🧪 Testing GPT API fixes...")
    
    # Initialize config and util
    config = Config()
    util = Util(config)
    
    # Test prompt (simulating chart data analysis)
    test_prompt = """
    Analyze EURUSD H1 timeframe with the following data:
    
    Latest bars:
    - Open: 1.0850, High: 1.0865, Low: 1.0845, Close: 1.0860
    - RSI: 65.5
    - MACD: 0.0012
    - EMA20: 1.0855
    
    Previous bars show upward trend with strong momentum.
    Volume is increasing.
    
    Please provide trading analysis and recommendation.
    """
    
    print(f"📊 Test prompt length: {len(test_prompt)} characters")
    
    # Test GPT API call
    try:
        result = util.call_gpt_api(test_prompt, use_signal_format=False)
        
        if result.get("error"):
            print(f"❌ GPT API call failed: {result.get('message')}")
            return False
        else:
            analysis = result.get("analysis", "")
            print(f"✅ GPT API call successful!")
            print(f"   - Response length: {len(analysis)} characters")
            print(f"   - Response preview: {analysis[:200]}...")
            
            if analysis and analysis.strip():
                print("✅ Response contains content - fix successful!")
                return True
            else:
                print("❌ Response is still empty - fix failed!")
                return False
                
    except Exception as e:
        print(f"❌ Exception during GPT API test: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_gpt_api_fix()
    if success:
        print("\n🎉 GPT API fix verification PASSED!")
    else:
        print("\n💥 GPT API fix verification FAILED!")
    
    sys.exit(0 if success else 1)
