#!/usr/bin/env python3
"""
Test script to verify the improved AI Bot History layout
"""

import os
import json
from datetime import datetime, timedelta
import random

def create_test_history_with_errors():
    """Create test history data including some with errors to test robustness"""
    
    # Create Logs/Signals directory if it doesn't exist
    logs_dir = os.path.join(os.getcwd(), "Logs", "Signals")
    os.makedirs(logs_dir, exist_ok=True)
    
    bot_name = "Test_Layout_Bot"
    
    # Create a variety of test cases
    test_cases = [
        # Successful BUY signal
        {
            "signal_type": "BUY",
            "entry_price": 2650.50,
            "tp_price": 2680.75,
            "sl_price": 2630.25,
            "analysis": "Strong bullish momentum detected with RSI above 60 and MACD crossing above signal line. Multiple timeframe analysis confirms upward trend with key support levels holding firm.",
            "error": False
        },
        # Successful SELL signal
        {
            "signal_type": "SELL", 
            "entry_price": 2665.25,
            "tp_price": 2635.50,
            "sl_price": 2685.75,
            "analysis": "Bearish divergence observed on H1 and H4 timeframes. Price action showing rejection at resistance level with increasing selling pressure.",
            "error": False
        },
        # HOLD signal
        {
            "signal_type": "HOLD",
            "entry_price": None,
            "tp_price": None,
            "sl_price": None,
            "analysis": "Market consolidation phase detected. Mixed signals across timeframes suggest waiting for clearer directional bias before entering positions.",
            "error": False
        },
        # Analysis with error
        {
            "signal_type": "BUY",
            "entry_price": 2655.00,
            "tp_price": 2675.00,
            "sl_price": 2645.00,
            "analysis": "",
            "error": True
        },
        # Long analysis text
        {
            "signal_type": "SELL",
            "entry_price": 2670.00,
            "tp_price": 2640.00,
            "sl_price": 2690.00,
            "analysis": "Comprehensive technical analysis reveals multiple bearish confluences: 1) Price rejection at key Fibonacci retracement level (61.8%), 2) RSI showing overbought conditions above 70, 3) MACD histogram declining, 4) Volume profile indicating distribution, 5) Previous resistance level acting as dynamic resistance, 6) Bearish engulfing pattern on H4 timeframe, 7) Support/resistance flip expected at current levels. Risk management suggests tight stop loss above recent high with multiple take profit levels to capture the anticipated downward movement.",
            "error": False
        }
    ]
    
    # Create 15 test entries with variety
    for i in range(15):
        # Generate timestamp (last 30 days)
        days_ago = random.randint(0, 30)
        hours_ago = random.randint(0, 23)
        minutes_ago = random.randint(0, 59)
        
        timestamp = datetime.now() - timedelta(days=days_ago, hours=hours_ago, minutes=minutes_ago)
        timestamp_str = timestamp.strftime("%Y%m%d_%H%M%S")
        
        # Select test case
        test_case = random.choice(test_cases)
        
        # Create signal data
        signal_data = {}
        if test_case['entry_price']:
            signal_data = {
                "signal_type": test_case['signal_type'],
                "symbol": "XAUUSD.iux",
                "entry_price": test_case['entry_price'],
                "tp_price": test_case['tp_price'],
                "sl_price": test_case['sl_price'],
                "reason": f"AI analysis suggests {test_case['signal_type']} signal based on technical indicators"
            }
        
        # Create history entry
        history_entry = {
            "error": test_case['error'],
            "message": "AI analysis completed successfully" if not test_case['error'] else "Analysis error occurred",
            "symbol": "XAUUSD.iux",
            "timeframes": random.choice([["H1"], ["M15", "H1"], ["H1", "H4"], ["M15", "H1", "H4"]]),
            "ai_provider": random.choice(["GPT", "GEMINI"]),
            "analysis": test_case['analysis'],
            "prompt": f"Analyze the XAUUSD trading data for comprehensive technical analysis.",
            "image_analyzed": False,
            "use_signal_format": 1 if test_case['signal_type'] in ['BUY', 'SELL'] else 0,
            "timestamp": timestamp.isoformat(),
            "signal_data": signal_data
        }
        
        # Save to file
        filename = f"{bot_name}_{timestamp_str}.json"
        filepath = os.path.join(logs_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(history_entry, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Created 15 test history entries for {bot_name}")
    print(f"📁 Files saved in: {logs_dir}")
    
    print("\n🎯 Test Cases Created:")
    print("- BUY signals with TP/SL")
    print("- SELL signals with TP/SL") 
    print("- HOLD signals (no prices)")
    print("- Error cases")
    print("- Long analysis text")
    print("- Various timeframe combinations")
    print("- Different AI providers")
    
    print("\n📊 Layout Improvements to Test:")
    print("1. Balanced header with 2-row stats layout")
    print("2. Better spaced history items")
    print("3. Color-coded signal types and profits")
    print("4. Improved pagination controls")
    print("5. Enhanced analysis preview")
    print("6. Better view details popup")
    print("7. Empty state handling")
    print("8. Error handling for missing files")
    
    print("\n🚀 To test:")
    print("1. Run main application")
    print("2. Create bot named 'Test Layout Bot'")
    print("3. Click '📊 History' button")
    print("4. Navigate through pages")
    print("5. Click 'View Full Analysis' buttons")
    print("6. Test refresh functionality")

if __name__ == "__main__":
    create_test_history_with_errors()
