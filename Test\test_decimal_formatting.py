#!/usr/bin/env python3
"""
Test script to verify decimal formatting based on symbol point values
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from App.config import Config
import MetaTrader5 as mt5

def test_decimal_calculation():
    """Test decimal places calculation from point values"""
    print("🧪 Testing Decimal Places Calculation")
    print("=" * 40)
    
    config = Config()
    
    # Test different point values
    test_cases = [
        (1.0, 0),      # Point = 1.0 -> 0 decimals
        (0.1, 1),      # Point = 0.1 -> 1 decimal
        (0.01, 2),     # Point = 0.01 -> 2 decimals (XAUUSD)
        (0.001, 3),    # Point = 0.001 -> 3 decimals
        (0.0001, 4),   # Point = 0.0001 -> 4 decimals (EURUSD)
        (0.00001, 5),  # Point = 0.00001 -> 5 decimals (USDJPY)
        (0.000001, 6), # Point = 0.000001 -> 6 decimals
    ]
    
    for point_value, expected_decimals in test_cases:
        result = config.get_decimal_places_from_point(point_value)
        status = "✅" if result == expected_decimals else "❌"
        print(f"{status} Point {point_value:>8} -> {result} decimals (expected {expected_decimals})")
    
    return True

def test_format_prompt_generation():
    """Test format prompt generation for different symbols"""
    print("\n🧪 Testing Format Prompt Generation")
    print("=" * 40)
    
    config = Config()
    
    # Test symbols (these might not be available in MT5 if not connected)
    test_symbols = ["XAUUSD", "EURUSD", "USDJPY", "BTCUSD"]
    
    for symbol in test_symbols:
        print(f"\n📊 Testing symbol: {symbol}")
        try:
            format_prompt = config.get_format_prompt_for_symbol(symbol)
            
            # Check if the prompt contains the symbol
            if symbol in format_prompt:
                print(f"✅ Symbol {symbol} found in format prompt")
            else:
                print(f"❌ Symbol {symbol} NOT found in format prompt")
            
            # Check if decimal places are mentioned
            if "decimal places" in format_prompt:
                print(f"✅ Decimal places specification found")
            else:
                print(f"❌ Decimal places specification NOT found")
            
            # Extract decimal places from the prompt
            import re
            decimal_match = re.search(r'(\d+) decimal places', format_prompt)
            if decimal_match:
                decimal_places = int(decimal_match.group(1))
                print(f"📏 Detected decimal places: {decimal_places}")
            
        except Exception as e:
            print(f"❌ Error generating format prompt for {symbol}: {e}")
    
    return True

def test_symbol_specific_examples():
    """Test that examples are appropriate for different symbol types"""
    print("\n🧪 Testing Symbol-Specific Examples")
    print("=" * 40)
    
    config = Config()
    
    # Test different symbol types
    symbol_tests = [
        ("XAUUSD", "gold", ["2045", "2040", "2050"]),  # Gold prices
        ("USDJPY", "jpy", ["110", "109", "111"]),      # JPY pairs
        ("EURUSD", "forex", ["1.08", "1.07", "1.09"]), # Standard forex
    ]
    
    for symbol, symbol_type, expected_price_patterns in symbol_tests:
        print(f"\n📊 Testing {symbol} ({symbol_type}):")
        try:
            format_prompt = config.get_format_prompt_for_symbol(symbol)
            
            # Check if appropriate price examples are used
            found_patterns = []
            for pattern in expected_price_patterns:
                if pattern in format_prompt:
                    found_patterns.append(pattern)
            
            if found_patterns:
                print(f"✅ Found appropriate price patterns: {found_patterns}")
            else:
                print(f"❌ No appropriate price patterns found")
                print(f"   Expected patterns: {expected_price_patterns}")
            
        except Exception as e:
            print(f"❌ Error testing {symbol}: {e}")
    
    return True

def main():
    """Run all tests"""
    print("🚀 Starting Decimal Formatting Tests")
    print("=" * 50)
    
    try:
        # Run tests
        test1_result = test_decimal_calculation()
        test2_result = test_format_prompt_generation()
        test3_result = test_symbol_specific_examples()
        
        # Summary
        print("\n📋 Test Summary")
        print("=" * 20)
        print(f"✅ Decimal Calculation: {'PASS' if test1_result else 'FAIL'}")
        print(f"✅ Format Prompt Generation: {'PASS' if test2_result else 'FAIL'}")
        print(f"✅ Symbol-Specific Examples: {'PASS' if test3_result else 'FAIL'}")
        
        if all([test1_result, test2_result, test3_result]):
            print("\n🎉 All tests passed! Decimal formatting is working correctly.")
            return True
        else:
            print("\n❌ Some tests failed. Please check the implementation.")
            return False
            
    except Exception as e:
        print(f"\n❌ Test execution failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
