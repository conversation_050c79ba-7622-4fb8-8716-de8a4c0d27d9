#!/usr/bin/env python3
"""
Create a Windows shortcut for the application
Run this script once to create a desktop shortcut
"""

import os
import sys
from pathlib import Path

def create_windows_shortcut():
    """Create a Windows shortcut on the desktop"""
    try:
        import win32com.client
        
        # Get paths
        script_dir = Path(__file__).parent.absolute()
        desktop = Path.home() / "Desktop"
        
        # Create shortcut
        shell = win32com.client.Dispatch("WScript.Shell")
        shortcut = shell.CreateShortCut(str(desktop / "MyApp.lnk"))
        
        # Set shortcut properties
        shortcut.Targetpath = str(script_dir / "start_app.bat")
        shortcut.WorkingDirectory = str(script_dir)
        shortcut.Description = "MyApp Trading Application"
        
        # Set icon if available
        icon_path = script_dir / "App" / "icon.png"
        if icon_path.exists():
            # Convert PNG to ICO for shortcut (optional)
            shortcut.IconLocation = str(script_dir / "start_app.bat")
        
        shortcut.save()
        
        print(f"✅ Shortcut created on desktop: {desktop / 'MyApp.lnk'}")
        return True
        
    except ImportError:
        print("❌ win32com not available. Install with: pip install pywin32")
        return False
    except Exception as e:
        print(f"❌ Error creating shortcut: {e}")
        return False

def create_batch_shortcut_manually():
    """Instructions for creating shortcut manually"""
    script_dir = Path(__file__).parent.absolute()
    
    print("Manual Shortcut Creation Instructions:")
    print("=" * 50)
    print("1. Right-click on your desktop")
    print("2. Select 'New' > 'Shortcut'")
    print(f"3. Enter this path: {script_dir / 'start_app.bat'}")
    print("4. Click 'Next'")
    print("5. Name it 'MyApp' and click 'Finish'")
    print("6. (Optional) Right-click the shortcut > Properties > Change Icon")

if __name__ == "__main__":
    print("Creating Windows shortcut for MyApp...")
    
    if not create_windows_shortcut():
        print("\nFallback: Manual shortcut creation")
        create_batch_shortcut_manually()
    
    input("\nPress Enter to exit...")
