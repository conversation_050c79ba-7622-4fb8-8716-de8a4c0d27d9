
import MetaTrader5 as mt5
import customtkinter as ctk
import pandas as pd
import numpy as np
import talib as ta
import threading
import time
from datetime import datetime
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import gc
from flask import Flask, request, jsonify
from waitress import serve

# ===========================
# Class: TabWebhook
# ===========================
class TabWebhook:
    def __init__(self, master, config, util):
        self.frame = master.add("Webhook")
        self.config = config
        self.util = util
        self.webhook_enabled = True
        self.app = Flask(__name__)
        self.name = "WH"
        self.side_var = ctk.StringVar()
        self.symbol_var = ctk.StringVar()
        self.lot_var = ctk.DoubleVar()
        # self.point_bsl_var = ctk.IntVar(value=self.config.SL_POINTS)
        # self.point_btp_var = ctk.IntVar(value=self.config.TP1_POINTS)
        # self.point_ssl_var = ctk.IntVar(value=self.config.SL_POINTS)
        # self.point_stp_var = ctk.IntVar(value=self.config.TP1_POINTS)
        self.time_var = ctk.IntVar()
        self.loop_running = False
        self.auto_be_enabled = False

        self.form1 = ctk.CTkFrame(self.frame)
        self.form1.pack(pady=10)  

        self.build_ui()
        self.build_router()
        

    def build_ui(self):
        self.lot_var.set(0.02)  # Default lot size
        self.lot_label = ctk.CTkLabel(self.form1, text="L Size:")
        self.lot_label.grid(row=1, column=0, padx=10, pady=5)
        self.lot_val = ctk.CTkEntry(self.form1, textvariable=self.lot_var)
        self.lot_val.grid(row=1, column=1, padx=10, pady=5)

        # self.symbol_label = ctk.CTkLabel(self.form1, text="Symbol:")
        # self.symbol_label.grid(row=1, column=2, padx=10, pady=5)
        # self.symbol_var.set("XU")  # Default symbol
        # self.symbol_dropdown = ctk.CTkOptionMenu(self.form1, values=list(self.config.symbols), variable=self.symbol_var)
        # self.symbol_dropdown.grid(row=1, column=3, padx=10, pady=5)
        
        # self.time_var.set(1)  # Default 5 min
        # self.time_label = ctk.CTkLabel(self.form1, text="Loop BE (min):")
        # self.time_label.grid(row=2, column=0, padx=10, pady=5)
        # self.time_val = ctk.CTkEntry(self.form1, textvariable=self.time_var)
        # self.time_val.grid(row=2, column=1, padx=10, pady=5)

        self.side_label = ctk.CTkLabel(self.form1, text="Side:")
        self.side_label.grid(row=1, column=2, padx=10, pady=5)
        self.side_var.set("BUY")  # Default symbol
        self.side_dropdown = ctk.CTkOptionMenu(self.form1, values=["BUY","SELL","BOTH"], variable=self.side_var)
        self.side_dropdown.grid(row=1, column=3, padx=10, pady=5)
  
        # self.status_label = ctk.CTkLabel(self.form3, text="🔘 Not Monitoring")
        # self.status_label.pack(pady=1) 

        self.switch_webhook = ctk.CTkSwitch(self.frame, text="Enable Webhook", command=self.toggle_webhook)
        self.switch_webhook.pack(padx=10, pady=10)
        self.switch_webhook.select()

 
    def build_router(self):

        @self.app.route('/webhook', methods=['POST'])
        def webhook():
            if not self.webhook_enabled:
                self.util.add_status_frame(f"❌ Webhook failed: Webhook disabled - " + request.get_data(as_text=True))
                # return "Webhook disabled", 403
                return "Webhook disabled", 200 
            
            data = request.get_json(silent=True, force=True)
            if not data:
                self.util.add_status_frame(f"❌ Webhook failed: Invalid data, not JSON - " + request.get_data(as_text=True))
                # return jsonify({"error": "Invalid data, not JSON "}), 400
                return "Invalid data, not JSON", 200 
            
            s ,a, p, comment = data.get("s"), data.get("a"), data.get("p"), data.get("c")
            stp, xtp ,xsl    = data.get("stp", 10), data.get("xtp", 3), data.get("xsl" ,1),
            vtp ,vsl         = data.get("tp"), data.get("sl")

            if not s or not a or not p:
                self.util.add_status_frame(f"❌ Webhook failed: Missing symbol or action or price - s:{s} a:{a} p:{p} c:{comment}")
                # return jsonify({"error": "Missing symbol or action or price"}), 400
                return "Missing symbol or action or price", 200 
            
            self.util.add_status_frame(f"✅ Webhook received: s:{s} a:{a} p:{p} c:{comment} :: {data}")

            # symbol = self.config.symbols[s] + self.config.symbol_posfix.get()
            symbol = s + self.config.symbol_posfix.get()
            lot = self.lot_var.get()
            
            entry = mt5.symbol_info_tick(symbol).ask if a == "Buy Now" else (mt5.symbol_info_tick(symbol).bid if a == "Sell Now" else p)
            if (a in ["Buy Limit", "Buy Now", "Buy Stop"]) and (self.side_var.get() == "BUY" or self.side_var.get() == "BOTH"):
                    tp = (entry + (stp*xtp)) if not vtp else vtp
                    sl = (entry - (stp*xsl)) if not vsl else vsl
                    self.util.send_order(a, symbol, lot, p, sl, tp, comment)
                    return "OK", 200
            elif (a in ["Sell Limit", "Sell Now", "Sell Stop"]) and (self.side_var.get() == "Sell" or self.side_var.get() == "BOTH"):
                    tp = (entry - (stp*xtp)) if not vtp else vtp
                    sl = (entry + (stp*xsl)) if not vsl else vsl
                    self.util.send_order(a, symbol, lot, p, sl, tp, comment)
                    return "OK", 200 
                     
            return "Only accept " + self.side_var.get(), 200
        
    # Webhook Callback function
    def toggle_webhook(self):
        if self.switch_webhook.get():
            self.enable_webhook()
        else:
            self.disable_webhook()

    def run_webhook(self):
        serve(self.app, host="0.0.0.0", port=5000)
        # self.app.run(host="0.0.0.0", port=5000, debug=False, use_reloader=False)
        self.toggle_webhook()

    def enable_webhook(self):
        self.webhook_enabled = True
        self.util.add_status_frame(f"🟢 Webhook ENABLED")

    def disable_webhook(self):
        self.webhook_enabled = False
        self.util.add_status_frame(f"🔴 Webhook DISABLED")
