import os
import MetaTrader5 as mt5
from dotenv import load_dotenv
import customtkinter as ctk
from tkinter import messagebox
import threading
import pandas as pd
import talib as ta
import time
from datetime import datetime

# Load .env
load_dotenv() 

account_default = "DEMO 1 IUX"
accounts = {
    "DEMO 1 IUX":"DEMO1",
    "DEMO 2 CFX":"DEMO2",
    "REAL 1 XM" :"REAL1",
    "REAL 2 CFX":"REAL2",
}
symbols = {
    "XU":"XAUUSD", 
    "BU":"BTCUSD", 
    "EU":"EURUSD", 
    "GU":"GBPUSD", 
    "UJ":"USDJPY"
}

# Maximum number of TP fields
MAX_TP_FIELDS = 4
RSI1_LEN = 50
SMA1_LEN = 25
RSI2_LEN = 25
SMA2_LEN = 50
RSI3_LEN = 25
SMA3_LEN = 25
TIMEFRAME = mt5.TIMEFRAME_M15
SL_POINTS = 500
TP_POINTS = 1000

# Map order type to MT5 constants
order_type_mapping = {
    "Buy Now": mt5.ORDER_TYPE_BUY,
    "Sell Now": mt5.ORDER_TYPE_SELL,
    "Buy Limit": mt5.ORDER_TYPE_BUY_LIMIT,
    "Sell Limit": mt5.ORDER_TYPE_SELL_LIMIT,
    "Buy Stop": mt5.ORDER_TYPE_BUY_STOP,
    "Sell Stop": mt5.ORDER_TYPE_SELL_STOP,
}

action_type_mapping = {
    "Buy Now": mt5.TRADE_ACTION_DEAL,
    "Sell Now": mt5.TRADE_ACTION_DEAL,
    "Buy Limit": mt5.TRADE_ACTION_PENDING,
    "Sell Limit": mt5.TRADE_ACTION_PENDING,
    "Buy Stop": mt5.TRADE_ACTION_PENDING,
    "Sell Stop": mt5.TRADE_ACTION_PENDING,
}

# Connect to MT5
def connect_to_mt5(prefix):
    # prefix = accounts[account_key]
    login = int(os.getenv(f'{prefix}_LOGIN'))
    pwd = os.getenv(f'{prefix}_PWD')
    server = os.getenv(f'{prefix}_SERVER')
    path = "C:\\Program Files\\MetaTrader 5\\terminal64.exe"
    symbol_posfix.set(os.getenv(f'{prefix}_SYMBOL_POSTFIX')) 
    
    if not mt5.initialize(path=path):
        account_status_label.configure(text="MT5 initialization failed", text_color="red")
        return
    
    if mt5.login(login, pwd, server):
        account_status_label.configure(text=f"✅ Logged in: {server}", text_color="lime")
    else:
        account_status_label.configure(text=f"❌ Login failed: {prefix}_LOGIN {mt5.last_error()}", text_color="red")
    
def get_symbol(symbol_var):
    return symbols[symbol_var.get()] + symbol_posfix.get()

def get_data(symbol, timeframe, bars=100):
    rates = mt5.copy_rates_from_pos(symbol, timeframe, 0, bars)
    df = pd.DataFrame(rates)
    df['time'] = pd.to_datetime(df['time'], unit='s')
    return df

def add_status_frame(text, text_color = 'white'):
    label = ctk.CTkLabel(status_frame, text=text, anchor="w", justify="left", text_color=text_color)
    # ❗ เพิ่มด้านบนโดยใช้ before=label ตัวแรก
    if status_labels:
        label.pack(fill="x", anchor="w", before=status_labels[0])
    else:
        label.pack(fill="x", anchor="w")
    status_labels.insert(0, label)  # เก็บ label นี้ไว้เป็นอันดับแรก

def send_order(order_type, symbol, lot, entry, sl, tp):
    request = {
        "action": action_type_mapping[order_type],
        "type_filling": mt5.ORDER_FILLING_IOC,
        "symbol": symbol,
        "volume": lot,
        "type": order_type_mapping[order_type],
        "price": entry,
        "sl": sl,
        "tp": tp,
        "deviation": 10,
        "magic": 161032,  # Unique identifier for your EA or script
        "comment": "Python Script Order",
    }

    # Send the order
    result = mt5.order_send(request)
    if result.retcode != mt5.TRADE_RETCODE_DONE:
        add_status_frame(f"❌ Trade failed: {result.comment}", "yellow")
        # status_label.configure(text=f"❌ Trade failed: {result.comment}", text_color="red")
    else:
        if order_type in ["Buy Limit", "Buy Now", "Buy Stop"]:
            add_status_frame(f"🟢 Trade successful: {order_type} {symbol}", "lime")
        elif order_type in ["Sell Limit", "Sell Now", "Sell Stop"]:
            add_status_frame(f"🔴 Trade successful: {order_type} {symbol}", "red")
        else:
            add_status_frame(f"⚠️ Trade failed: Unknown", "yellow")
        # status_label.configure(text=f"✅ Trade successful: {order_type} {symbol}", text_color="lime")

def close_orders_by_condition(condition: str):
    positions = mt5.positions_get()
    if positions is None or len(positions) == 0:
        print(f"❌ No open positions. (close {condition})")
        return
    closed = 0
    for pos in positions:
        ticket = pos.ticket
        symbol = pos.symbol
        type_ = pos.type  # 0 = BUY, 1 = SELL
        profit = pos.profit
        volume = pos.volume

        should_close = False

        # เงื่อนไขปิด order ตาม condition ที่รับมา
        if condition == 'buy-profit' and type_ == 0 and profit > 0:
            should_close = True
        elif condition == 'sell-profit' and type_ == 1 and profit > 0:
            should_close = True
        elif condition == 'all-profit' and profit > 0:
            should_close = True
        elif condition == 'buy-loss' and type_ == 0 and profit < 0:
            should_close = True
        elif condition == 'sell-loss' and type_ == 1 and profit < 0:
            should_close = True
        elif condition == 'all-loss' and profit < 0:
            should_close = True
        elif condition == 'all-buy' and type_ == 0:
            should_close = True
        elif condition == 'all-sell' and type_ == 1:
            should_close = True
        elif condition == 'all':
            should_close = True

        if should_close:
            closed +=1
            price = mt5.symbol_info_tick(symbol).bid if type_ == 0 else mt5.symbol_info_tick(symbol).ask
            order_type = mt5.ORDER_TYPE_SELL if type_ == 0 else mt5.ORDER_TYPE_BUY
            close_request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "position": ticket,
                "symbol": symbol,
                "volume": volume,
                "type": order_type,
                "price": price,
                "deviation": 10,
                "magic": 234000,
                "comment": "Auto close by condition",
                "type_filling": mt5.ORDER_FILLING_IOC,
            }

            result = mt5.order_send(close_request)
            if result.retcode == mt5.TRADE_RETCODE_DONE:
                add_status_frame(f"✅ Closed {symbol} ({'BUY' if type_ == 0 else 'SELL'}) | profit: {profit:.2f}", "yellow")
                # print(f"✅ Closed {symbol} ({'BUY' if type_ == 0 else 'SELL'}) | profit: {profit:.2f}")
            else:
                add_status_frame(f"❌ Failed to close {symbol}: {result.retcode}", "yellow")
                # print(f"❌ Failed to close {symbol}: {result.retcode}")
    return closed
# ==============
# account_tab
# ==============
def account_tab():
    # This tab will show account info and login status
    account_info_label = ctk.CTkLabel(account_tab_frame, text="Account Info", font=("Arial", 16))
    account_info_label.pack(pady=10)

    # Dropdown for selecting symbol
    account_label = ctk.CTkLabel(account_tab_frame, text="Select account:")
    account_label.pack(pady=5)
    # account_var.set("REAL2")  # Default account
    account_var.set(account_default)  # Default account
    account_dropdown = ctk.CTkOptionMenu(account_tab_frame, values=list(accounts), variable=account_var)
    account_dropdown.pack() 

    # Account login status and action
    connect_button = ctk.CTkButton(account_tab_frame, text="Connect", command=lambda: connect_to_mt5(accounts[account_var.get()]))
    connect_button.pack(pady=10)

# ==============
# input_tab
# ==============
def input_tab(): 
    ctk.CTkLabel(input_form_frame_1, text="Symbol:").grid(row=1, column=0, padx=10, pady=5)
    input_symbol_var.set("XU")  # Default symbol
    ctk.CTkOptionMenu(input_form_frame_1, values=list(symbols), variable=input_symbol_var).grid(row=1, column=1, padx=10, pady=5)

    ctk.CTkLabel(input_form_frame_1, text="Type:").grid(row=1, column=2, padx=10, pady=5)
    input_order_type_var.set("Buy Limit")  # Default order_type
    ctk.CTkOptionMenu(input_form_frame_1, values=["Buy Limit", "Sell Limit", "Buy Now", "Sell Now", "Buy Stop", "Sell Stop"], variable=input_order_type_var).grid(row=1, column=3, padx=10, pady=5)

    ctk.CTkLabel(input_form_frame_1, text="Entry:").grid(row=2, column=0, padx=10, pady=5)
    ctk.CTkEntry(input_form_frame_1, textvariable=input_entry_var).grid(row=2, column=1, padx=10, pady=5)

    ctk.CTkLabel(input_form_frame_1, text="SL:").grid(row=2, column=2, padx=10, pady=5)
    ctk.CTkEntry(input_form_frame_1, textvariable=input_sl_var).grid(row=2, column=3, padx=10, pady=5)

    input_add_tp_field()
    
    # Button to add more TP fields
    ctk.CTkButton(input_tab_frame, text="Add TP", command=input_add_tp_field).pack(side="left", padx=10) # .grid(row=MAX_TP_FIELDS + 3, column=1, columnspan=2)

    # Submit button
    ctk.CTkButton(input_tab_frame, text="Submit", fg_color="lime", command=submit_input_order).pack(side="right", padx=10) #.grid(row=MAX_TP_FIELDS + 3, column=2, columnspan=2)

    # Buttons to open trade (buy/sell)
    # ctk.CTkButton(input_tab_frame, text="<<", fg_color="red", command=lambda: send_order("Sell Now", input_lot_var.get(), 0, 0, 0)).pack(side="left", padx=10)
    # ctk.CTkButton(input_tab_frame, text=">>", fg_color="lime", command=lambda: send_order("Buy Now", input_lot_var.get(), 0, 0, 0)).pack(side="right", padx=10)

def input_add_tp_field():
    if len(input_tp_lot_vars) < MAX_TP_FIELDS:
        tp_var = ctk.StringVar(value=0)
        lot_var = ctk.StringVar(value="0.01")
        irow = len(input_tp_lot_vars) + 2

        tp_label = ctk.CTkLabel(input_form_frame_2, text=f"TP{len(input_tp_lot_vars) + 1}:")
        tp_label.grid(row=irow, column=0)

        tp_entry = ctk.CTkEntry(input_form_frame_2, textvariable=tp_var)
        tp_entry.grid(row=irow, column=1, padx=10)

        lot_label = ctk.CTkLabel(input_form_frame_2, text="Lot:")
        lot_label.grid(row=irow, column=2)

        lot_entry = ctk.CTkEntry(input_form_frame_2, textvariable=lot_var)
        lot_entry.grid(row=irow, column=3, padx=10)

        delete_button = ctk.CTkButton(input_form_frame_2, text="❌", width=10, fg_color="red", command=lambda: input_delete_tp_field(tp_label, tp_entry, lot_label, lot_entry, delete_button, tp_var, lot_var))
        delete_button.grid(row=irow, column=4)

        input_tp_lot_vars.append((tp_var, lot_var))
    else:
        status_label.configure(text=f"❌ Limit Reached: You can only add up to {MAX_TP_FIELDS} TP fields.")
        # ctk.CTkMessageBox.show_warning("Limit Reached", f"You can only add up to {MAX_TP_FIELDS} TP fields.")

def input_delete_tp_field(tp_label, tp_entry, lot_label, lot_entry, button, tp_var, lot_var):
    tp_label.destroy()
    tp_entry.destroy()
    lot_label.destroy()
    lot_entry.destroy()
    button.destroy()
    input_tp_lot_vars.remove((tp_var, lot_var))

def on_input_order_type_change(order_type):
    symbol = get_symbol(input_symbol_var)
    tick = mt5.symbol_info_tick(symbol)

    if not tick:
        print(f"❌ No symbol: {symbol}")
        return

    if order_type in ["Buy Limit", "Buy Now", "Buy Stop"]:
        add_status_frame(f"🟢 Trade successful: {order_type} {symbol}", "lime")
        input_entry_var.set(tick.ask)
    elif order_type in ["Sell Limit", "Sell Now", "Sell Stop"]:
        add_status_frame(f"🔴 Trade successful: {order_type} {symbol}", "red")
        input_entry_var.set(tick.bid)
    else:
        add_status_frame(f"⚠️ Trade failed: Unknown", "yellow") 
    # price_var.set(f"{price:.5f}")  

def submit_input_order():  
    try:
        # Get user inputs
        entry = float(input_entry_var.get())
        sl = float(input_sl_var.get())
        order_type = input_order_type_var.get()

        # Collect TP and Lot values
        tp_lot_values = []
        confirmTxt = ""
        for tp_var, lot_var in input_tp_lot_vars:
            tp = float(tp_var.get())
            lot = float(lot_var.get())
            if tp and lot:
                tp_lot_values.append((tp, lot))
                tp_point = int(abs(entry - tp) * 100)
                confirmTxt += f"\nTP {tp}  /  {tp_point} point"
 
        sl_point = int(abs(entry - sl) * 100)
        response=messagebox.askyesno("Order Confirmation!!",f'{order_type} \nET {entry} \nSL {sl}  /  {sl_point} point {confirmTxt}')
        if response:
            # Prepare and send orders with different take-profit levels and lot sizes
            for tp, lot in tp_lot_values:
                send_order(order_type, lot, entry, sl, tp)
            # ctk.CTkMessageBox.show_info("Success", "Orders sent successfully!")
    except Exception as e:
        status_label.configure(text=f"❌ Trade failed: {e}", text_color="red")
        # ctk.CTkMessageBox.show_error("Error", f"An error occurred: {e}")

# ==============
# instant_tab
# ==============
def instant_tab():
    # This tab will allow users to place trades (buy/sell)
    # instant_label = ctk.CTkLabel(instant_tab_frame, text="Instant", font=("Arial", 16))
    # instant_label.pack(pady=10)
 
    instant_lot_var.set(0.02)  # Default lot size
    instant_lot_label = ctk.CTkLabel(instant_form_frame_1, text="L Size:")
    instant_lot_label.grid(row=1, column=0, padx=10, pady=5)
    instant_lot_val = ctk.CTkEntry(instant_form_frame_1, textvariable=instant_lot_var)
    instant_lot_val.grid(row=1, column=1, padx=10, pady=5)

    # Dropdown for selecting symbol
    instant_symbol_label = ctk.CTkLabel(instant_form_frame_1, text="Symbol:")
    instant_symbol_label.grid(row=1, column=2, padx=10, pady=5)
    instant_symbol_var.set("XU")  # Default symbol
    instant_symbol_dropdown = ctk.CTkOptionMenu(instant_form_frame_1, values=list(symbols), variable=instant_symbol_var)
    instant_symbol_dropdown.grid(row=1, column=3, padx=10, pady=5)

    ctk.CTkButton(instant_tab_frame, text="<<", fg_color="red", command=lambda: submit_instant_SELL()).pack(side="left", padx=10)
    ctk.CTkButton(instant_tab_frame, text=">>", fg_color="green", command=lambda: submit_instant_BUY()).pack(side="right", padx=10)

def submit_instant_BUY(): 
    try:
        symbol = get_symbol(instant_symbol_var)
        point = mt5.symbol_info(symbol).point 
        entry = mt5.symbol_info_tick(symbol).ask
        sl = entry - SL_POINTS * point 
        tp = entry + TP_POINTS * point  
        # print(f"symbol et {entry} tp {tp} sl {sl}")

        lot_size = instant_lot_var.get() 
        send_order("Buy Now", symbol, lot_size, entry, sl, tp)
    except Exception as e:
        status_label.configure(text=f"❌ Trade failed: {e} et {entry} tp {tp} sl {sl} p {point}", text_color="red")
        # messagebox.showerror("Error", f"An error occurred: {e}")
 
def submit_instant_SELL():
    try:
        symbol = get_symbol(instant_symbol_var) 
        point = mt5.symbol_info(symbol).point 
        entry = mt5.symbol_info_tick(symbol).bid
        sl =  entry + SL_POINTS * point
        tp =  entry - TP_POINTS * point
        lot_size = instant_lot_var.get()
        send_order("Sell Now", symbol, lot_size, entry, sl, tp)
    except Exception as e:
        status_label.configure(text=f"❌ Trade failed: {e} et {entry} tp {tp} sl {sl} p {point}", text_color="red")
        # messagebox.showerror("Error", f"An error occurred: {e}")
 
# ==============
# auto_tab
# ==============
def auto_tab():
    
    auto_lot_var.set(0.02)  # Default lot size
    auto_lot_label = ctk.CTkLabel(auto_form_frame_1, text="L Size:")
    auto_lot_label.grid(row=1, column=0, padx=10, pady=5)
    auto_lot_val = ctk.CTkEntry(auto_form_frame_1, textvariable=auto_lot_var)
    auto_lot_val.grid(row=1, column=1, padx=10, pady=5)

    # Dropdown for selecting symbol
    auto_symbol_label = ctk.CTkLabel(auto_form_frame_1, text="Symbol:")
    auto_symbol_label.grid(row=1, column=2, padx=10, pady=5)
    auto_symbol_var.set("XU")  # Default symbol
    auto_symbol_dropdown = ctk.CTkOptionMenu(auto_form_frame_1, values=list(symbols), variable=auto_symbol_var)
    auto_symbol_dropdown.grid(row=1, column=3, padx=10, pady=5)
    # GUI elements


    stop_btn = ctk.CTkButton(auto_tab_frame, text="⏹ Stop", fg_color="red", command=auto_stop_loop)
    stop_btn.pack(side="left", pady=10)
    
    start_btn = ctk.CTkButton(auto_tab_frame, text="▶ Start", fg_color="green", command=auto_start_loop)
    start_btn.pack(side="right", pady=10)

def auto_start_loop():
    global loop_running, loop_thread
    symbol = get_symbol(auto_symbol_var)
    if not loop_running:
        loop_running = True
        loop_thread = threading.Thread(target=auto_order_loop, daemon=True)
        loop_thread.start()
        auto_status_label.configure(text=f"🟢 Monitoring started {symbol}")
        add_status_frame(f"🟢 Monitoring started {symbol}")

def auto_stop_loop():
    global loop_running
    symbol = get_symbol(auto_symbol_var)
    loop_running = False
    auto_status_label.configure(text=f"🔴 Monitoring stopped {symbol}")
    add_status_frame(f"🔴 Monitoring stopped {symbol}")

def auto_order_loop():
    global loop_running
    symbol = get_symbol(auto_symbol_var)
    lot = auto_lot_var.get()
    try:
        i = 0 # Loop ตรวจสอบ
        while loop_running:
            i += 1
            orders = update_orders()
            if len(orders) < MAX_TP_FIELDS :
                df = get_data(symbol, TIMEFRAME)
                trend1 = compute_rsma_indicators(df, symbol, lot, RSI1_LEN, SMA1_LEN)
                trend2 = compute_rsma_indicators(df, symbol, lot, RSI2_LEN, SMA2_LEN)
                trend3 = compute_rsma_indicators(df, symbol, lot, RSI3_LEN, SMA3_LEN)
                    
                status_label.configure(text=f"🕒 Checking {i}: {time.strftime('%H:%M:%S')}", text_color="yellow")
                auto_status_label1.configure(text=f"🕒 {trend1['trend']}: {trend1['data']}", text_color=trend1['color'])
                auto_status_label2.configure(text=f"🕒 {trend2['trend']}: {trend2['data']}", text_color=trend2['color'])
                auto_status_label3.configure(text=f"🕒 {trend3['trend']}: {trend3['data']}", text_color=trend3['color'])
            time.sleep(60)  # 60 sec = 1 min /  300 sec = 5 mins
    except KeyboardInterrupt:
        print("Stopped by user")
    finally:
        mt5.shutdown()

def compute_rsma_indicators(df, symbol, lot, rsi, sma):
    df['rsi'] = ta.RSI(df['close'], timeperiod=rsi)
    df['sma'] = ta.SMA(df['rsi'], timeperiod=sma)
    order_type = "NO" 
    point = mt5.symbol_info(symbol).point 
    
    # if df['time'].iloc[-2] < df['time'].iloc[-1]:
    if df['rsi'].iloc[-2] < df['sma'].iloc[-2] and df['rsi'].iloc[-1] > df['sma'].iloc[-1]: # crossover(df['rsi'], df['sma']):
        entry = mt5.symbol_info_tick(symbol).ask
        sl = entry - SL_POINTS * point 
        tp = entry + TP_POINTS * point  
        order_type = "Buy Now"
        close_orders_by_condition("all-sell")
        send_order(order_type, symbol, lot, entry, sl, tp)

    elif df['rsi'].iloc[-2] > df['sma'].iloc[-2] and df['rsi'].iloc[-1] < df['sma'].iloc[-1]: # crossunder(df['rsi'], df['sma']):
        entry = mt5.symbol_info_tick(symbol).bid
        sl =  entry + SL_POINTS * point
        tp =  entry - TP_POINTS * point
        order_type = "Sell Now"
        close_orders_by_condition("all-buy")
        send_order(order_type, symbol, lot, entry, sl, tp)
    
    trend = df['rsi'].iloc[-1] > df['sma'].iloc[-1]
    return {
        "color" : ('lime' if trend else 'red'),
        "trend" : ('BULL' if trend else 'BEAR'),
        "order_type" : order_type,
        "data" : f"r{rsi} = " + "{:.2f}".format(df['rsi'].iloc[-1], 2) + f" | s{sma} = " + "{:.2f}".format(df['sma'].iloc[-1], 2)
   }
    # return ('BULL 🟢 ' if df['rsi'].iloc[-1] > df['sma'].iloc[-1] else 'BEAR 🔴 ') + " r " + "{:.2f}".format(df['rsi'].iloc[-1], 2) + " s " + "{:.2f}".format(df['sma'].iloc[-1], 2)
 
def update_orders():
    """ Auto update orders based on price conditions """
    orders = mt5.positions_get() if mt5.initialize() else []
    
    for order in orders:
        entry_price = order.price_open
        current_price = order.price_current
        current_tp = order.tp
        symbol_info = mt5.symbol_info(order.symbol)
        if not symbol_info:
            continue

        point = symbol_info.point
        new_sl = None

        if order.type == mt5.ORDER_TYPE_BUY and current_price >= entry_price + 5:
            new_sl = entry_price + (10 * point)
        elif order.type == mt5.ORDER_TYPE_SELL and current_price <= entry_price - 5:
            new_sl = entry_price - (10 * point)

        if new_sl:
            request = {"action": mt5.TRADE_ACTION_SLTP, "position": order.ticket, "sl": new_sl, "tp": current_tp}
            add_status_frame(f"📦 Set SL to BE: position {order.ticket} price {entry_price}")
            mt5.order_send(request)
    return orders

# Initialize customtkinter
ctk.set_appearance_mode("dark")  # Set dark mode for the GUI
ctk.set_default_color_theme("blue")  # Set blue theme

# Create main window
root = ctk.CTk()
root.geometry("600x500")
root.title("My Platform")
symbol_posfix = ctk.StringVar()
status_labels = []
loop_running = False
loop_thread = None

# Create Tabview
tabview = ctk.CTkTabview(root)
tabview.pack(pady=20, padx=20, fill="both", expand=True)

# Add tabs for account and trading
account_tab_frame = tabview.add("Account")

input_tab_frame = tabview.add("Input")
input_form_frame_1 = ctk.CTkFrame(input_tab_frame)
input_form_frame_1.pack(pady=20) 
input_form_frame_2 = ctk.CTkFrame(input_tab_frame)
input_form_frame_2.pack(pady=20)

instant_tab_frame = tabview.add("Instant")
instant_form_frame_1 = ctk.CTkFrame(instant_tab_frame)
instant_form_frame_1.pack(pady=20) 

auto_tab_frame = tabview.add("Auto")
auto_form_frame_1 = ctk.CTkFrame(auto_tab_frame)
auto_form_frame_1.pack(pady=20) 
auto_status_label = ctk.CTkLabel(auto_tab_frame, text="🔘 Not Monitoring")
auto_status_label.pack(pady=1)
auto_status_label1 = ctk.CTkLabel(auto_tab_frame, text="")
auto_status_label1.pack(pady=1)
auto_status_label2 = ctk.CTkLabel(auto_tab_frame, text="")
auto_status_label2.pack(pady=1)
auto_status_label3 = ctk.CTkLabel(auto_tab_frame, text="")
auto_status_label3.pack(pady=1)

# Set up Account Tab
account_var = ctk.StringVar()
account_tab()

instant_symbol_var = ctk.StringVar()
instant_lot_var = ctk.DoubleVar(value=0.02)
instant_tab()

# Set up Trade Tab
input_symbol_var = ctk.StringVar()
input_lot_var = ctk.DoubleVar(value=0.02)
input_order_type_var = ctk.StringVar()
input_entry_var = ctk.DoubleVar()
input_tp_lot_vars = []
input_sl_var = ctk.DoubleVar()
input_tab()

# ใช้เก็บ reference label ล่าสุด เพื่อ pack ก่อนหน้าได้
auto_symbol_var = ctk.StringVar()
auto_lot_var = ctk.DoubleVar(value=0.02)
auto_tab()


# Add a status label at the bottom to show messages
status_label_frame = ctk.CTkFrame(root)
status_label_frame.pack(fill="both", padx=20, pady=5) 
account_status_label = ctk.CTkLabel(status_label_frame, text="Connection: None", text_color="yellow", font=("Arial", 12))
account_status_label.pack(side="left", padx=10, pady=10)
status_label = ctk.CTkLabel(status_label_frame, text="Status: Ready", text_color="yellow", font=("Arial", 12))
status_label.pack(side="right", padx=10, pady=10)

status_frame = ctk.CTkScrollableFrame(root, width=480, height=250)
status_frame.pack(pady=10, padx=20, fill="both", expand=False)

connect_to_mt5(accounts[account_default])
# ปิด mt5 อย่างปลอดภัยเมื่อปิด GUI
def on_closing():
    global loop_running
    loop_running = False
    mt5.shutdown()
    root.destroy()
root.protocol("WM_DELETE_WINDOW", on_closing)
root.mainloop()
