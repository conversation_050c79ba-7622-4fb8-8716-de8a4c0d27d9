#!/usr/bin/env python3
"""
Debug symbol variations to see exactly what's happening
"""

import os
import sys

# Load environment variables first
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ Environment variables loaded from .env file")
except ImportError:
    # Manual loading
    env_path = os.path.join(os.getcwd(), '.env')
    if os.path.exists(env_path):
        with open(env_path, 'r') as f:
            for line in f:
                if '=' in line and not line.strip().startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
        print("✅ Environment variables loaded manually from .env file")

def debug_symbol_variations():
    """Debug the symbol variation logic step by step"""
    print("🔍 Debugging Symbol Variations")
    print("=" * 35)
    
    try:
        from App.config import Config
        from App.util import Util
        
        config = Config()
        util = Util(config)
        
        # Test parameters
        symbol = "XAUUSD"
        timeframes = ["H1"]
        barback = 10
        
        print(f"✅ Input parameters:")
        print(f"   Symbol: {symbol}")
        print(f"   Timeframes: {timeframes}")
        print(f"   Bars back: {barback}")
        
        # Manually create symbol variations (same logic as in the method)
        symbol_variations = [
            symbol + ".iux",  # Primary: IUX Markets format (XAUUSD.iux)
            symbol,  # Original symbol
            symbol + (config.symbol_posfix.get() if config.symbol_posfix else ""),  # With postfix
            symbol.replace("XAUUSD", "GOLD.iux"),  # GOLD with suffix
            symbol.replace("XAUUSD", "GOLD"),  # Common MT5 variation
            symbol.replace("XAUUSD", "XAU/USD"),  # Another variation
            symbol.replace("EURUSD", "EURUSD.iux"),  # EUR with suffix
            symbol.replace("EURUSD", "EUR/USD"),  # EUR variation
        ]
        
        # Remove duplicates while preserving order
        symbol_variations = list(dict.fromkeys(symbol_variations))
        
        print(f"\n📋 Symbol variations to try:")
        for i, var in enumerate(symbol_variations):
            print(f"   {i+1}. {var}")
        
        # Test each variation manually
        print(f"\n🧪 Testing each variation:")
        
        successful_symbol = None
        for i, test_symbol in enumerate(symbol_variations):
            print(f"\n   Testing #{i+1}: {test_symbol}")
            
            try:
                # Test direct MT5 access first
                import MetaTrader5 as mt5
                if not mt5.initialize():
                    print("   ❌ MT5 not initialized")
                    continue
                
                rates = mt5.copy_rates_from_pos(test_symbol, mt5.TIMEFRAME_H1, 0, 5)
                if rates is not None and len(rates) > 0:
                    print(f"   ✅ Direct MT5: {len(rates)} bars")
                    
                    # Now test our chart data method
                    chart_data = util.get_chart_data(test_symbol, "H1", barback)
                    if chart_data:
                        print(f"   ✅ Chart data method: {len(chart_data)} bars")
                        successful_symbol = test_symbol
                        break
                    else:
                        print(f"   ❌ Chart data method failed")
                else:
                    print(f"   ❌ Direct MT5: No data")
                    
            except Exception as e:
                print(f"   ❌ Exception: {str(e)}")
        
        if successful_symbol:
            print(f"\n🎉 Found working symbol: {successful_symbol}")
            
            # Test the multi-timeframe method
            print(f"\n🧪 Testing multi-timeframe method with original symbol...")
            result = util.get_multi_timeframe_data(symbol, timeframes, barback)
            
            if result:
                print(f"✅ Multi-timeframe method worked!")
                print(f"   Original: {result.get('symbol')}")
                print(f"   Actual: {result.get('actual_symbol')}")
                print(f"   Total bars: {result.get('total_bars')}")
            else:
                print(f"❌ Multi-timeframe method failed")
                
                # Let's see what the method is actually doing
                print(f"\n🔍 Debugging multi-timeframe method...")
                
                # Check if the method is using the right variations
                print(f"   Symbol postfix config: {config.symbol_posfix.get() if config.symbol_posfix else 'None'}")
                
        else:
            print(f"\n❌ No working symbol found")
            
            # Check what symbols are actually available
            print(f"\n🔍 Checking available symbols...")
            import MetaTrader5 as mt5
            if mt5.initialize():
                symbols = mt5.symbols_get()
                if symbols:
                    gold_symbols = [s.name for s in symbols if 'xau' in s.name.lower() or 'gold' in s.name.lower()]
                    print(f"   Available gold symbols: {gold_symbols}")
                else:
                    print(f"   No symbols retrieved")
            else:
                print(f"   MT5 initialization failed")
        
        return successful_symbol is not None
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_get_chart_data_directly():
    """Test get_chart_data method directly"""
    print("\n🧪 Testing get_chart_data Directly")
    print("=" * 38)
    
    try:
        from App.config import Config
        from App.util import Util
        
        config = Config()
        util = Util(config)
        
        # Test with the symbol we know works
        test_symbol = "XAUUSD.iux"
        timeframe = "H1"
        barback = 10
        
        print(f"✅ Testing get_chart_data:")
        print(f"   Symbol: {test_symbol}")
        print(f"   Timeframe: {timeframe}")
        print(f"   Bars back: {barback}")
        
        chart_data = util.get_chart_data(test_symbol, timeframe, barback)
        
        if chart_data:
            print(f"✅ get_chart_data successful!")
            print(f"   Retrieved: {len(chart_data)} bars")
            print(f"   Latest close: {chart_data[-1].get('close', 'N/A')}")
            print(f"   Has EMA20: {'ema_20' in chart_data[-1]}")
            print(f"   Has RSI14: {'rsi_14' in chart_data[-1]}")
            return True
        else:
            print(f"❌ get_chart_data failed")
            return False
            
    except Exception as e:
        print(f"❌ get_chart_data test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run symbol variation debugging"""
    print("🔧 Symbol Variation Debugging")
    print("=" * 35)
    
    # Run tests
    tests = [
        ("Symbol Variations", debug_symbol_variations),
        ("Direct Chart Data", test_get_chart_data_directly)
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n📋 Debug Results:")
    print("=" * 20)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  - {test_name}: {status}")
    
    if all(results.values()):
        print("\n🎉 Symbol handling is working!")
    else:
        print("\n⚠️ Symbol handling issues found")
        print("\n💡 Possible solutions:")
        print("  1. Check if MT5 is connected")
        print("  2. Verify symbol exists and is visible")
        print("  3. Check symbol postfix configuration")
        print("  4. Ensure chart data method is working correctly")

if __name__ == "__main__":
    main()
