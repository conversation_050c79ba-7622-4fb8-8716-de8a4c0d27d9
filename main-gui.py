# Load environment variables from .env file first
import os
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ Environment variables loaded from .env file")
except ImportError:
    # If python-dotenv is not installed, try to load .env manually
    env_path = os.path.join(os.getcwd(), '.env')
    if os.path.exists(env_path):
        with open(env_path, 'r') as f:
            for line in f:
                if '=' in line and not line.strip().startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
        print("✅ Environment variables loaded manually from .env file")
    else:
        print("⚠️ No .env file found")

from App.config import Config
from App.util import Util
# from Test.tab_account import TabAccount
from App.tab_input import TabInput
from App.tab_instant import TabInstant
# from App.tab_auto import TabAuto
from App.tab_webhook import TabWebhook
from App.tab_control import TabControl
from App.tab_orders import TabOrders
from App.tab_ai_bots import TabAIBots
import customtkinter as ctk
import MetaTrader5 as mt5
import threading
import sys
from pathlib import Path

# System tray imports
try:
    import pystray
    from PIL import Image, ImageDraw
    TRAY_AVAILABLE = True
except ImportError:
    TRAY_AVAILABLE = False
    print("System tray not available. Install with: pip install pystray pillow")

# ===========================
# Main App Setup
# ===========================
class MyApp:
    def __init__(self, quiet_mode=False, auto_minimize=False, port=None, account=None):
        # objgraph.show_growth()  # ดู object ที่เพิ่มขึ้น
        self.quiet_mode = quiet_mode
        self.auto_minimize = auto_minimize
        self.port = port
        self.account = account

        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue") 
        self.root = ctk.CTk()
        width, height = 720, 740
        # self.root.geometry(f"{width}x{height}")
        # self.root.grab_set()

        # Center the popup
        # self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
        # self.root.geometry("720x720+0+0")

        # Hide window initially if quiet mode
        if quiet_mode:
            self.root.withdraw()

        # self.root.attributes("-topmost", True)
        self.config = Config()
        self.root.title(self.config.app_name)

        # System tray variables
        self.tray_icon = None
        self.is_minimized_to_tray = False

        self.util = Util(self.config)
        self.tabview = ctk.CTkTabview(self.root)
        self.tabview.pack(expand=True, fill="both", padx=20, pady=5)

        # Add a status label at the bottom to show messages
        self.config.status_label_frame = ctk.CTkFrame(self.root)
        self.config.status_label_frame.pack(fill="both", padx=20, pady=5)
        # self.config.timer_label = ctk.CTkLabel(self.config.status_label_frame, text="(0)", text_color="yellow", font=("Arial", 12))
        # self.config.timer_label.pack(side="right", padx=10, pady=10)
        self.config.status_label = ctk.CTkLabel(self.config.status_label_frame, text="Status: Ready", text_color="yellow", font=("Arial", 12))
        self.config.status_label.pack(side="right", padx=10, pady=5)

        self.config.status_scroll_frame = ctk.CTkScrollableFrame(self.root, width=480, height=250)
        self.config.status_scroll_frame.pack(padx=20, pady=5, fill="both", expand=False)

        # Add minimize to tray button
        if TRAY_AVAILABLE:
            self.tray_button = ctk.CTkButton(
                self.root,
                text="📍",
                command=self.minimize_to_tray,
                width=25,
                height=25,
                font=("Arial", 10)
            )
            self.tray_button.pack(side="left", padx=10, pady=5)

        # Use command line account if provided, otherwise use default
        account_value = self.account if self.account and self.account in self.config.accounts else self.config.account_default
        if self.account and self.account in self.config.accounts and not self.quiet_mode:
            print(f"🔧 Account override: Using account {self.account} from command line")
        elif self.account and self.account not in self.config.accounts:
            print(f"⚠️ Warning: Account {self.account} not found in config, using default: {self.config.account_default}")

        self.account_var = ctk.StringVar(value=account_value)
        self.config.prefix = self.config.accounts[self.account_var.get()]
        self.config.path = os.getenv(f"{self.config.prefix}_PATH" , r"C:\\Program Files\\MetaTrader 5\\terminal64.exe")
 
        ctk.CTkOptionMenu(self.root, values=list(self.config.accounts), variable=self.account_var).pack(side="left", padx=10, pady=5)
        ctk.CTkButton(self.root, text="Connect", command=lambda: self.util.connect_to_mt5(self.config.accounts[self.account_var.get()])).pack(side="left", padx=10, pady=5)

        self.log_level_menu = ctk.CTkOptionMenu(self.root, values=[f"{level} - {name}" for level, name in self.config.log_level_names.items()], command=self.change_log_level)
        self.log_level_menu.set(f"{self.config.log_level} - {self.config.log_level_names[self.config.log_level]}")
        self.log_level_menu.pack(side="right", padx=10, pady=5)
        ctk.CTkLabel(self.root, text="Log Level:", font=("Arial", 12)).pack(side="right", padx=10, pady=5)

        # prefix = self.config.accounts[self.account_var.get()]
        # self.util.connect_to_mt5(prefix) 
        self.util.connect_to_mt5(self.config.prefix)

        # Set port in config if provided via command line
        if self.port:
            self.config.port_default = self.port
            if not self.quiet_mode:
                print(f"🔧 Port override: Using port {self.port} from command line")

        # self.account_tab = TabAccount(self.tabview, self.config, self.util, self.quiet_mode)
        self.input_tab = TabInput(self.tabview, self.config, self.util)
        self.instant_tab = TabInstant(self.tabview, self.config, self.util)
        # self.auto_tab = TabAuto(self.tabview, self.config, self.util)
        self.webhook_tab = TabWebhook(self.tabview, self.config, self.util)
        self.control_tab = TabControl(self.tabview, self.config, self.util)
        self.orders_tab = TabOrders(self.tabview, self.config, self.util)
        self.ai_bots_tab = TabAIBots(self.tabview, self.config, self.util)

        # --- Flask server will be started when webhook is enabled via toggle ---
        # No longer auto-starting Flask server to prevent threading issues

        # objgraph.show_growth()  # ดู object ที่เพิ่มขึ้น
        self.root.protocol("WM_DELETE_WINDOW", self.on_close)

        # Auto-minimize to tray if requested
        if self.auto_minimize and TRAY_AVAILABLE:
            self.root.after(2000, self.auto_minimize_to_tray)  # Minimize after 2 seconds
        elif self.quiet_mode and not TRAY_AVAILABLE:
            # If quiet mode but no tray available, just minimize to taskbar
            self.root.after(1000, lambda: self.root.iconify())

        self.root.mainloop()

    def change_log_level(self, selection):
        """Change log level based on selection"""
        # Extract level number from selection (format: "3 - Warning")
        level = int(selection.split(" - ")[0])
        self.config.log_level = level
        level_name = self.config.log_level_names[level]
        self.util.add_status_frame(f"📊 Log level changed to: {level} - {level_name}", "cyan", level=2)
 
    def on_close(self):
        """Properly cleanup resources before closing"""
        try:
            # Stop webhook server first
            if hasattr(self, 'webhook_tab') and hasattr(self.webhook_tab, 'cleanup'):
                self.webhook_tab.cleanup()
                self.util.add_status_frame("🔴 Webhook server stopped", "yellow")

            # Stop system tray icon
            if self.tray_icon:
                self.tray_icon.stop()
                self.tray_icon = None

            # Stop any running loops in tabs
            if hasattr(self, 'orders_tab') and hasattr(self.orders_tab, 'loop_running'):
                self.orders_tab.loop_running = False
                self.util.add_status_frame("🔴 Stopping background processes...", "yellow")

            # Give threads time to stop gracefully
            import time
            time.sleep(2)

            # Shutdown MT5 connection
            mt5.shutdown()
            self.util.add_status_frame("🔴 MT5 connection closed", "yellow")

        except Exception as e:
            print(f"Error during cleanup: {e}")
        finally:
            self.root.destroy()

    def create_tray_icon(self):
        """Create a system tray icon"""
        if not TRAY_AVAILABLE:
            return None

        # Try to load existing icon, or create a simple one
        try:
            icon_path = Path("App/icon.png")
            if icon_path.exists():
                image = Image.open(icon_path)
            else:
                # Create a simple icon if none exists
                image = Image.new('RGB', (64, 64), color='blue')
                draw = ImageDraw.Draw(image)
                draw.ellipse([16, 16, 48, 48], fill='white')
        except Exception:
            # Fallback: create a simple colored square
            image = Image.new('RGB', (64, 64), color='blue')
            draw = ImageDraw.Draw(image)
            draw.rectangle([8, 8, 56, 56], fill='white')

        # Create menu
        menu = pystray.Menu(
            pystray.MenuItem("Show/Restore", self.restore_from_tray),
            pystray.MenuItem(f"Webhook {self.account_var.get()} Status", self.show_webhook_status),
            pystray.Menu.SEPARATOR,
            pystray.MenuItem("Exit", self.exit_from_tray)
        )

        # Create icon
        icon = pystray.Icon(
            name=self.config.app_name,
            icon=image,
            title=f"{self.config.app_name} - Running",
            menu=menu
        )

        return icon

    def auto_minimize_to_tray(self):
        """Auto-minimize to tray during startup"""
        if self.quiet_mode:
            self.util.add_status_frame("🔇 Starting in quiet mode - minimizing to tray", "cyan")
        self.minimize_to_tray()

    def minimize_to_tray(self):
        """Minimize application to system tray"""
        if not TRAY_AVAILABLE:
            self.util.add_status_frame("❌ System tray not available. Install: pip install pystray pillow", "red")
            return

        try:
            # Hide the main window
            self.root.withdraw()
            self.is_minimized_to_tray = True

            # Create and start tray icon if not already created
            if self.tray_icon is None:
                self.tray_icon = self.create_tray_icon()
                if self.tray_icon:
                    # Run tray icon in background thread
                    tray_thread = threading.Thread(target=self.tray_icon.run, daemon=True)
                    tray_thread.start()

                    self.util.add_status_frame("📍 Application minimized to system tray", "cyan")
                else:
                    self.util.add_status_frame("❌ Failed to create system tray icon", "red")
                    self.root.deiconify()  # Restore window if tray failed
                    self.is_minimized_to_tray = False

        except Exception as e:
            self.util.add_status_frame(f"❌ Error minimizing to tray: {e}", "red")
            self.root.deiconify()  # Restore window on error
            self.is_minimized_to_tray = False

    def restore_from_tray(self, icon=None, item=None):
        """Restore application from system tray"""
        try:
            self.root.deiconify()
            self.root.lift()
            self.root.focus_force()
            self.is_minimized_to_tray = False

            # Stop tray icon
            if self.tray_icon:
                self.tray_icon.stop()
                self.tray_icon = None

            self.util.add_status_frame("📍 Application restored from system tray", "cyan")

        except Exception as e:
            print(f"Error restoring from tray: {e}")

    def show_webhook_status(self, icon=None, item=None):
        """Show webhook status in tray notification"""
        try:
            if hasattr(self, 'webhook_tab') and hasattr(self.webhook_tab, 'webhook_enabled'):
                status = "Enabled" if self.webhook_tab.webhook_enabled else "Disabled"
                if self.tray_icon:
                    self.tray_icon.notify(f"Webhook Status: {self.config.port_default} {status}", f"{self.config.app_name}")
        except Exception as e:
            print(f"Error showing webhook status: {e}")

    def exit_from_tray(self, icon=None, item=None):
        """Exit application from system tray"""
        try:
            if self.tray_icon:
                self.tray_icon.stop()
            self.on_close()
        except Exception as e:
            print(f"Error exiting from tray: {e}")
            sys.exit(1)

if __name__ == "__main__":
    # Check for help argument
    if "--help" in sys.argv or "-h" in sys.argv:
        print("Usage: python main-gui.py [OPTIONS]")
        print("\nOptions:")
        print("  --quiet, -q           Start in quiet mode (minimized to tray)")
        print("  --minimize, -m        Auto-minimize to tray after startup")
        print("  --port=PORT, -p PORT  Set webhook server port (default: from config)")
        print("  --account=ACC, -a ACC Set MT5 account (e.g., REAL2, DEMO1)")
        print("  --help, -h            Show this help message")
        print("\nExamples:")
        print("  python main-gui.py --quiet --port=5050")
        print("  python main-gui.py -q -m -p 8080 -a REAL2")
        sys.exit(0)

    # Parse command line arguments
    quiet_mode = "--quiet" in sys.argv or "-q" in sys.argv
    auto_minimize = "--minimize" in sys.argv or "-m" in sys.argv

    # Parse port argument
    port = None
    for i, arg in enumerate(sys.argv):
        if arg.startswith("--port="):
            try:
                port = int(arg.split("=")[1])
            except ValueError:
                print(f"❌ Invalid port value: {arg.split('=')[1]}")
                sys.exit(1)
            break
        elif arg == "-p" and i + 1 < len(sys.argv):
            try:
                port = int(sys.argv[i + 1])
            except ValueError:
                print(f"❌ Invalid port value: {sys.argv[i + 1]}")
                sys.exit(1)
            break

    # Parse account argument
    account = None
    for i, arg in enumerate(sys.argv):
        if arg.startswith("--account="):
            account = arg.split("=")[1]
            break
        elif arg == "-a" and i + 1 < len(sys.argv):
            account = sys.argv[i + 1]
            break

    # Print parsed arguments (only if not in quiet mode)
    if not quiet_mode:
        print("🚀 Starting MT5 Trading Application...")
        if port:
            print(f"📡 Webhook port: {port}")
        if account:
            print(f"💼 Account: {account}")
        if auto_minimize:
            print("📍 Auto-minimize enabled")

    # Start the application
    MyApp(quiet_mode=quiet_mode, auto_minimize=auto_minimize, port=port, account=account)
