#!/usr/bin/env python3
"""
Direct test of AI analysis functionality to debug the API key issue
"""

import os
import sys

# Load environment variables first
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ Environment variables loaded from .env file")
except ImportError:
    # Manual loading
    env_path = os.path.join(os.getcwd(), '.env')
    if os.path.exists(env_path):
        with open(env_path, 'r') as f:
            for line in f:
                if '=' in line and not line.strip().startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
        print("✅ Environment variables loaded manually from .env file")

# Import application components
from App.config import Config
from App.util import Util

def test_direct_ai_analysis():
    """Test AI analysis directly"""
    print("🧪 Testing Direct AI Analysis")
    print("=" * 40)
    
    try:
        # Initialize components
        config = Config()
        util = Util(config)
        
        print("✅ Application components initialized")
        
        # Check API key
        api_key = os.getenv('OPENAI_API_KEY')
        if api_key:
            print(f"✅ API key found: {api_key[:10]}...{api_key[-4:]}")
        else:
            print("❌ API key not found")
            return False
        
        # Test chart data retrieval
        print("\n📊 Testing chart data retrieval...")
        try:
            chart_data = util.get_multi_timeframe_data("XAUUSD", ["H1"], 10)
            if chart_data:
                print(f"✅ Chart data retrieved: {chart_data['total_bars']} bars")
            else:
                print("❌ Failed to retrieve chart data")
                return False
        except Exception as e:
            print(f"❌ Chart data error: {e}")
            return False
        
        # Test AI analysis
        print("\n🤖 Testing AI analysis...")
        try:
            analysis_result = util.perform_ai_analysis(
                chart_data,
                "Provide a brief analysis of XAUUSD",
                "gpt",
                "",  # No image
                "XAUUSD",
                ["H1"],
                True  # Use signal format
            )
            
            if analysis_result.get("error"):
                print(f"❌ AI analysis failed: {analysis_result.get('message')}")
                return False
            else:
                print("✅ AI analysis successful!")
                print(f"   - AI Provider: {analysis_result.get('ai_provider')}")
                print(f"   - Analysis length: {len(analysis_result.get('analysis', ''))} characters")
                print(f"   - Structured signal: {analysis_result.get('structured_signal', False)}")
                
                # Show first 200 characters of analysis
                analysis_text = analysis_result.get('analysis', '')
                if analysis_text:
                    print(f"   - Analysis preview: {analysis_text[:200]}...")
                
                return True
                
        except Exception as e:
            print(f"❌ AI analysis error: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Test setup error: {e}")
        return False

def test_gpt_api_direct():
    """Test GPT API call directly"""
    print("\n🧪 Testing GPT API Direct Call")
    print("=" * 40)
    
    try:
        # Initialize components
        config = Config()
        util = Util(config)
        
        # Test direct GPT API call
        print("🤖 Making direct GPT API call...")
        
        test_prompt = """
        Analyze this test request and respond in the following format:
        
        Signal ID: test123
        C.GPT 
        Symbol: XAUUSD 
        Status: PENDING
        Signal: Buy Limit 
        Price: 2000.00
        SL: 1995.00
        TP1: 2005.00
        TP2: 2010.00 
        TP3: 2015.00 
        TP4: 2020.00 
        TP5: 2025.00
        """
        
        result = util.call_gpt_api(test_prompt)
        
        if result.get("error"):
            print(f"❌ GPT API call failed: {result.get('message')}")
            return False
        else:
            print("✅ GPT API call successful!")
            analysis = result.get("analysis", "")
            print(f"   - Response length: {len(analysis)} characters")
            print(f"   - Response preview: {analysis[:300]}...")
            
            # Test signal parsing
            print("\n📋 Testing signal parsing...")
            signal_data = util.parse_signal_format(analysis)
            if signal_data:
                print("✅ Signal parsing successful!")
                print(f"   - Signal ID: {signal_data.get('signal_id', 'N/A')}")
                print(f"   - Signal Type: {signal_data.get('signal_type', 'N/A')}")
                print(f"   - Entry Price: {signal_data.get('entry_price', 'N/A')}")
            else:
                print("⚠️ Signal parsing failed or no structured data found")
            
            return True
            
    except Exception as e:
        print(f"❌ Direct GPT API test error: {e}")
        return False

def main():
    """Run direct AI analysis tests"""
    print("🚀 Direct AI Analysis Test Suite")
    print("=" * 50)
    
    # Check environment first
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ OPENAI_API_KEY not found in environment")
        print("Make sure to run this script from the same directory as your .env file")
        return
    
    print(f"✅ OPENAI_API_KEY found: {api_key[:10]}...{api_key[-4:]}")
    
    # Test direct GPT API call first
    gpt_success = test_gpt_api_direct()
    
    # Test full AI analysis if GPT works
    if gpt_success:
        analysis_success = test_direct_ai_analysis()
    else:
        analysis_success = False
    
    print("\n📋 Test Results Summary:")
    print(f"  - Direct GPT API Call: {'✅ PASS' if gpt_success else '❌ FAIL'}")
    print(f"  - Full AI Analysis: {'✅ PASS' if analysis_success else '❌ FAIL'}")
    
    if gpt_success and analysis_success:
        print("\n🎉 All tests passed! AI analysis should work in the application.")
        print("\n💡 If it still fails in the GUI, try:")
        print("1. Restart the application completely")
        print("2. Check that the application is loading .env on startup")
        print("3. Look for any error messages in the application logs")
    else:
        print("\n⚠️ Some tests failed. Check the API key and network connection.")

if __name__ == "__main__":
    main()
