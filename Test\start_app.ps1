# MyApp PowerShell Launcher
# Right-click and "Run with PowerShell" or double-click if execution policy allows

Write-Host "Starting MyApp..." -ForegroundColor Green
Write-Host ""

# Get script directory
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $scriptDir

# Check if virtual environment exists
if (-not (Test-Path "venv\Scripts\Activate.ps1")) {
    Write-Host "ERROR: Virtual environment not found!" -ForegroundColor Red
    Write-Host "Please make sure 'venv' folder exists in the same directory as this script." -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

# Activate virtual environment
Write-Host "Activating virtual environment..." -ForegroundColor Yellow
& "venv\Scripts\Activate.ps1"

# Check if main.py exists
if (-not (Test-Path "main.py")) {
    Write-Host "ERROR: main.py not found!" -ForegroundColor Red
    Write-Host "Please make sure main.py is in the same directory as this script." -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

# Run the application
Write-Host "Starting application..." -ForegroundColor Green
Write-Host ""

try {
    python main.py
}
catch {
    Write-Host "Error running application: $_" -ForegroundColor Red
    Read-Host "Press Enter to exit"
}
