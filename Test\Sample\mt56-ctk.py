import os
import MetaTrader5 as mt5
from dotenv import load_dotenv
import customtkinter as ctk

# Load .env
load_dotenv()

accounts = {
    "Demo 1": "DEMO1",
    "Real 1": "REAL1",
    "Real 2": "REAL2"
}

# Connect to MT5
def connect_to_mt5(account_key):
    prefix = accounts[account_key]
    login = int(os.getenv(f'{prefix}_LOGIN'))
    pwd = os.getenv(f'{prefix}_PWD')
    server = os.getenv(f'{prefix}_SERVER')
    path = "C:\\Program Files\\MetaTrader 5\\terminal64.exe"
    
    if not mt5.initialize(path=path):
        status_label.configure(text="MT5 initialization failed", text_color="red")
        return
    
    if mt5.login(login, pwd, server):
        status_label.configure(text=f"✅ Logged in: {server}", text_color="green")
    else:
        status_label.configure(text=f"❌ Login failed: {mt5.last_error()}", text_color="red")

# Open trade (Buy/Sell)
def open_trade(order_type):
    symbol = symbol_var.get()
    lot_size = lot_size_var.get()
    
    if order_type == "buy":
        order = mt5.ORDER_TYPE_BUY
    else:
        order = mt5.ORDER_TYPE_SELL
    
    # Open buy/sell order (simplified version)
    price = mt5.symbol_info_tick(symbol).ask if order_type == "buy" else mt5.symbol_info_tick(symbol).bid
    request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": lot_size,
        "type": order,
        "price": price,
        "sl": 0,
        "tp": 0,
        "deviation": 10,
        "magic": 234000,
        "comment": "Test trade",
        "type_filling": mt5.ORDER_FILLING_IOC,
        "type_time": mt5.ORDER_TIME_GTC
    }

    result = mt5.order_send(request)
    if result.retcode != mt5.TRADE_RETCODE_DONE:
        status_label.configure(text=f"❌ Trade failed: {result.comment}", text_color="red")
    else:
        status_label.configure(text=f"✅ Trade successful: {order_type} {symbol}", text_color="green")

# Tab functions
def account_tab():
    # This tab will show account info and login status
    account_info_label = ctk.CTkLabel(account_tab_frame, text="Account Info", font=("Arial", 16))
    account_info_label.pack(pady=10)

    account_label = ctk.CTkLabel(account_tab_frame, text=f"Logged in as: {accounts[selected_account_var.get()]}")
    account_label.pack()

    # Account login status and action
    connect_button = ctk.CTkButton(account_tab_frame, text="Connect", command=lambda: connect_to_mt5(selected_account_var.get()))
    connect_button.pack(pady=10)

def trade_tab():
    # This tab will allow users to place trades (buy/sell)
    trade_label = ctk.CTkLabel(trade_tab_frame, text="Trade", font=("Arial", 16))
    trade_label.pack(pady=10)

    # Dropdown for selecting symbol
    symbol_label = ctk.CTkLabel(trade_tab_frame, text="Symbol:")
    symbol_label.pack(pady=5)
    symbol_var.set("EURUSD")  # Default symbol
    symbol_dropdown = ctk.CTkOptionMenu(trade_tab_frame, values=["EURUSD", "GBPUSD", "USDJPY"], variable=symbol_var)
    symbol_dropdown.pack()

    # Dropdown for selecting lot size
    lot_size_label = ctk.CTkLabel(trade_tab_frame, text="Lot Size:")
    lot_size_label.pack(pady=5)
    lot_size_var.set(0.1)  # Default lot size
    lot_size_slider = ctk.CTkSlider(trade_tab_frame, from_=0.01, to=10.0, number_of_steps=100, variable=lot_size_var)
    lot_size_slider.pack(pady=10)

    # Buttons to open trade (buy/sell)
    buy_button = ctk.CTkButton(trade_tab_frame, text="Buy", command=lambda: open_trade("buy"))
    buy_button.pack(pady=10)
    sell_button = ctk.CTkButton(trade_tab_frame, text="Sell", command=lambda: open_trade("sell"))
    sell_button.pack(pady=10)

# Initialize customtkinter
ctk.set_appearance_mode("dark")  # Set dark mode for the GUI
ctk.set_default_color_theme("blue")  # Set blue theme

# Create main window
root = ctk.CTk()
root.geometry("600x400")
root.title("Trading Platform")

# Create Tabview
tabview = ctk.CTkTabview(root)
tabview.pack(pady=20, padx=20, fill="both", expand=True)

# Add tabs for account and trading
account_tab_frame = tabview.add("Account")
trade_tab_frame = tabview.add("Trade")

# Set up Account Tab
selected_account_var = ctk.StringVar(value="Real 2")
account_tab()

# Set up Trade Tab
symbol_var = ctk.StringVar()
lot_size_var = ctk.DoubleVar()
trade_tab()

# Add a status label at the bottom to show messages
status_label = ctk.CTkLabel(root, text="Status: Ready", text_color="yellow", font=("Arial", 12))
status_label.pack(pady=10)

root.mainloop()
