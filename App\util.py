import MetaTrader5 as mt5
import customtkinter as ctk
import tkinter as tk
import pandas as pd
import talib as ta
import requests
import time
from datetime import datetime, time as dtime
import logging
import os
import gc
import threading
from functools import wraps
import signal
from App.debug_logger import DebugLogger
import numpy as np
import base64
import json
import random
import string
import urllib.parse
import psutil
import subprocess

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # If python-dotenv is not installed, try to load .env manually
    env_path = os.path.join(os.getcwd(), '.env')
    if os.path.exists(env_path):
        with open(env_path, 'r') as f:
            for line in f:
                if '=' in line and not line.strip().startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
# ===========================
# Timeout and Safety Decorators
# ===========================
def timeout_handler(signum, frame):
    raise TimeoutError("Operation timed out")

def with_timeout(seconds=5):
    """Decorator to add timeout to functions"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                # Set timeout signal (only works on Unix-like systems)
                if hasattr(signal, 'SIGALRM'):
                    old_handler = signal.signal(signal.SIGALRM, timeout_handler)
                    signal.alarm(seconds)

                result = func(*args, **kwargs)

                if hasattr(signal, 'SIGALRM'):
                    signal.alarm(0)  # Cancel alarm
                    signal.signal(signal.SIGALRM, old_handler)

                return result
            except TimeoutError:
                print(f"Function {func.__name__} timed out after {seconds} seconds")
                return None
            except Exception as e:
                print(f"Error in {func.__name__}: {e}")
                return None
        return wrapper
    return decorator

def safe_mt5_operation(func):
    """Decorator to safely handle MT5 operations"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            # Check MT5 connection first
            if not self.initialize_mt5():
                print(f"MT5 not initialized for {func.__name__}")
                return None

            result = func(*args, **kwargs)
            return result
        except Exception as e:
            print(f"MT5 operation error in {func.__name__}: {e}")
            return None
    return wrapper

# ===========================
# Class: Util
# ===========================
class Util:
    def __init__(self, config):
        self.config = config
        self._mt5_lock = threading.Lock()  # Thread safety for MT5 operations

        # Initialize debug logger
        self.debug_logger = DebugLogger(config, self)

        # Set up logging
        logging.basicConfig(
            filename=os.path.join("Logs", datetime.now().strftime('%Y-%m-%d') + '.txt'),
            level=logging.INFO,
            format='%(asctime)s [%(levelname)s] %(message)s',
        )

    def initialize_mt5(self): 
        path = os.getenv(f"{self.config.prefix}_PATH" , r"C:\\Program Files\\MT\\DEMO1\\terminal64.exe")
        return mt5.initialize(path=path, portable=True)

    def connect_to_mt5(self, prefix, quiet_mode=True):
        
        # prefix = self.config.prefix
        login = int(os.getenv(f"{prefix}_LOGIN"))
        pwd = os.getenv(f"{prefix}_PWD")
        server = os.getenv(f"{prefix}_SERVER")
        timezone = os.getenv(f"{prefix}_TZ")
        path = os.getenv(f"{prefix}_PATH" , r"C:\\Program Files\\MT\\DEMO1\\terminal64.exe")
        # path = os.getenv("MT5_PATH" , "C:\\Program Files\\MetaTrader 5\\terminal64.exe")
        self.config.symbol_posfix = os.getenv(f'{prefix}_SYMBOL_POSTFIX')
        # self.config.symbol_posfix.set(os.getenv(f'{prefix}_SYMBOL_POSTFIX'))

        self.config.prefix = prefix
        self.config.path = path
 
        # Start MT5 in quiet mode if requested
        if quiet_mode:
            self.start_mt5(path, quiet_mode)

        # if not self.initialize_mt5():
        # if not mt5.initialize(path=path + " /portable"):
        if not mt5.initialize(path=path, portable=True):
            # self.account_status_label.configure(text="MT5 initialization failed", text_color="red")
            self.set_status_label("MT5 initialization failed", "red")
            return
        
        if mt5.login(login, pwd, server):
            # self.account_status_label.configure(text=f"✅ Logged in: {server} (TZ-{timezone})", text_color="lime")
            self.set_status_label(f"✅ Logged in: {server} (TZ-{timezone})", "lime")
        else:
            # self.account_status_label.configure(text=f"❌ Login failed: {prefix}_LOGIN {mt5.last_error()}", text_color="red")
            self.set_status_label(f"❌ Login failed: {prefix}_LOGIN {mt5.last_error()}", "red")
        
    def is_mt5_running(self):
        """Check if MetaTrader 5 is already running"""
        for process in psutil.process_iter(attrs=["name"]):
            if "terminal64.exe" in process.info["name"]:
                return True
        return False

    def start_mt5(self, path, quiet_mode):
        """ Start MT5 in the background if it's not running """
        if not self.is_mt5_running():
            if not quiet_mode:
                print("Starting MT5...")
            subprocess.Popen(
                path,
                # path + " /portable",
                # shell=True,
                creationflags=subprocess.CREATE_NO_WINDOW  # Hide MT5 window
            )
            time.sleep(5)  # Wait for MT5 to initialize

    def print_all(self ,df, option = 1): 
        if option == 1:
            # Option 1: Temporarily show all rows 
            # with pd.option_context('display.max_rows', None, 'display.max_columns', None):
            with pd.option_context('display.max_rows', None):
                print(df)
            return
        # Option 2: Permanently change display settings (for current session) 
        pd.set_option('display.max_rows', None)
        # pd.set_option('display.max_columns', None)
        print(df)
    
    def get_symbol(self, symbol_var):
        if isinstance(symbol_var, ctk.StringVar):
            symbol_var = symbol_var.get()
        if len(symbol_var) < 4:
            symbol_var = self.config.symbols[symbol_var]
        return symbol_var + self.config.symbol_posfix

    def onchange_symbol(self, symbol):
        symbol =  self.config.symbols[symbol] + self.config.symbol_posfix
        point = mt5.symbol_info(symbol).point
        self.set_status_label(f"⏳ Changed to {symbol} point {point} ({format(point, '.6f')})", "yellow")

        # Only print in verbose mode (not quiet mode)
        if not (hasattr(self.config, 'quiet_mode') and self.config.quiet_mode):
            print(f"⏳ Changed to {symbol} point {point} ({format(point, '.6f')})")

    def get_data(self, symbol, timeframe, bars=100):
        rates = mt5.copy_rates_from_pos(symbol, timeframe, 0, bars)
        df = pd.DataFrame(rates)
        df['time'] = pd.to_datetime(df['time'], unit='s')
        return df

    def set_status_label(self, text, text_color = 'white'):
        self.config.status_label.configure(text=text, text_color=text_color)

    def add_status_frame(self, text, text_color='white', level=3):
        """
        Add status message with log level filtering

        Args:
            text (str): Message text
            text_color (str): Text color
            level (int): Importance level 1-5 (1=Critical, 2=Error, 3=Warning, 4=Info, 5=Debug)
        """
        # Check if application is shutting down
        if hasattr(self.config, 'is_shutting_down') and self.config.is_shutting_down:
            # During shutdown, only log to console and file
            status_text = f"{time.strftime('%H:%M:%S')} : {text}"
            logging.info(text)
            print(status_text)
            return

        # Check if message should be displayed based on log level
        current_log_level = getattr(self.config, 'log_level', 3)  # Default to level 3

        if level > current_log_level:
            return  # Skip message if level is too low

        # Check if quiet mode is enabled and modify text accordingly
        if hasattr(self.config, 'quiet_mode') and self.config.quiet_mode:
            # In quiet mode, make messages more compact
            text = self.make_compact_message(text)

        status_text = f"{time.strftime('%H:%M:%S')} : {text}"

        # Thread-safe GUI update using after() method
        def update_gui():
            try:
                # Enhanced safety checks for frame existence
                if not hasattr(self.config, 'status_scroll_frame') or not self.config.status_scroll_frame:
                    return

                # Check if frame and its parent still exist
                try:
                    if not self.config.status_scroll_frame.winfo_exists():
                        return
                    # Also check if parent window exists
                    if not self.config.status_scroll_frame.winfo_toplevel().winfo_exists():
                        return
                except (AttributeError, tk.TclError):
                    return  # Frame or parent no longer exists

                # Create label with additional error handling
                try:
                    label = ctk.CTkLabel(self.config.status_scroll_frame, text=status_text, anchor="w", justify="left", text_color=text_color)
                except Exception as e:
                    print(f"Failed to create status label: {e}")
                    return

                # Safely add the label with improved error handling
                try:
                    if self.config.status_scroll_labels:
                        # Clean up any invalid references first
                        self.config.status_scroll_labels = [l for l in self.config.status_scroll_labels
                                                          if self._is_widget_valid(l)]

                        if self.config.status_scroll_labels and self._is_widget_valid(self.config.status_scroll_labels[0]):
                            label.pack(fill="x", anchor="w", before=self.config.status_scroll_labels[0])
                        else:
                            label.pack(fill="x", anchor="w")
                    else:
                        label.pack(fill="x", anchor="w")

                    # Only add to list if packing succeeded
                    self.config.status_scroll_labels.insert(0, label)

                except Exception as e:
                    print(f"Failed to pack status label: {e}")
                    # Try to destroy the label if packing failed
                    try:
                        label.destroy()
                    except:
                        pass
                    return

                # Safe cleanup of old labels with improved error handling
                self._cleanup_old_status_labels()

            except Exception as e:
                print(f"GUI update error: {e}")

        # Schedule GUI update on main thread with enhanced safety
        if hasattr(self.config, 'status_scroll_frame') and self.config.status_scroll_frame:
            try:
                # Check if the frame still exists and main loop is running
                if self._is_widget_valid(self.config.status_scroll_frame):
                    # Use a more robust scheduling approach
                    try:
                        # Try after_idle first (safest for GUI updates)
                        self.config.status_scroll_frame.after_idle(update_gui)
                    except (AttributeError, tk.TclError):
                        # Fallback to after(0) if after_idle fails
                        try:
                            self.config.status_scroll_frame.after(0, update_gui)
                        except (AttributeError, tk.TclError):
                            # Last resort - just log to console
                            print(status_text)
                else:
                    # Frame doesn't exist, just log to console
                    print(status_text)
            except Exception as e:
                # If all GUI updates fail, just log to console
                print(f"{status_text} (GUI update failed: {e})")
        else:
            # No GUI available, just log to console
            print(status_text)

        logging.info(text)

        # Print to console only if level is high enough
        if level <= current_log_level:
            print(status_text)

    def send_to_google_form(self, symbol, action, entry, sl, tp, lot, signal_id="", comment="", reason="", risk=""):
        """
        Send order data to Google Form for tracking and analysis

        Args:
            symbol: Trading symbol (e.g., XAUUSD)
            action: Order action (Buy Now, Sell Now, Buy Limit, etc.)
            entry: Entry price
            sl: Stop loss price
            tp: Take profit price
            lot: Lot size
            signal_id: Signal ID (optional)
            comment: Order comment (optional)
            reason: Trade reason/analysis (optional)
            risk: Risk factors and management (optional)
        """
        try:
            # Format the URL with parameters
            url = self.config.GOOGLE_FORM_URL.format(
                Symbol=urllib.parse.quote(str(symbol)),
                Action=urllib.parse.quote(str(action)),
                Entry=urllib.parse.quote(str(entry)),
                SL=urllib.parse.quote(str(sl)),
                TP=urllib.parse.quote(str(tp)),
                Lot=urllib.parse.quote(str(lot)),
                SignalID=urllib.parse.quote(str(signal_id)),
                Comment=urllib.parse.quote(str(comment)),
                Reason=urllib.parse.quote(str(reason)),
                Risk=urllib.parse.quote(str(risk))
            )

            # Submit data directly to Google Form
            # Extract form ID from the URL
            form_id_start = url.find('/d/e/') + 5
            form_id_end = url.find('/viewform')
            form_id = url[form_id_start:form_id_end]

            # Create the form submission URL
            submit_url = f"https://docs.google.com/forms/d/e/{form_id}/formResponse"

            # Prepare form data for submission with separate Reason and Risk fields
            form_data = {
                "entry.178506718": str(symbol),
                "entry.645304246": str(action),
                "entry.785671803": str(entry),
                "entry.898028705": str(sl),
                "entry.1523790774": str(tp),
                "entry.381285031": str(lot),
                "entry.1073635155": str(signal_id),
                "entry.1148525053": str(comment),
                "entry.398055293": str(reason),
                "entry.305299530": str(risk)
            }

            # Submit the form data
            response = requests.post(submit_url, data=form_data, timeout=10)

            if response.status_code == 200:
                self.add_status_frame(f"📊 Order data submitted to Google Form: {symbol} {action}", "green")
                self.debug_logger.debug_print(f"Google Form submitted successfully")
                return True
            else:
                self.add_status_frame(f"⚠️ Google Form submission failed: HTTP {response.status_code}", "yellow")
                self.debug_logger.debug_print(f"Google Form submission URL: {submit_url}")
                return False

        except requests.exceptions.Timeout:
            self.add_status_frame("⚠️ Google Form request timed out", "yellow")
            return False
        except Exception as e:
            self.add_status_frame(f"❌ Google Form error: {e}", "red")
            self.debug_logger.debug_print(f"Google Form error details: {e}")
            return False

    def _is_widget_valid(self, widget):
        """Check if a widget is valid and still exists"""
        try:
            return widget and hasattr(widget, 'winfo_exists') and widget.winfo_exists()
        except (AttributeError, tk.TclError):
            return False

    def _cleanup_old_status_labels(self):
        """Safely cleanup old status labels with improved error handling"""
        try:
            # First, clean up any invalid references
            valid_labels = []
            for label in self.config.status_scroll_labels:
                if self._is_widget_valid(label):
                    valid_labels.append(label)
                # Invalid labels are automatically excluded

            self.config.status_scroll_labels = valid_labels

            # Now remove excess labels
            while len(self.config.status_scroll_labels) > self.config.LIMIT_LOGS:
                try:
                    old_label = self.config.status_scroll_labels.pop()
                    if self._is_widget_valid(old_label):
                        # Schedule destruction on main thread to avoid race conditions
                        try:
                            old_label.after_idle(old_label.destroy)
                        except (AttributeError, tk.TclError):
                            # Fallback to immediate destruction
                            try:
                                old_label.destroy()
                            except (AttributeError, tk.TclError):
                                pass  # Already destroyed
                except (IndexError, AttributeError):
                    break  # No more labels to remove

        except Exception as e:
            print(f"Error during label cleanup: {e}")
            # Reset the list if cleanup fails completely
            self.config.status_scroll_labels = []

    def make_compact_message(self, text):
        """Make status messages more compact for quiet mode"""
        # Define patterns for common verbose messages and their compact versions
        compact_patterns = {
            # Connection messages
            r"✅ Logged in: (.+) \(TZ-(.+)\)": r"✅ Connected: \1",
            r"❌ Login failed: (.+) (.+)": r"❌ Login failed",

            # Order processing messages
            r"Processing orders for (.+) with (.+) positions and (.+) pending orders": r"📦 Processing \1",
            r"Updated (.+) positions and (.+) pending orders": r"📦 Updated \1+\2",

            # Webhook messages
            r"Webhook server started on (.+):(.+)": r"🌐 Webhook: :\2",
            r"Webhook server stopped successfully": r"🌐 Webhook stopped",

            # Auto SL/TP messages
            r"Auto SL to BE (.+) for (.+) positions": r"🎯 Auto SL: \2 pos",
            r"Auto TP (.+) for (.+) positions": r"🎯 Auto TP: \2 pos",

            # General processing messages
            r"(.+) completed successfully with (.+) changes": r"\1: \2 changes",
            r"(.+) processing completed": r"\1 done",
        }

        # Apply compact patterns
        import re
        for pattern, replacement in compact_patterns.items():
            text = re.sub(pattern, replacement, text)

        # Truncate very long messages
        if len(text) > 80:
            text = text[:77] + "..."

        return text

    def safe_mt5_call(self, operation, *args, **kwargs):
        """Thread-safe wrapper for MT5 operations with timeout"""
        with self._mt5_lock:
            try:
                # Quick connection check
                if not self.initialize_mt5():
                    error_code = mt5.last_error()
                    self.add_status_frame(f"❌ MT5 not initialized: {error_code}", "red", level=1)
                    return None

                # Execute the operation
                result = operation(*args, **kwargs)

                # Check if operation failed and get error details
                if result is None and hasattr(mt5, 'last_error'):
                    error_code = mt5.last_error()
                    if error_code != (0, 'Success'):
                        self.add_status_frame(f"❌ MT5 operation failed: {error_code}", "yellow")

                return result
            except Exception as e:
                self.add_status_frame(f"❌ MT5 operation exception: {e}", "red")
                return None

    def safe_order_send(self, request):
        """Thread-safe wrapper specifically for mt5.order_send"""
        with self._mt5_lock:
            try:
                # Quick connection check
                if not self.initialize_mt5():
                    error_code = mt5.last_error()
                    self.add_status_frame(f"❌ MT5 not initialized: {error_code}", "red")
                    return None

                # Send the order
                result = mt5.order_send(request)

                # Check if operation failed and get error details
                if result is None:
                    error_code = mt5.last_error()
                    self.add_status_frame(f"❌ Order send failed: {error_code}", "red")

                return result
            except Exception as e:
                self.add_status_frame(f"❌ Order send exception: {e}", "red")
                return None

    def cleanup_memory(self):
        """Perform memory cleanup to prevent memory leaks"""
        try:
            # Force garbage collection
            gc.collect()

            # Clean up old status labels if too many exist
            if hasattr(self.config, 'status_scroll_labels') and len(self.config.status_scroll_labels) > self.config.LIMIT_LOGS * 0.8:
                excess_count = len(self.config.status_scroll_labels) - int(self.config.LIMIT_LOGS * 0.7)
                removed_count = 0

                # Safely remove labels from the end of the list
                for _ in range(excess_count):
                    if self.config.status_scroll_labels:
                        try:
                            old_label = self.config.status_scroll_labels.pop()
                            # Check if label still exists before destroying
                            if hasattr(old_label, 'winfo_exists') and old_label.winfo_exists():
                                old_label.destroy()
                            removed_count += 1
                        except Exception as e:
                            # Label already destroyed or invalid, just continue
                            print(f"Label cleanup error: {e}")
                            continue

                if removed_count > 0:
                    # Use print instead of add_status_frame to avoid recursion
                    print(f"🧹 Memory cleanup: Removed {removed_count} old status labels")

        except Exception as e:
            print(f"Memory cleanup error: {e}")

    def is_in_restricted_time(self):
        now_utc = datetime.utcnow().time()
        return dtime(21, 0) <= now_utc < dtime(22, 0)
    
    def get_limited_positions(self, symbol, filter_comment = "", limit=10):
        positions = mt5.positions_get(symbol=symbol)
        if positions is None:
            return []
        # กรองเฉพาะ TF ที่ต้องการ
        filtered = [pos for pos in positions if pos.comment.startswith(filter_comment)]
        # คืนกลับเฉพาะจำนวนที่ต้องการ
        return filtered[:limit]
    
    def get_volatility(self, symbol, timeframe=mt5.TIMEFRAME_M15, period=14):
        rates = mt5.copy_rates_from_pos(symbol, timeframe, 0, period + 1)
        if rates is None or len(rates) < period + 1:
            return None
        
        df = pd.DataFrame(rates)
        atr = ta.volatility.AverageTrueRange(high=df['high'], low=df['low'], close=df['close'], window=period)
        atr_value = atr.average_true_range().iloc[-1]
        return atr_value
    
    # 📤 ฟังก์ชันส่งข้อความไป LINE
    def send_line_message(self, message):
        if not self.LINE_TOKEN or self.LINE_TOKEN == '':
            return
        url = 'https://notify-api.line.me/api/notify'
        headers = {'Authorization': f'Bearer {self.config.LINE_TOKEN}'}
        data = {'message': message}
        requests.post(url, headers=headers, data=data)

    def send_telegram_message(bot_token, chat_id, message):
        # สร้าง Bot: เปิดกล่องค้นหาใน Telegram พิมพ์ @BotFather 
        # กด Start พิมพ์ /newbot → ตั้งชื่อ + username → ได้ "Bot Token"
        # เปิดแชทกับบอทของคุณ แล้วส่งข้อความใด ๆ
        # หาค่า chat_id จาก API
        url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
        payload = {
            'chat_id': chat_id,
            'text': message,
            'parse_mode': 'HTML'
        }
        requests.post(url, data=payload)
        
    def send_discord_message(webhook_url, message):
        # ไปที่ Discord → สร้าง/เลือกห้อง → ตั้งค่า → "Integrations" → "Webhooks"
        # สร้าง Webhook และก๊อป URL มาใช้
        data = {
            "content": message
        }
        requests.post(webhook_url, json=data)

    def send_order(self, order_type, symbol, lot, entry, sl, tp, comment="Script", reason="", risk="", signal_id=""):
        try:
            self.add_status_frame(f"🔄 Preparing order: {order_type} {symbol} {lot} lots", "cyan")

            # Get price safely
            tick_info = self.safe_mt5_call(mt5.symbol_info_tick, symbol)
            if not tick_info:
                self.add_status_frame(f"❌ Failed to get price for {symbol}", "red")
                return None

            price = tick_info.bid if order_type == "Buy Now" else (tick_info.ask if order_type == "Sell Now" else entry)
            self.add_status_frame(f"📊 Price info: bid={tick_info.bid}, ask={tick_info.ask}, using={price}", "cyan")

            # Check if mappings exist
            if order_type not in self.config.action_type_mapping:
                self.add_status_frame(f"❌ Unknown order type: {order_type}", "red")
                return None

            if order_type not in self.config.order_type_mapping:
                self.add_status_frame(f"❌ Unknown order type mapping: {order_type}", "red")
                return None

            # Get symbol info safely
            symbol_info = self.safe_mt5_call(mt5.symbol_info, symbol)
            if not symbol_info:
                self.add_status_frame(f"❌ Failed to get point for {symbol}", "red")
                return None

            point = symbol_info.point  # e.g., 0.01 for XAUUSD
            original_sl_points = int(abs(float(entry) -  float(sl)) / point)  # e.g., 100 points

            request = {
                "action": self.config.action_type_mapping[order_type],
                "symbol": symbol,
                "volume": float(lot),
                "type": self.config.order_type_mapping[order_type],
                "price": float(price),
                "sl": float(sl),
                "tp": float(tp),
                "deviation": 10,
                "magic": original_sl_points, #161032,  # Unique identifier for your EA or script
                "comment": comment[:31],
                "type_filling": mt5.ORDER_FILLING_FOK,
            }

            self.add_status_frame(f"📋 Order request prepared: {request}", "cyan")

            # Send the order using the safe wrapper
            self.add_status_frame(f"📤 Sending order request...", "cyan")
            result = self.safe_order_send(request)
            self.add_status_frame(f"📤 Order send result: {result}", "cyan")

        except Exception as e:
            self.add_status_frame(f"❌ Order preparation failed: {e}", "red")
            return None

        if not result:
            self.add_status_frame(f"❌ Execute failed: No return value", "yellow")
            return None
        elif result.retcode != mt5.TRADE_RETCODE_DONE:
            self.add_status_frame(f"❌ Execute failed: {result.comment}", "yellow")
            return result  # Return result even if failed, so caller can check retcode
        else:
            if order_type in ["Buy Limit", "Buy Now", "Buy Stop"]:
                self.add_status_frame(f"🟢 Sending {order_type}: {symbol} lot {lot} @ {entry}, SL {sl}, TP {tp} - {comment}", "lime")
            elif order_type in ["Sell Limit", "Sell Now", "Sell Stop"]:
                self.add_status_frame(f"🔴 Sending {order_type}: {symbol} lot {lot} @ {entry}, SL {sl}, TP {tp} - {comment}", "red")
            else:
                self.add_status_frame(f"⚠️ Execute failed: Order type unknown - {comment}", "yellow")

            # Send order data to Google Form for tracking
            self.add_status_frame(f"📊 Preparing Google Form submission for {symbol} {order_type}", "cyan")
            try:
                google_form_result = self.send_to_google_form(
                    symbol=symbol,
                    action=order_type,
                    entry=entry,
                    sl=sl,
                    tp=tp,
                    lot=lot,
                    signal_id=signal_id,
                    comment=comment,
                    reason=reason,
                    risk=risk
                )
                if google_form_result:
                    self.add_status_frame(f"✅ Google Form submission successful for {symbol}", "green")
                else:
                    self.add_status_frame(f"❌ Google Form submission failed for {symbol}", "red")
            except Exception as e:
                self.add_status_frame(f"⚠️ Google Form integration error: {e}", "yellow")
                self.debug_logger.debug_print(f"Google Form error details: {str(e)}")

            return result  # Return successful result

    def positions_count(self, symbol, filter_comment = ""):
        orders = mt5.positions_get(symbol=symbol) if self.initialize_mt5() else [] 
        count_orders = {"All":0, "All_FT":0, "All_TF":0}
        for tf_name in self.config.timeframes:
            count_orders[tf_name] = {"B":0, "S":0}

        for order in orders:
            if order.symbol != symbol:
                continue

            count_orders["All"] += 1
            if order.comment.startswith(filter_comment) or filter_comment == "":
                count_orders["All_FT"] += 1
                for tf_name in self.config.timeframes:
                    if order.comment.startswith(filter_comment + tf_name):
                        count_orders[tf_name]["S" if order.type else "B"] += 1
                        # count_orders["All_TF"] += 1 

        return count_orders
    
    def calculate_atr_trailing_stop(self, symbol, order_type):
        """Calculate ATR-based trailing stop with proper swing detection

        Formula: Stop = min(H4 swing-low - 0.75×ATR(H4), D1 20-EMA - 0.5×ATR(D1))

        Enhanced with:
        - No same-bar moves: Only move stop after swing candle has closed
        - Proper swing detection with confirmation
        - Time-based validation

        Args:
            symbol: Trading symbol
            order_type: 0 for BUY, 1 for SELL

        Returns:
            Dict with calculated stop and metadata, or None if calculation fails
        """
        try:
            # Get H4 data for swing detection and ATR
            h4_rates = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_H4, 0, 100)
            if h4_rates is None or len(h4_rates) < 30:
                return None

            h4_df = pd.DataFrame(h4_rates)
            h4_df['time'] = pd.to_datetime(h4_df['time'], unit='s')

            # Calculate H4 ATR(14)
            h4_atr = ta.ATR(h4_df['high'], h4_df['low'], h4_df['close'], timeperiod=14)
            if h4_atr is None or len(h4_atr) == 0:
                return None
            h4_atr_value = h4_atr.iloc[-1]

            # Enhanced swing detection with confirmation
            # Look for swing low/high in bars -3 to -1 (excluding current bar)
            # This ensures the swing candle has closed
            swing_data = self._find_confirmed_swing(h4_df, order_type)
            if swing_data is None:
                return None

            swing_price = swing_data['price']
            swing_bar_time = swing_data['time']
            swing_bar_index = swing_data['index']

            # Check if this swing is from a closed candle (not current bar)
            current_time = pd.Timestamp.now()
            time_since_swing = (current_time - swing_bar_time).total_seconds()

            # Must be at least 4 hours old (one H4 candle) to be considered closed
            if time_since_swing < 4 * 3600:  # 4 hours in seconds
                self.debug_logger.debug_print(f"ATR: Swing too recent ({time_since_swing/3600:.1f}h), waiting for candle close")
                return None

            # Get D1 data for EMA and ATR
            d1_rates = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_D1, 0, 50)
            if d1_rates is None or len(d1_rates) < 20:
                return None

            d1_df = pd.DataFrame(d1_rates)
            d1_df['time'] = pd.to_datetime(d1_df['time'], unit='s')

            # Calculate D1 20-EMA
            d1_ema20 = ta.EMA(d1_df['close'], timeperiod=20)
            if d1_ema20 is None or len(d1_ema20) == 0:
                return None
            d1_ema20_value = d1_ema20.iloc[-1]

            # Calculate D1 ATR(14)
            d1_atr = ta.ATR(d1_df['high'], d1_df['low'], d1_df['close'], timeperiod=14)
            if d1_atr is None or len(d1_atr) == 0:
                return None
            d1_atr_value = d1_atr.iloc[-1]

            # Calculate stop levels based on order type
            if order_type == mt5.ORDER_TYPE_BUY:
                # For BUY: Stop below price
                # Component 1: H4 swing-low - 0.75 × ATR(H4)
                stop1 = swing_price - (0.75 * h4_atr_value)

                # Component 2: D1 20-EMA - 0.5 × ATR(D1)
                stop2 = d1_ema20_value - (0.5 * d1_atr_value)

                # Use minimum (tighter stop)
                calculated_stop = min(stop1, stop2)

                self.debug_logger.debug_print(f"ATR BUY Stop: H4_swing={swing_price:.5f} (bar {swing_bar_index}), H4_ATR={h4_atr_value:.5f}, Stop1={stop1:.5f}")
                self.debug_logger.debug_print(f"ATR BUY Stop: D1_EMA20={d1_ema20_value:.5f}, D1_ATR={d1_atr_value:.5f}, Stop2={stop2:.5f}")
                self.debug_logger.debug_print(f"ATR BUY Stop: Final={calculated_stop:.5f}, Swing Age: {time_since_swing/3600:.1f}h")

            else:  # SELL
                # For SELL: Stop above price
                # Component 1: H4 swing-high + 0.75 × ATR(H4)
                stop1 = swing_price + (0.75 * h4_atr_value)

                # Component 2: D1 20-EMA + 0.5 × ATR(D1)
                stop2 = d1_ema20_value + (0.5 * d1_atr_value)

                # Use maximum (tighter stop)
                calculated_stop = max(stop1, stop2)

                self.debug_logger.debug_print(f"ATR SELL Stop: H4_swing={swing_price:.5f} (bar {swing_bar_index}), H4_ATR={h4_atr_value:.5f}, Stop1={stop1:.5f}")
                self.debug_logger.debug_print(f"ATR SELL Stop: D1_EMA20={d1_ema20_value:.5f}, D1_ATR={d1_atr_value:.5f}, Stop2={stop2:.5f}")
                self.debug_logger.debug_print(f"ATR SELL Stop: Final={calculated_stop:.5f}, Swing Age: {time_since_swing/3600:.1f}h")

            # Return enhanced data with metadata
            return {
                'stop_price': calculated_stop,
                'swing_price': swing_price,
                'swing_time': swing_bar_time,
                'swing_index': swing_bar_index,
                'time_since_swing': time_since_swing,
                'h4_atr': h4_atr_value,
                'd1_ema20': d1_ema20_value,
                'd1_atr': d1_atr_value,
                'stop1': stop1,
                'stop2': stop2
            }

        except Exception as e:
            self.debug_logger.debug_print(f"ATR trailing stop calculation error: {e}")
            return None

    def set_trailing_sl(self, symbol, order_type="all"):
        """Manually trigger trailing stop loss calculation

        Args:
            symbol: Trading symbol (e.g., "XAUUSD", "XU")
            order_type: "all", "buy", or "sell"

        Returns:
            Result dictionary from update_SL_to_BE_by_point
        """
        try:
            # Get trailing stop type from Orders tab if available
            trailing_type = "Fixed"  # Default
            try:
                # Try to get trailing type from Orders tab
                if hasattr(self.config, 'orders_tab_ref') and hasattr(self.config.orders_tab_ref, 'trailing_stop_type'):
                    trailing_type = self.config.orders_tab_ref.trailing_stop_type.get()
            except:
                pass

            self.add_status_frame(f"🔄 Manual trailing SL calculation started for {symbol} ({order_type.upper()}) - Type: {trailing_type}", "cyan")

            # Convert symbol if needed (XU -> XAUUSD, etc.)
            full_symbol = self.get_symbol_from_short(symbol) if len(symbol) <= 2 else symbol

            # Apply trailing SL - for now use main function for all order types
            # TODO: Implement BUY/SELL filtering in future enhancement
            result = self.update_SL_to_BE_by_point(full_symbol, "", True, trailing_type)

            # Process results and show status
            if result:
                processed_count = result.get("All_FT", 0)
                if processed_count > 0:
                    self.add_status_frame(f"✅ Trailing SL applied to {processed_count} {order_type.upper()} positions for {symbol}", "green")
                else:
                    self.add_status_frame(f"ℹ️ No {order_type.upper()} positions found or conditions not met for {symbol}", "yellow")
            else:
                self.add_status_frame(f"❌ Failed to calculate trailing SL for {symbol}", "red")

            return result

        except Exception as e:
            self.add_status_frame(f"❌ Error in trailing SL calculation for {symbol}: {e}", "red")
            return None

    def update_SL_to_BE_by_point_filtered(self, symbol, filter_comment="", is_moving_tp=False, trailing_type="Fixed", order_type_filter=None):
        """Enhanced version of update_SL_to_BE_by_point with order type filtering

        Args:
            symbol: Trading symbol
            filter_comment: Filter by comment prefix
            is_moving_tp: Enable moving TP
            trailing_type: "Fixed" or "ATR"
            order_type_filter: "buy", "sell", or None for all

        Returns:
            Result dictionary with counts
        """
        try:
            # Get positions safely with timeout
            orders = self.safe_mt5_call(mt5.positions_get, symbol=symbol)
            if not orders:
                return {"All":0, "All_FT":0, "All_TF":0}

            # Filter by order type if specified
            if order_type_filter:
                if order_type_filter.lower() == "buy":
                    orders = [order for order in orders if order.type == mt5.ORDER_TYPE_BUY]
                elif order_type_filter.lower() == "sell":
                    orders = [order for order in orders if order.type == mt5.ORDER_TYPE_SELL]

            # If no orders after filtering, return empty result
            if not orders:
                return {"All":0, "All_FT":0, "All_TF":0}

            # Use the main function with filtered orders
            # We'll temporarily replace the orders and call the main function
            original_get_positions = mt5.positions_get

            def mock_positions_get(symbol=None):
                return orders

            # Temporarily replace the function
            mt5.positions_get = mock_positions_get

            try:
                result = self.update_SL_to_BE_by_point(symbol, filter_comment, is_moving_tp, trailing_type)
            finally:
                # Restore original function
                mt5.positions_get = original_get_positions

            return result

        except Exception as e:
            self.debug_logger.debug_print(f"Filtered trailing SL error: {e}")
            return {"All":0, "All_FT":0, "All_TF":0}

    def _find_confirmed_swing(self, df, order_type, lookback_bars=20, confirmation_bars=2):
        """Find confirmed swing high/low that has closed

        Args:
            df: H4 DataFrame with OHLC data
            order_type: 0 for BUY (find swing low), 1 for SELL (find swing high)
            lookback_bars: Number of bars to look back for swing
            confirmation_bars: Number of bars after swing for confirmation

        Returns:
            Dict with swing data or None if no confirmed swing found
        """
        try:
            if len(df) < lookback_bars + confirmation_bars + 1:
                return None

            # Exclude current bar (last bar) to ensure we only use closed candles
            closed_bars = df.iloc[:-1]  # Remove last bar

            if order_type == mt5.ORDER_TYPE_BUY:
                # Find swing low for BUY orders
                # Look in the range excluding the last few bars for confirmation
                search_range = closed_bars.iloc[-(lookback_bars + confirmation_bars):-confirmation_bars]

                if len(search_range) == 0:
                    return None

                # Find the lowest low in the search range
                swing_idx = search_range['low'].idxmin()
                swing_price = search_range.loc[swing_idx, 'low']
                swing_time = search_range.loc[swing_idx, 'time']

                # Confirm it's actually a swing low (lower than surrounding bars)
                swing_pos = search_range.index.get_loc(swing_idx)
                if swing_pos > 0 and swing_pos < len(search_range) - 1:
                    left_low = search_range.iloc[swing_pos - 1]['low']
                    right_low = search_range.iloc[swing_pos + 1]['low']

                    # Must be lower than both neighbors
                    if swing_price < left_low and swing_price < right_low:
                        return {
                            'price': swing_price,
                            'time': swing_time,
                            'index': swing_idx,
                            'type': 'swing_low'
                        }

            else:  # SELL orders
                # Find swing high for SELL orders
                search_range = closed_bars.iloc[-(lookback_bars + confirmation_bars):-confirmation_bars]

                if len(search_range) == 0:
                    return None

                # Find the highest high in the search range
                swing_idx = search_range['high'].idxmax()
                swing_price = search_range.loc[swing_idx, 'high']
                swing_time = search_range.loc[swing_idx, 'time']

                # Confirm it's actually a swing high (higher than surrounding bars)
                swing_pos = search_range.index.get_loc(swing_idx)
                if swing_pos > 0 and swing_pos < len(search_range) - 1:
                    left_high = search_range.iloc[swing_pos - 1]['high']
                    right_high = search_range.iloc[swing_pos + 1]['high']

                    # Must be higher than both neighbors
                    if swing_price > left_high and swing_price > right_high:
                        return {
                            'price': swing_price,
                            'time': swing_time,
                            'index': swing_idx,
                            'type': 'swing_high'
                        }

            return None

        except Exception as e:
            self.debug_logger.debug_print(f"Swing detection error: {e}")
            return None

    def update_SL_to_BE_by_point(self, symbol, filter_comment = "", is_moving_tp = False, trailing_type = "Fixed"):
        """ Auto update orders STOP LOSS to BREAKEVEN

        Args:
            symbol: Trading symbol
            filter_comment: Filter by comment prefix
            is_moving_tp: Enable moving TP
            trailing_type: "Fixed" for current formula or "ATR" for ATR-based trailing
        """
        try:
            # Get positions safely with timeout
            orders = self.safe_mt5_call(mt5.positions_get, symbol=symbol)
            if not orders:
                return {"All":0, "All_FT":0, "All_TF":0}

            count_orders = {"All":0, "All_FT":0, "All_TF":0}
            for tf_name in self.config.timeframes:
                count_orders[tf_name] = {"B":0, "S":0}

            for order in orders:
                if order.symbol != symbol:
                    continue

                count_orders["All"] += 1
                if order.comment.startswith(filter_comment) or filter_comment == "":
                    count_orders["All_FT"] += 1
                    for tf_name in self.config.timeframes:
                        if order.comment.startswith(filter_comment + tf_name):
                            count_orders[tf_name]["S" if order.type else "B"] += 1
                            # count_orders["All_TF"] += 1
                    entry_price = order.price_open
                    current_price = order.price_current
                    current_tp = order.tp
                    current_sl = order.sl
                    type_ = order.type  # 0 = BUY, 1 = SELL
                    magic_number = order.magic
                    

                    # Get symbol info safely
                    symbol_info = self.safe_mt5_call(mt5.symbol_info, order.symbol)
                    if not symbol_info:
                        continue

                    # Get original SL distance from magic number (stored as points)
                    if magic_number > 0 and magic_number != 155214:  # 155214 is default EA magic
                        # Magic number contains original SL distance in points
                        original_sl_points = magic_number
                        point_sl = original_sl_points * symbol_info.point

                        self.debug_logger.log_magic_number_info(
                            order.ticket, magic_number, original_sl_points, point_sl, order.symbol
                        )
                    else:
                        # Fallback to current calculation for orders without stored distance
                        point_sl = abs(entry_price - current_sl)
                        self.debug_logger.debug_print(f"#{order.ticket}: Using fallback SL distance calculation: {point_sl:.5f}")

                    # Validate point_sl to avoid division by zero or invalid values
                    if point_sl <= 0:
                        self.debug_logger.debug_print(f"#{order.ticket}: Invalid SL distance {point_sl}, skipping")
                        continue

                    point_tp = current_tp - entry_price / symbol_info.point
                    factor = (point_tp / original_sl_points) - 1
                    # factor = 2  # เริ่มจากคูณ 2
                    point = symbol_info.point
                    point_10 = point * 10
                    point_50 = point * 50

                    # Calculate point_be based on trailing type
                    if trailing_type == "Fixed":
                        # For Fixed trailing: trigger at 2/3 of the distance from entry to TP
                        tp_distance = abs(current_tp - entry_price)
                        point_be = tp_distance * (2.0 / 3.0)
                    else:
                        # For other trailing types: use the original formula
                        point_be = factor * point_sl

                    # point_sl = point * self.config.SL_POINTS 
                    # point_be = point * self.config.BE_POINTS 
                    new_sl = None
                    new_tp = current_tp

                    # Debug current state
                    self.debug_logger.debug_print(f"#{order.ticket}: Entry={entry_price:.5f}, Current={current_price:.5f}, SL={current_sl:.5f}, TP={current_tp:.5f}")
                    self.debug_logger.debug_print(f"#{order.ticket}: point_sl={point_sl:.5f}, point_be={point_be:.5f}, is_moving_tp={is_moving_tp}, trailing_type={trailing_type}")
                    if trailing_type == "Fixed":
                        tp_distance = abs(current_tp - entry_price)
                        self.debug_logger.debug_print(f"#{order.ticket}: Fixed trailing - TP distance: {tp_distance:.5f}, 2/3 trigger: {point_be:.5f}")

                    # Check if we should use ATR trailing stop
                    if trailing_type == "ATR" and is_moving_tp:
                        # Use ATR-based trailing stop with enhanced swing detection
                        atr_result = self.calculate_atr_trailing_stop(symbol, type_)

                        if atr_result is not None:
                            atr_stop = atr_result['stop_price']
                            swing_age_hours = atr_result['time_since_swing'] / 3600

                            # Enhanced trigger conditions for ATR trailing with TP movement
                            # For BUY: Only move SL up if ATR stop is higher than current SL
                            # For SELL: Only move SL down if ATR stop is lower than current SL
                            if type_ == mt5.ORDER_TYPE_BUY:
                                if (atr_stop > current_sl and
                                    atr_stop < current_price and
                                    swing_age_hours >= 4.0):  # Swing must be at least 4 hours old

                                    new_sl = atr_stop

                                    # Calculate new TP based on ATR trailing logic
                                    # Use the distance from entry to current price, then extend TP further
                                    current_profit_distance = current_price - entry_price
                                    atr_h4 = atr_result['h4_atr']

                                    # Extend TP by 2x ATR from current price for dynamic target
                                    new_tp = current_price + (2.0 * atr_h4)

                                    # Ensure new TP is higher than current TP (only move TP up)
                                    if new_tp > current_tp:
                                        self.debug_logger.debug_print(f"#{order.ticket}: BUY ATR Trailing - New SL: {new_sl:.5f}, New TP: {new_tp:.5f}")
                                        self.debug_logger.debug_print(f"#{order.ticket}: Swing: {atr_result['swing_price']:.5f} ({swing_age_hours:.1f}h old)")
                                        self.debug_logger.debug_print(f"#{order.ticket}: TP extended by 2x ATR: {2.0 * atr_h4:.5f}")
                                    else:
                                        # Keep current TP if calculated TP is not higher
                                        new_tp = current_tp
                                        self.debug_logger.debug_print(f"#{order.ticket}: BUY ATR Trailing - New SL: {new_sl:.5f}, TP unchanged: {new_tp:.5f}")

                                    self.debug_logger.debug_print(f"#{order.ticket}: Components: Stop1={atr_result['stop1']:.5f}, Stop2={atr_result['stop2']:.5f}")
                                else:
                                    self.debug_logger.debug_print(f"#{order.ticket}: BUY ATR conditions not met - Stop: {atr_stop:.5f}, Current SL: {current_sl:.5f}, Age: {swing_age_hours:.1f}h")

                            else:  # SELL
                                if (atr_stop < current_sl and
                                    atr_stop > current_price and
                                    swing_age_hours >= 4.0):  # Swing must be at least 4 hours old

                                    new_sl = atr_stop

                                    # Calculate new TP based on ATR trailing logic
                                    # Use the distance from entry to current price, then extend TP further
                                    current_profit_distance = entry_price - current_price  # For SELL, profit is when price goes down
                                    atr_h4 = atr_result['h4_atr']

                                    # Extend TP by 2x ATR from current price for dynamic target
                                    new_tp = current_price - (2.0 * atr_h4)

                                    # Ensure new TP is lower than current TP (only move TP down for SELL)
                                    if new_tp < current_tp:
                                        self.debug_logger.debug_print(f"#{order.ticket}: SELL ATR Trailing - New SL: {new_sl:.5f}, New TP: {new_tp:.5f}")
                                        self.debug_logger.debug_print(f"#{order.ticket}: Swing: {atr_result['swing_price']:.5f} ({swing_age_hours:.1f}h old)")
                                        self.debug_logger.debug_print(f"#{order.ticket}: TP extended by 2x ATR: {2.0 * atr_h4:.5f}")
                                    else:
                                        # Keep current TP if calculated TP is not lower
                                        new_tp = current_tp
                                        self.debug_logger.debug_print(f"#{order.ticket}: SELL ATR Trailing - New SL: {new_sl:.5f}, TP unchanged: {new_tp:.5f}")

                                    self.debug_logger.debug_print(f"#{order.ticket}: Components: Stop1={atr_result['stop1']:.5f}, Stop2={atr_result['stop2']:.5f}")
                                else:
                                    self.debug_logger.debug_print(f"#{order.ticket}: SELL ATR conditions not met - Stop: {atr_stop:.5f}, Current SL: {current_sl:.5f}, Age: {swing_age_hours:.1f}h")

                    # Use Fixed trailing stop (original logic)
                    elif type_ == mt5.ORDER_TYPE_BUY:  #and entry_price > current_sl:
                        if is_moving_tp:
                            # Infinite RR-based Moving TP logic for BUY orders
                            original_sl_distance = abs(entry_price - current_sl) if current_sl < entry_price else point_sl

                            # Calculate how far we've moved from entry
                            current_profit_distance = current_price - entry_price

                            # Define trail step (1 unit of original SL distance)
                            trail_step = original_sl_distance

                            # Calculate which stage we're in based on profit distance
                            if current_profit_distance >= (original_sl_distance * 2/3):  # At least 2/3 of first TP

                                # Calculate stage number (how many trail steps we've completed)
                                stage = int(current_profit_distance / trail_step)

                                # Calculate next trigger level
                                next_trigger = entry_price + ((stage + 1) * trail_step)

                                self.debug_logger.debug_print(f"#{order.ticket}: BUY Infinite Trail - Current: {current_price:.5f}, Stage: {stage}")
                                self.debug_logger.debug_print(f"#{order.ticket}: Profit Distance: {current_profit_distance:.5f}, Next Trigger: {next_trigger:.5f}")

                                if stage == 0:
                                    # First stage: Move SL to BE, extend TP
                                    new_sl = entry_price + point_50  # Breakeven + buffer
                                    new_tp = entry_price + (trail_step * 4)  # Extend to 4 units
                                    self.debug_logger.debug_print(f"#{order.ticket}: BUY Stage 0 (Initial Trail) - SL to BE: {new_sl:.5f}, TP: {new_tp:.5f}")

                                elif stage >= 1:
                                    # Subsequent stages: Trail SL and extend TP infinitely
                                    # SL trails at 50% of the current profit distance
                                    trail_sl_distance = current_profit_distance * 0.5
                                    new_sl = entry_price + trail_sl_distance

                                    # TP extends by 2 more trail steps ahead
                                    new_tp = entry_price + (current_profit_distance + (trail_step * 2))

                                    self.debug_logger.debug_print(f"#{order.ticket}: BUY Stage {stage} (Infinite Trail) - SL: {new_sl:.5f}, TP: {new_tp:.5f}")
                                    self.debug_logger.debug_print(f"#{order.ticket}: Trail SL Distance: {trail_sl_distance:.5f}, Extending TP by: {trail_step * 2:.5f}")

                        elif current_price >= entry_price + point_be:
                                # SL to BE logic for BUY orders
                                new_sl = entry_price + point_50
                                self.debug_logger.debug_print(f"#{order.ticket}: BUY SL to BE - Current: {current_price:.5f} >= Trigger: {entry_price + point_be:.5f}, New SL: {new_sl:.5f}")

                    elif type_ == mt5.ORDER_TYPE_SELL: # and entry_price < current_sl:
                        if is_moving_tp:
                            # Infinite RR-based Moving TP logic for SELL orders
                            original_sl_distance = abs(current_sl - entry_price) if current_sl > entry_price else point_sl

                            # Calculate how far we've moved from entry (profit distance)
                            current_profit_distance = entry_price - current_price

                            # Define trail step (1 unit of original SL distance)
                            trail_step = original_sl_distance

                            # Calculate which stage we're in based on profit distance
                            if current_profit_distance >= (original_sl_distance * 2/3):  # At least 2/3 of first TP

                                # Calculate stage number (how many trail steps we've completed)
                                stage = int(current_profit_distance / trail_step)

                                # Calculate next trigger level
                                next_trigger = entry_price - ((stage + 1) * trail_step)

                                self.debug_logger.debug_print(f"#{order.ticket}: SELL Infinite Trail - Current: {current_price:.5f}, Stage: {stage}")
                                self.debug_logger.debug_print(f"#{order.ticket}: Profit Distance: {current_profit_distance:.5f}, Next Trigger: {next_trigger:.5f}")

                                if stage == 0:
                                    # First stage: Move SL to BE, extend TP
                                    new_sl = entry_price - point_50  # Breakeven + buffer
                                    new_tp = entry_price - (trail_step * 4)  # Extend to 4 units
                                    self.debug_logger.debug_print(f"#{order.ticket}: SELL Stage 0 (Initial Trail) - SL to BE: {new_sl:.5f}, TP: {new_tp:.5f}")

                                elif stage >= 1:
                                    # Subsequent stages: Trail SL and extend TP infinitely
                                    # SL trails at 50% of the current profit distance
                                    trail_sl_distance = current_profit_distance * 0.5
                                    new_sl = entry_price - trail_sl_distance

                                    # TP extends by 2 more trail steps ahead
                                    new_tp = entry_price - (current_profit_distance + (trail_step * 2))

                                    self.debug_logger.debug_print(f"#{order.ticket}: SELL Stage {stage} (Infinite Trail) - SL: {new_sl:.5f}, TP: {new_tp:.5f}")
                                    self.debug_logger.debug_print(f"#{order.ticket}: Trail SL Distance: {trail_sl_distance:.5f}, Extending TP by: {trail_step * 2:.5f}")

                        elif current_price <= entry_price - point_be:
                                # SL to BE logic for SELL orders
                                new_sl = entry_price - point_50
                                self.debug_logger.debug_print(f"#{order.ticket}: SELL SL to BE - Current: {current_price:.5f} <= Trigger: {entry_price - point_be:.5f}, New SL: {new_sl:.5f}")

                    # Execute the order modification if needed
                    should_modify = False
                    modification_reason = []

                    # Check if SL should be modified
                    if new_sl and abs(new_sl - current_sl) > symbol_info.point:  # Only if significant change
                        should_modify = True
                        modification_reason.append(f"SL: {current_sl:.5f} -> {new_sl:.5f}")

                    # Check if TP should be modified
                    if new_tp != current_tp and abs(new_tp - current_tp) > symbol_info.point:
                        should_modify = True
                        modification_reason.append(f"TP: {current_tp:.5f} -> {new_tp:.5f}")

                    if should_modify:
                        self.debug_logger.debug_print(f"#{order.ticket}: Modifying order - {', '.join(modification_reason)}")

                        request = {"action": mt5.TRADE_ACTION_SLTP, "position": order.ticket, "sl": new_sl, "tp": new_tp}

                        # Send order modification using safe wrapper
                        result = self.safe_order_send(request)
                        if result and result.retcode == mt5.TRADE_RETCODE_DONE:
                            # Log successful SL change
                            if new_sl and abs(new_sl - current_sl) > symbol_info.point:
                                self.debug_logger.log_order_action(order.ticket, "SL to BE", current_sl, new_sl, "SL", True)

                            # Log TP change if it was also modified
                            if new_tp != current_tp and abs(new_tp - current_tp) > symbol_info.point:
                                self.debug_logger.log_order_action(order.ticket, "Moving TP", current_tp, new_tp, "TP", True)
                        else:
                            error_msg = result.comment if result else "Unknown error"
                            self.debug_logger.log_error(Exception(f"Order send failed: {error_msg}"), "order modification", order.ticket)
                    else:
                        if new_sl:
                            self.debug_logger.debug_print(f"#{order.ticket}: No modification needed - SL change too small: {abs(new_sl - current_sl):.5f} <= {symbol_info.point:.5f}")
                        else:
                            self.debug_logger.debug_print(f"#{order.ticket}: No modification needed - conditions not met")

            # Log processing summary
            group_name = filter_comment if filter_comment else "All"
            action_type = "SL+TP" if is_moving_tp else "SL to BE"
            processed_count = sum(1 for order in orders if (order.comment.startswith(filter_comment) or filter_comment == ""))
            self.debug_logger.log_order_processing(symbol, filter_comment, count_orders["All_FT"], processed_count, action_type)

            return count_orders
        except Exception as e:
            self.debug_logger.log_error(e, "update_SL_to_BE_by_point")
            return {"All":0, "All_FT":0, "All_TF":0}

    def update_TP_to_BE_by_condition(self, symbol, condition):
        """ Auto update orders STOP LOSS to BREAKEVEN """
        orders = mt5.positions_get() if self.initialize_mt5() else []
        if orders is None or len(orders) == 0:
            self.add_status_frame(f"❌ Try to update TP to BE {condition}: No open positions.", "yellow")
            return 0
        count = 0
        for order in orders:
            if order.symbol != symbol:
                continue
            count+=1
            entry_price = order.price_open
            current_price = order.price_current
            current_tp = order.tp
            current_sl = order.sl
            type_ = order.type  # 0 = BUY, 1 = SELL
            profit = order.profit
            symbol_info = mt5.symbol_info(order.symbol)
            if not symbol_info:
                continue

            should_update = False
            # เงื่อนไขปิด order ตาม condition ที่รับมา
            if condition == 'all-buy' and type_ == 0 and profit < 0:
                should_update = True
            elif condition == 'all-sell' and type_ == 1 and profit < 0:
                should_update = True
            elif condition == 'all' and profit < 0:
                should_update = True

            if should_update:
                point = symbol_info.point
                point_10 = point * 10
                # point_sl = point * self.config.SL_POINTS 
                new_tp = None

                if type_ == mt5.ORDER_TYPE_BUY: 
                    new_tp = entry_price + point_10
    
                elif type_ == mt5.ORDER_TYPE_SELL:
                    new_tp = entry_price - point_10 

                if new_tp:
                    request = {"action": mt5.TRADE_ACTION_SLTP, "position": order.ticket, "sl": current_sl, "tp": new_tp} 
                    result = mt5.order_send(request)
                    if result.retcode != mt5.TRADE_RETCODE_DONE:
                        self.add_status_frame(f"❌ Execute failed: {result.comment}", "yellow") 
                    else:
                        self.add_status_frame(f"📦 Set TP to BE: position {order.ticket} TP {current_tp} >> {new_tp}")

        return count
    
    def update_SL_to_BE_by_condition(self, symbol, condition):
        """ Auto update orders STOP LOSS to BREAKEVEN """
        orders = mt5.positions_get() if self.initialize_mt5() else []
        if orders is None or len(orders) == 0:
            self.add_status_frame(f"❌ Try to update SL to BE {condition}: No open positions.", "yellow")
            return 0
        count = 0
        for order in orders:
            if order.symbol != symbol:
                continue
            count+=1
            entry_price = order.price_open
            current_price = order.price_current
            current_tp = order.tp
            current_sl = order.sl
            type_ = order.type  # 0 = BUY, 1 = SELL
            profit = order.profit
            symbol_info = mt5.symbol_info(order.symbol)
            if not symbol_info:
                continue

            should_update = False
            # เงื่อนไขปิด order ตาม condition ที่รับมา
            if condition == 'all-buy' and type_ == 0 and profit > 0:
                should_update = True
            elif condition == 'all-sell' and type_ == 1 and profit > 0:
                should_update = True
            elif condition == 'all' and profit > 0:
                should_update = True

            if should_update:
                point = symbol_info.point
                point_10 = point * 10
                point_50 = point * 50
                # point_sl = point * self.config.SL_POINTS 
                new_sl = None

                if type_ == mt5.ORDER_TYPE_BUY: 
                    new_sl = entry_price + point_50
    
                elif type_ == mt5.ORDER_TYPE_SELL:
                    new_sl = entry_price - point_50 

                if new_sl:
                    request = {"action": mt5.TRADE_ACTION_SLTP, "position": order.ticket, "sl": new_sl, "tp": current_tp}
                    result = mt5.order_send(request)
                    if result.retcode != mt5.TRADE_RETCODE_DONE:
                        self.add_status_frame(f"❌ Execute failed: {result.comment}", "yellow") 
                    else:
                        self.add_status_frame(f"📦 Set SL to BE: position {order.ticket} SL {current_sl} >> {new_sl}")

        return count

    def close_orders_by_condition(self, symbol, condition, filter_comment = ""):
        orders = mt5.positions_get() if self.initialize_mt5() else []
        closed = 0
        if orders is None or len(orders) == 0:
            self.add_status_frame(f"❌ Try to close {condition}: No open positions.", "yellow")
            return closed
        for order in orders:
            if order.symbol != symbol:
                continue
            ticket = order.ticket
            symbol_ = order.symbol
            type_ = order.type  # 0 = BUY, 1 = SELL
            profit = order.profit
            volume = order.volume

            should_close = False

            # เงื่อนไขปิด order ตาม condition ที่รับมา
            if condition == 'buy-profit' and type_ == 0 and profit > 0:
                should_close = True
            elif condition == 'sell-profit' and type_ == 1 and profit > 0:
                should_close = True
            elif condition == 'all-profit' and profit > 0:
                should_close = True
            elif condition == 'buy-loss' and type_ == 0 and profit < 0:
                should_close = True
            elif condition == 'sell-loss' and type_ == 1 and profit < 0:
                should_close = True
            elif condition == 'all-loss' and profit < 0:
                should_close = True
            elif condition == 'all-buy' and type_ == 0:
                should_close = True
            elif condition == 'all-sell' and type_ == 1:
                should_close = True
            elif condition == 'all':
                should_close = True
            elif condition == 'filter' and order.comment.startswith(filter_comment) or filter_comment == "":
                should_close = True

            if should_close:
                closed +=1
                price = mt5.symbol_info_tick(symbol_).bid if type_ == 0 else mt5.symbol_info_tick(symbol_).ask
                order_type = mt5.ORDER_TYPE_SELL if type_ == 0 else mt5.ORDER_TYPE_BUY
                close_request = {
                    "action": mt5.TRADE_ACTION_DEAL,
                    "position": ticket,
                    "symbol": symbol_,
                    "volume": volume,
                    "type": order_type,
                    "price": price,
                    "deviation": 10,
                    "magic": 155214,
                    "comment": "Auto close by condition",
                    "type_filling": mt5.ORDER_FILLING_FOK,
                }

                result = mt5.order_send(close_request)
                if result.retcode == mt5.TRADE_RETCODE_DONE:
                    self.add_status_frame(f"✅ Closed {symbol_} ({'BUY' if type_ == 0 else 'SELL'}) | profit: {profit:.2f}", "yellow")
                    # print(f"✅ Closed {symbol} ({'BUY' if type_ == 0 else 'SELL'}) | profit: {profit:.2f}")
                else:
                    self.add_status_frame(f"❌ Failed to close {symbol_}: {result.retcode}", "yellow")
                    # print(f"❌ Failed to close {symbol}: {result.retcode}")
        return closed

    def close_pending_orders_by_filter(self, symbol, filter_comment=""):
        """
        Close pending orders (Limit and Stop orders) that are not yet active
        If filter_comment is empty, closes all pending orders for the symbol
        If filter_comment is provided, only closes orders where comment ends with filter_comment
        """
        pending_orders = mt5.orders_get(symbol=symbol) if self.initialize_mt5() else []
        closed = 0

        if pending_orders is None or len(pending_orders) == 0:
            self.add_status_frame(f"❌ Try to close pending orders: No pending orders found for {symbol}.", "yellow")
            return closed

        for order in pending_orders:
            if order.symbol != symbol:
                continue

            # Check if we should close this order based on filter
            should_close = False
            if filter_comment == "":
                # Close all pending orders if no filter
                should_close = True
            else:
                # Close only if comment ends with filter_comment
                if order.comment.endswith(filter_comment):
                    should_close = True

            if should_close:
                # Cancel the pending order
                cancel_request = {
                    "action": mt5.TRADE_ACTION_REMOVE,
                    "order": order.ticket,
                }

                result = mt5.order_send(cancel_request)
                if result.retcode == mt5.TRADE_RETCODE_DONE:
                    closed += 1
                    order_type_name = "Buy Limit" if order.type == mt5.ORDER_TYPE_BUY_LIMIT else \
                                     "Sell Limit" if order.type == mt5.ORDER_TYPE_SELL_LIMIT else \
                                     "Buy Stop" if order.type == mt5.ORDER_TYPE_BUY_STOP else \
                                     "Sell Stop" if order.type == mt5.ORDER_TYPE_SELL_STOP else \
                                     f"Type {order.type}"
                    self.add_status_frame(f"✅ Cancelled pending order: {order.symbol} {order_type_name} | Comment: {order.comment}", "yellow")
                else:
                    self.add_status_frame(f"❌ Failed to cancel pending order {order.ticket}: {result.retcode}", "yellow")

        if closed > 0:
            filter_msg = f" with filter '{filter_comment}'" if filter_comment else ""
            self.add_status_frame(f"✅ Cancelled {closed} pending orders for {symbol}{filter_msg}", "green")

        return closed

    # ===========================
    # Chart Data and AI Analysis Functions
    # ===========================

    def get_chart_data(self, symbol, timeframe, barback):
        """Get chart data with technical indicators for AI analysis"""
        try:
            # Map timeframe string to MT5 constant
            timeframe_map = {
                "M1": mt5.TIMEFRAME_M1,
                "M5": mt5.TIMEFRAME_M5,
                "M15": mt5.TIMEFRAME_M15,
                "M30": mt5.TIMEFRAME_M30,
                "H1": mt5.TIMEFRAME_H1,
                "H4": mt5.TIMEFRAME_H4,
                "D1": mt5.TIMEFRAME_D1,
                "W1": mt5.TIMEFRAME_W1,
                "MN1": mt5.TIMEFRAME_MN1
            }

            mt5_timeframe = timeframe_map.get(timeframe.upper())
            if not mt5_timeframe:
                raise ValueError(f"Invalid timeframe: {timeframe}")

            # Get rates from MT5
            rates = mt5.copy_rates_from_pos(symbol, mt5_timeframe, 0, barback + 100)  # Get extra bars for indicators
            if rates is None or len(rates) == 0:
                raise ValueError(f"No data available for {symbol} {timeframe}")

            # Convert to numpy arrays for TA-Lib
            close = np.array([rate[4] for rate in rates], dtype=float)  # Close prices
            high = np.array([rate[2] for rate in rates], dtype=float)   # High prices
            low = np.array([rate[3] for rate in rates], dtype=float)    # Low prices
            volume = np.array([rate[5] for rate in rates], dtype=float) # Volume

            # Calculate technical indicators
            ema_20 = ta.EMA(close, timeperiod=20)
            rsi_14 = ta.RSI(close, timeperiod=14)

            # MACD calculation
            macd, macd_signal, macd_hist = ta.MACD(close, fastperiod=12, slowperiod=26, signalperiod=9)

            # Additional RSI and SMA indicators as requested
            rsi_25 = ta.RSI(close, timeperiod=25)
            sma_50_of_rsi25 = ta.SMA(rsi_25, timeperiod=50)
            rsi_50 = ta.RSI(close, timeperiod=50)
            sma_25_of_rsi50 = ta.SMA(rsi_50, timeperiod=25)

            # Format the result - take only the last 'barback' bars
            result = []
            start_idx = len(rates) - barback

            for i in range(start_idx, len(rates)):
                bar_time = datetime.fromtimestamp(rates[i][0]).strftime("%Y-%m-%d %H:%M:%S")

                result.append({
                    "time": bar_time,
                    "open": round(rates[i][1], 5),
                    "high": round(rates[i][2], 5),
                    "low": round(rates[i][3], 5),
                    "close": round(rates[i][4], 5),
                    "volume": int(rates[i][5]),
                    "ema_20": round(ema_20[i], 5) if not np.isnan(ema_20[i]) else None,
                    "rsi_14": round(rsi_14[i], 1) if not np.isnan(rsi_14[i]) else None,
                    "macd": round(macd[i], 6) if not np.isnan(macd[i]) else None,
                    "macd_signal": round(macd_signal[i], 6) if not np.isnan(macd_signal[i]) else None,
                    "rsi_25": round(rsi_25[i], 1) if not np.isnan(rsi_25[i]) else None,
                    "sma50_rsi25": round(sma_50_of_rsi25[i], 1) if not np.isnan(sma_50_of_rsi25[i]) else None,
                    "rsi_50": round(rsi_50[i], 1) if not np.isnan(rsi_50[i]) else None,
                    "sma25_rsi50": round(sma_25_of_rsi50[i], 1) if not np.isnan(sma_25_of_rsi50[i]) else None
                })

            return result

        except Exception as e:
            raise Exception(f"Chart data error: {str(e)}")

    def get_multi_timeframe_data(self, symbol, timeframes, barback):
        """Get chart data for multiple timeframes"""
        try: 
            # Remove duplicates while preserving order 

            successful_symbol = None
            multi_timeframe_data = {}
            total_bars = 0

            for tf in timeframes:
                chart_data = None

                # Try each symbol variation until one works
                chart_data = self.get_chart_data(symbol, tf, barback)

                if not chart_data:
                    self.add_status_frame(f"❌ Failed to get chart data for {symbol} {tf}", "red", level=2)
                    return None

                multi_timeframe_data[tf] = chart_data
                total_bars += len(chart_data)
                self.add_status_frame(f"✅ Chart data: {successful_symbol} {tf} ({len(chart_data)} bars)", "green", level=4)

            return {
                "symbol": symbol,
                "actual_symbol": successful_symbol,
                "timeframes": timeframes,
                "total_bars": total_bars,
                "data": multi_timeframe_data
            }

        except Exception as e:
            self.add_status_frame(f"❌ Multi-timeframe data error: {str(e)}", "red", level=1)
            return None

    def download_image(self, image_url):
        """Download image from URL for AI analysis"""
        try:
            response = requests.get(image_url, timeout=30)
            response.raise_for_status()

            # Convert to base64 for AI API
            image_base64 = base64.b64encode(response.content).decode('utf-8')
            return image_base64

        except Exception as e:
            self.add_status_frame(f"❌ Image download failed: {str(e)}", "red", level=2)
            return None

    def generate_signal_id(self):
        """Generate 8-character random signal ID using a-z0-9"""
        chars = string.ascii_lowercase + string.digits
        return ''.join(random.choice(chars) for _ in range(8))

    def parse_signal_format(self, analysis_text):
        """Parse structured signal format from AI response"""
        try:
            lines = analysis_text.strip().split('\n')
            signal_data = {}

            for line in lines:
                line = line.strip()
                if ':' in line:
                    key, value = line.split(':', 1)
                    key = key.strip().lower()
                    value = value.strip()

                    if 'signal id' in key:
                        signal_data['signal_id'] = value
                    elif 'symbol' in key:
                        signal_data['symbol'] = value
                    elif 'status' in key:
                        signal_data['status'] = value
                    elif 'signal' in key:
                        signal_data['signal_type'] = value
                    elif 'price' in key:
                        signal_data['entry_price'] = value
                    elif key == 'sl':
                        signal_data['sl_price'] = value
                    elif key == 'tp1':
                        signal_data['tp1_price'] = value
                    elif key == 'tp2':
                        signal_data['tp2_price'] = value
                    elif key == 'tp3':
                        signal_data['tp3_price'] = value
                    elif key == 'tp4':
                        signal_data['tp4_price'] = value
                    elif key == 'tp5':
                        signal_data['tp5_price'] = value
                    elif 'reason' in key:
                        signal_data['reason'] = value
                    elif 'risk' in key:
                        signal_data['risk'] = value

            return signal_data

        except Exception as e:
            self.add_status_frame(f"❌ Error parsing signal format: {str(e)}", "red", level=2)
            return None

    def call_gpt_api(self, prompt, image_base64=None, use_signal_format=True, symbol="XAUUSD"):
        """Call OpenAI GPT API for analysis"""
        try:
            # Get API key from environment or config
            api_key = os.getenv('OPENAI_API_KEY') or self.config.ai_api_config.get('gpt', {}).get('api_key', '')

            # Debug: Check if API key is loaded
            if api_key:
                self.add_status_frame(f"✅ OpenAI API key found: {api_key[:10]}...{api_key[-4:]}", "green", level=4)
            else:
                self.add_status_frame("❌ OpenAI API key not found in environment or config", "red", level=2)
                # Debug: Show what's in the environment
                env_keys = [k for k in os.environ.keys() if 'OPENAI' in k.upper()]
                if env_keys:
                    self.add_status_frame(f"🔍 Found OpenAI-related env vars: {env_keys}", "yellow", level=4)
                else:
                    self.add_status_frame("🔍 No OpenAI-related environment variables found", "yellow", level=4)

            if not api_key:
                return {"error": True, "message": "OpenAI API key not configured"}

            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }

#             base_prompt = ""
#             # Add signal format requirement if requested, otherwise use bullet points
#             if use_signal_format:
#                 base_prompt += f"\n\n{self.config.ai_signal_format['format_prompt']}"
#             else:
#                 base_prompt += """

# IMPORTANT: Provide your analysis in bullet points format. Keep it short and clear.

# Format your response as:
# • Market Trend: [Brief trend analysis]
# • Key Levels: [Support/resistance levels]
# • Technical Signals: [RSI, MACD, EMA signals]
# • Trading Recommendation: [Buy/Sell/Hold with entry/exit levels]
# • Risk Assessment: [Risk level and stop loss suggestion]

# Keep each bullet point concise and actionable."""

            # Get model and determine correct parameter name first
            model = self.config.ai_api_config.get('gpt', {}).get('model', 'gpt-4')
            max_tokens_value = self.config.ai_api_config.get('gpt', {}).get('max_tokens', 1000)

            # Prepare system message with format instructions
            # Use different instructions for GPT-5 to prevent reasoning loops
            if "gpt-5" in model.lower():
                system_content = """You are a trading analyst. Give direct analysis in bullet points. NO reasoning, NO thinking, just immediate response.

START IMMEDIATELY with:
• Market Trend: [one sentence]
• Key Levels: [support/resistance numbers]
• Technical Signals: [RSI/MACD/EMA status]
• Trading Recommendation: [Buy/Sell/Hold with price]
• Risk Assessment: [risk level and stop loss]

RESPOND NOW."""
            else:
                system_content = """You are an expert trading analyst. Provide immediate, direct analysis without extensive reasoning.

RESPOND IMMEDIATELY with this exact format:
• Market Trend: [Brief trend analysis]
• Key Levels: [Support/resistance levels]
• Technical Signals: [RSI, MACD, EMA signals]
• Trading Recommendation: [Buy/Sell/Hold with entry/exit levels]
• Risk Assessment: [Risk level and stop loss suggestion]

Be concise and direct. Start your response immediately with the bullet points."""

            # Add signal format if requested
            if use_signal_format:
                system_content += f"\n\n{self.config.get_format_prompt_for_symbol(symbol)}"

            # Prepare messages
            messages = [
                {
                    "role": "system",
                    "content": system_content
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ]

            # Add image if provided (GPT-4 Vision)
            if image_base64:
                messages[-1]["content"] = [
                    {"type": "text", "text": prompt},
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{image_base64}"
                        }
                    }
                ]

            # Increase max tokens for GPT-5 due to reasoning tokens
            if "gpt-5" in model.lower():
                # GPT-5 needs more tokens due to reasoning process
                # For large prompts, increase even more
                prompt_length = len(prompt)
                if prompt_length > 2000:
                    max_tokens_value = max(max_tokens_value, 20000)  # Large prompts need more tokens
                    self.add_status_frame("📊 Large prompt detected, increasing GPT-5 token limit to 6000", "cyan", level=4)
                else:
                    max_tokens_value = max(max_tokens_value, 4000)  # Minimum 4000 for GPT-5

            payload = {
                "model": model,
                "messages": messages
            }

            # Use correct parameters based on model
            if "gpt-5" in model.lower():
                payload["max_completion_tokens"] = max_tokens_value
                # GPT-5 only supports temperature = 1 (default), so we don't set it
            else:
                payload["max_tokens"] = max_tokens_value
                payload["temperature"] = 0.7  # Only for older models

            response = requests.post(
                "https://api.openai.com/v1/chat/completions",
                headers=headers,
                json=payload,
                timeout=60
            )
            print(response.json())

            if response.status_code == 200:
                result = response.json()
                analysis = result['choices'][0]['message']['content']
                finish_reason = result['choices'][0].get('finish_reason', 'unknown')

                # Check for empty content
                if not analysis or analysis.strip() == '':
                    if finish_reason == 'length':
                        # If GPT-5 failed due to reasoning tokens, try GPT-4 as fallback
                        if "gpt-5" in model.lower():
                            self.add_status_frame("⚠️ GPT-5 failed due to reasoning tokens, trying GPT-4 fallback...", "yellow", level=3)

                            # Retry with GPT-4
                            fallback_payload = payload.copy()
                            fallback_payload["model"] = "gpt-4"
                            fallback_payload["max_tokens"] = 1500  # Use max_tokens for GPT-4
                            if "max_completion_tokens" in fallback_payload:
                                del fallback_payload["max_completion_tokens"]
                            fallback_payload["temperature"] = 0.7

                            fallback_response = requests.post(
                                "https://api.openai.com/v1/chat/completions",
                                headers=headers,
                                json=fallback_payload,
                                timeout=60
                            )

                            if fallback_response.status_code == 200:
                                fallback_result = fallback_response.json()
                                fallback_analysis = fallback_result['choices'][0]['message']['content']
                                if fallback_analysis and fallback_analysis.strip():
                                    self.add_status_frame("✅ GPT-4 fallback successful", "green", level=4)
                                    return {"error": False, "analysis": fallback_analysis}

                        error_msg = f"GPT response was truncated due to token limit. Used {result.get('usage', {}).get('total_tokens', 'unknown')} tokens. Try increasing max_tokens or shortening the prompt."
                        self.add_status_frame(f"❌ {error_msg}", "red", level=2)
                        return {"error": True, "message": error_msg}
                    else:
                        error_msg = f"GPT returned empty content (finish_reason: {finish_reason})"
                        self.add_status_frame(f"❌ {error_msg}", "red", level=2)
                        return {"error": True, "message": error_msg}

                self.add_status_frame("✅ GPT analysis completed", "green", level=4)
                return {"error": False, "analysis": analysis}
            elif response.status_code == 401:
                error_msg = "OpenAI API authentication failed - invalid API key"
                self.add_status_frame(f"❌ {error_msg}", "red", level=2)
                return {"error": True, "message": error_msg}
            elif response.status_code == 429:
                try:
                    error_data = response.json()
                    if error_data.get("error", {}).get("code") == "insufficient_quota":
                        error_msg = "OpenAI API quota exceeded - please check your billing and usage limits at https://platform.openai.com/usage"
                    else:
                        error_msg = "OpenAI API rate limit exceeded - please wait and try again"
                except:
                    error_msg = "OpenAI API rate limit or quota exceeded"
                self.add_status_frame(f"❌ {error_msg}", "red", level=2)
                return {"error": True, "message": error_msg}
            elif response.status_code == 404:
                try:
                    error_data = response.json()
                    if "model" in error_data.get("error", {}).get("message", "").lower():
                        error_msg = f"OpenAI model '{payload.get('model', 'unknown')}' not available - try gpt-3.5-turbo instead"
                    else:
                        error_msg = "OpenAI API endpoint not found"
                except:
                    error_msg = "OpenAI API endpoint not found"
                self.add_status_frame(f"❌ {error_msg}", "red", level=2)
                return {"error": True, "message": error_msg}
            else:
                error_msg = f"GPT API error: {response.status_code} - {response.text}"
                self.add_status_frame(f"❌ {error_msg}", "red", level=2)
                return {"error": True, "message": error_msg}

        except Exception as e:
            error_msg = f"GPT API call failed: {str(e)}"
            self.add_status_frame(f"❌ {error_msg}", "red", level=2)
            return {"error": True, "message": error_msg}

    def call_gemini_api(self, prompt, image_base64=None, use_signal_format=True, symbol="XAUUSD"):
        """Call Google Gemini API for analysis"""
        try:
            # Get API key from environment or config
            api_key = os.getenv('GEMINI_API_KEY') or self.config.ai_api_config.get('gemini', {}).get('api_key', '')

            # Debug: Check if API key is loaded
            if api_key:
                self.add_status_frame(f"✅ Gemini API key found: {api_key[:10]}...{api_key[-4:]}", "green", level=4)
            else:
                self.add_status_frame("❌ Gemini API key not found in environment or config", "red", level=2)
                return {"error": True, "message": "Gemini API key not configured"}

            # Prepare the request URL - use latest Gemini model
            model_name = "gemini-1.5-flash" if not image_base64 else "gemini-1.5-pro"
            url = f"https://generativelanguage.googleapis.com/v1beta/models/{model_name}:generateContent?key={api_key}"

            # Prepare system instruction
            system_instruction = """You are an expert trading analyst. Analyze the provided market data and give actionable trading insights.

IMPORTANT: Always provide your analysis in bullet points format. Keep it short and clear.

Format your response as:
• Market Trend: [Brief trend analysis]
• Key Levels: [Support/resistance levels]
• Technical Signals: [RSI, MACD, EMA signals]
• Trading Recommendation: [Buy/Sell/Hold with entry/exit levels]
• Risk Assessment: [Risk level and stop loss suggestion]

Keep each bullet point concise and actionable. Always provide a complete response."""

            # Add signal format if requested
            if use_signal_format:
                system_instruction += f"\n\n{self.config.get_format_prompt_for_symbol(symbol)}"

            # Prepare content parts
            parts = [{"text": prompt}]

            # Add image if provided
            if image_base64:
                parts.append({
                    "inline_data": {
                        "mime_type": "image/jpeg",
                        "data": image_base64
                    }
                })

            payload = {
                "system_instruction": {
                    "parts": [{"text": system_instruction}]
                },
                "contents": [{
                    "parts": parts
                }],
                "generationConfig": {
                    "maxOutputTokens": self.config.ai_api_config.get('gemini', {}).get('max_tokens', 1000),
                    "temperature": 0.7,
                    "topP": 0.8,
                    "topK": 10
                },
                "safetySettings": [
                    {
                        "category": "HARM_CATEGORY_HARASSMENT",
                        "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                    },
                    {
                        "category": "HARM_CATEGORY_HATE_SPEECH",
                        "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                    },
                    {
                        "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                        "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                    },
                    {
                        "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
                        "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                    }
                ]
            }

            headers = {
                "Content-Type": "application/json"
            }

            response = requests.post(url, headers=headers, json=payload, timeout=60)

            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and len(result['candidates']) > 0:
                    candidate = result['candidates'][0]
                    if 'content' in candidate and 'parts' in candidate['content']:
                        analysis = candidate['content']['parts'][0]['text']
                        self.add_status_frame("✅ Gemini analysis completed", "green", level=4)
                        return {"error": False, "analysis": analysis}
                    else:
                        # Handle blocked content or other issues
                        finish_reason = candidate.get('finishReason', 'UNKNOWN')
                        if finish_reason == 'SAFETY':
                            return {"error": True, "message": "Gemini blocked content due to safety filters"}
                        else:
                            return {"error": True, "message": f"Gemini response incomplete: {finish_reason}"}
                else:
                    return {"error": True, "message": "No analysis generated by Gemini"}
            elif response.status_code == 400:
                try:
                    error_data = response.json()
                    error_msg = error_data.get("error", {}).get("message", "Bad request")
                    self.add_status_frame(f"❌ Gemini API error: {error_msg}", "red", level=2)
                    return {"error": True, "message": f"Gemini API error: {error_msg}"}
                except:
                    error_msg = f"Gemini API bad request: {response.text}"
                    self.add_status_frame(f"❌ {error_msg}", "red", level=2)
                    return {"error": True, "message": error_msg}
            elif response.status_code == 403:
                error_msg = "Gemini API authentication failed - invalid API key or quota exceeded"
                self.add_status_frame(f"❌ {error_msg}", "red", level=2)
                return {"error": True, "message": error_msg}
            else:
                error_msg = f"Gemini API error: {response.status_code} - {response.text}"
                self.add_status_frame(f"❌ {error_msg}", "red", level=2)
                return {"error": True, "message": error_msg}

        except Exception as e:
            error_msg = f"Gemini API call failed: {str(e)}"
            self.add_status_frame(f"❌ {error_msg}", "red", level=2)
            return {"error": True, "message": error_msg}

    def generate_prompt(self, chart_data_result, custom_prompt, symbol, timeframes, use_signal_format=True):
        """Generate prompt for AI analysis"""
        try:
            # Prepare the analysis prompt
            base_prompt = f"""
Analyze the {symbol} trading data for timeframes {timeframes}.

Technical Analysis Data:
- Total bars analyzed: {chart_data_result['total_bars']}
- Timeframes: {', '.join(timeframes)}
- Use Trend Line, Fibonacci Retracement, Support/resistance, Supply/demand and Pivot Points in your analysis.

Chart Data Summary:
{chart_data_result['data']}

"""

            # Add chart data summary for each timeframe
#             for tf, data in chart_data_result['data'].items():
#                 if data:
#                     latest_bar = data[-1]
#                     base_prompt += f"""
# {tf} Timeframe (Latest Bar):
# - Price: O:{latest_bar['open']} H:{latest_bar['high']} L:{latest_bar['low']} C:{latest_bar['close']}
# - EMA20: {latest_bar['ema_20']}
# - RSI14: {latest_bar['rsi_14']}
# - MACD: {latest_bar['macd']} Signal: {latest_bar['macd_signal']}
# - RSI25: {latest_bar['rsi_25']} SMA50: {latest_bar['sma50_rsi25']}
# - RSI50: {latest_bar['rsi_50']} SMA25: {latest_bar['sma25_rsi50']}
# """

            # Add custom prompt if provided
            if custom_prompt:
                base_prompt += f"\n\nAdditional Instructions: {custom_prompt}"

            # Format instructions are now in system message, so we don't add them here
            # This keeps the user prompt focused on the data and custom instructions

            return base_prompt

        except Exception as e:
            error_msg = f"AI analysis error: {str(e)}"
            self.add_status_frame(f"❌ {error_msg}", "red", level=1)
            return None

    def perform_ai_analysis(self, chart_data_result, custom_prompt, ai_provider, image_url, symbol, timeframes, use_signal_format=True):
        """Perform AI analysis using specified provider"""
        base_prompt = self.generate_prompt(chart_data_result, custom_prompt, symbol, timeframes, use_signal_format)
        try: 
            # Download image if provided
            image_base64 = None
            if image_url:
                image_base64 = self.download_image(image_url)
                if image_base64:
                    base_prompt += "\n\nA chart image has been provided for visual analysis."

            # Call appropriate AI API
            if ai_provider == "gpt":
                analysis_result = self.call_gpt_api(base_prompt, image_base64, use_signal_format, symbol)
            elif ai_provider == "gemini":
                analysis_result = self.call_gemini_api(base_prompt, image_base64, use_signal_format, symbol)
            else:
                return {"error": True, "message": f"Unsupported AI provider: {ai_provider}"}

            if analysis_result.get("error"):
                return analysis_result

            # Parse signal format if requested
            signal_data = None
            if use_signal_format:
                signal_data = self.parse_signal_format(analysis_result["analysis"])

            # Prepare final response
            response = {
                "error": False,
                "message": "AI analysis completed successfully",
                "symbol": symbol,
                "timeframes": timeframes,
                "ai_provider": ai_provider.upper(),
                "analysis": analysis_result["analysis"],
                # "chart_data": chart_data_result,
                # "custom_prompt": custom_prompt,
                "prompt": base_prompt,
                "image_analyzed": bool(image_base64),
                "use_signal_format": use_signal_format,
                "timestamp": datetime.now().isoformat()
            }

            # Add parsed signal data if available
            if signal_data:
                response["signal_data"] = signal_data
                response["structured_signal"] = True

            return response

        except Exception as e:
            error_msg = f"AI analysis error: {str(e)}"
            self.add_status_frame(f"❌ {error_msg}", "red", level=1)
            return {"error": True, "message": error_msg}

    def save_signal_log(self, analysis_result, bot_name="Unknown"):
        """Save AI analysis result to Logs/Signals folder"""
        try:
            # Create Logs/Signals directory if it doesn't exist
            logs_dir = os.path.join(os.getcwd(), "Logs", "Signals")
            os.makedirs(logs_dir, exist_ok=True)

            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{bot_name}_{timestamp}.json"
            filepath = os.path.join(logs_dir, filename)

            # Save analysis result as JSON
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(analysis_result, f, indent=2, ensure_ascii=False)

            self.add_status_frame(f"📁 Signal saved: {filename}", "cyan", level=4)
            return filepath

        except Exception as e:
            self.add_status_frame(f"❌ Failed to save signal log: {str(e)}", "red", level=2)
            return None