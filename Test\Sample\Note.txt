
//==========================================

python -m pip install --upgrade pip setuptools wheel
pip install MetaTrader5 
pip install --upgrade MetaTrader5

## ดาวน์โหลดจาก https://github.com/cgohlke/talib-build/releases  (Tested) 
python -m pip install ta_lib-0.5.2-cp313-cp313-win_amd64.whl
pip install matplotlib
pip install pandas 
pip install python-dotenv
pip install tk
pip install virtualenv
virtualenv venv
pip install flask
pip install --upgrade google-api-python-client google-auth-httplib2 google-auth-oauthlib

//==========================================

Client ID
720520122257-8dqq2gq7p262te4iu5fuh9j8ji9vh2ms.apps.googleusercontent.com
Client secret
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

IUX
Demo Standard
2100283585	P161032#aDM	IUXMarkets-Demo
Demo Pro
2100360531	P161032#aDM	IUXMarkets-Demo	



https://algoprime.co/sign-up
2e9551f3-3b97-48f6-9680-8836217dbdf9
zd
Best2Bene#

[/] HC    - 
[/] TL    - make pararel
[/] DZV
    Zone 1 [] - []
    Zone 2 [] - []
    Zone 3 [] - []
    Zone 4 [] - []

Modified
[/] OB    - 
[/] EMA   -
[/] RSId  -
[/] SR    -

Non-Modified
[/] QM    -
[O] SMC   -

Seperate Overlay
[ ] Fibo  - make dynamic for each TF (or draw manually)

Seperate pane finish
[/] Sto   -


=====================

To start python with .bat
    # @echo off
    start /b "C:\Program Files\Python313\pythonw.exe" "c:/Projects/python/mt59-gui.py"
    exit

To create shortcut for .bat
Target:
    cmd /c "C:\Projects\python\_start1.bat"


=====================
