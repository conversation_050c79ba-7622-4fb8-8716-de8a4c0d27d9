# Quiet Startup & Auto SL to BE Guide

## Quiet Startup Feature

I've created a quiet startup mode that starts your app minimized to the system tray with no visible windows.

### Files Created:

1. **`start_app_quiet.bat`** - Double-click to start in quiet mode
2. **Updated `main.py`** - Added quiet mode support
3. **Updated `App/tab_account.py`** - Added MT5 quiet startup

### How to Use Quiet Startup:

#### Option 1: Use the Batch File
```bash
# Double-click this file:
start_app_quiet.bat
```

#### Option 2: Command Line
```bash
# Activate venv first, then:
python main.py --quiet --minimize
```

### What Happens in Quiet Mode:

1. **No Command Window** - Batch file runs silently
2. **No MT5 GUI** - MetaTrader 5 starts hidden in background
3. **App Minimizes to Tray** - Main window goes directly to system tray
4. **All Services Running** - Webhook server, refresh loops, etc. all work normally
5. **System Tray Access** - Right-click tray icon to restore or check status

### Command Line Arguments:

- `--quiet` - Start with window hidden
- `--minimize` - Auto-minimize to tray after startup
- Both can be used together for full quiet mode

## Auto SL to BE (Stop Loss to Break Even)

The Auto SL to BE feature automatically moves stop losses to break even when positions become profitable.

### Location in App:

Go to **Orders Tab** → Look at the **control panel** (top area)

### Controls Available:

#### **Top Row - Auto Refresh:**
- Enable/Disable auto refresh
- Set refresh interval (seconds)
- Manual refresh button
- Status display

#### **Bottom Row - Auto SL to BE:**
- **All Orders** - Auto BE for all positions (enabled by default)
- **SIG Orders** - Auto BE only for SIG-prefixed orders
- **INPUT Orders** - Auto BE only for INPUT-prefixed orders
- **Status Indicator** - Shows which auto BE modes are active

### How Auto SL to BE Works:

1. **Monitors Positions** - Checks all open positions during refresh cycles
2. **Profit Detection** - Identifies profitable positions
3. **Break Even Calculation** - Calculates break even price + small buffer
4. **Automatic Update** - Moves stop loss to break even price
5. **Status Logging** - Reports all SL updates in status log

### Configuration:

The break even logic is controlled by settings in `App/config.py`:
- `BE_POINTS` - Points to add above/below break even for buffer
- Default is usually 1000 points (10 pips for most pairs)

### Visual Indicators:

- **✅ Active: All+SIG+INPUT** - Shows which modes are enabled
- **❌ Inactive** - No auto BE modes enabled
- **Status Messages** - Real-time updates in status log

### Best Practices:

1. **Start with "All Orders"** - Good default for most users
2. **Use Specific Filters** - Enable SIG/INPUT only if you use those prefixes
3. **Monitor Status Log** - Watch for SL update confirmations
4. **Set Appropriate Refresh Interval** - 15-30 seconds recommended

## Troubleshooting

### Auto SL to BE Not Visible:
- The controls are in the Orders tab, in the control panel area
- Look for two rows of controls at the top of the Orders tab
- If still not visible, try maximizing the window

### Quiet Mode Not Working:
- Make sure you've installed system tray support: `install_tray_support.bat`
- Check that pystray and Pillow are installed
- Look for the app icon in the system tray (bottom-right corner)

### MT5 Still Shows GUI:
- The quiet mode hides MT5 when starting fresh
- If MT5 is already running, it won't hide the existing window
- Close MT5 completely before using quiet mode

### Webhook Not Working in Quiet Mode:
- The webhook server continues running in quiet mode
- Check the webhook status from the system tray menu
- All background processes remain active when minimized

## Usage Examples:

### Daily Trading Setup:
1. **Morning**: Double-click `start_app_quiet.bat`
2. **Check Status**: Right-click tray icon → "Webhook Status"
3. **Monitor**: App runs silently in background
4. **Evening**: Right-click tray icon → "Exit"

### Development/Testing:
1. **Normal Start**: Double-click `start_app.bat` (visible window)
2. **Debug**: Use the Debug button in Orders tab
3. **Monitor**: Watch status messages for auto BE activity

### Server/VPS Deployment:
1. **Quiet Start**: Use `start_app_quiet.bat` for headless operation
2. **Remote Access**: Use system tray or webhook endpoints
3. **Monitoring**: Check logs and status remotely

The quiet startup feature makes your app perfect for running in the background while maintaining full functionality!
