
import MetaTrader5 as mt5
import customtkinter as ctk
import os
import threading
import time
from datetime import datetime

# ===========================
# Class: TabAccount
# ===========================
class TabControl:
    def __init__(self, master, config, util):
        self.frame = master.add("Control")
        self.config = config
        self.util = util
        self.loop_running = False
        # self.auto_be_enabled = False
        self.thread1 = None
        # self.thread2 = None
        self.time_var = ctk.IntVar(value=1)
        self.filter_var = ctk.StringVar(value="")
        self.symbol_var = ctk.StringVar(value="XU")
        self.lot_var = ctk.StringVar(value="0.6")
        self.pending_filter_var = ctk.StringVar()  # Filter for pending orders

        self.form1 = ctk.CTkFrame(self.frame)
        self.form1.pack(pady=20)
        # self.form2 = ctk.CTkFrame(self.frame)
        # self.form2.pack(pady=20)
        self.form3 = ctk.CTkFrame(self.frame)
        self.form3.pack(pady=20)
        
        self.build_form()
        # self.start_loop()

    def build_form(self): 
        pady = 2
        padx = 10

        ctk.CTkLabel(self.form1, text="Close Order", anchor="w", justify="left", width=125).grid(row=0, column=0, padx=padx, pady=pady)

        self.symbol_label = ctk.CTkLabel(self.form1, text="Symbol:", anchor="e", justify="right", width=125)
        self.symbol_label.grid(row=0, column=1, padx=10, pady=5)
        self.symbol_var.set("XU")  # Default symbol
        self.symbol_dropdown = ctk.CTkOptionMenu(self.form1, values=list(self.config.symbols), variable=self.symbol_var)
        self.symbol_dropdown.grid(row=0, column=2, padx=10, pady=5)
        
        # symbol = self.util.get_symbol(self.symbol_var)
        ctk.CTkButton(self.form1, text="Close All",                                 command=lambda: self.util.close_orders_by_condition(self.util.get_symbol(self.symbol_var), "all")).grid(row=1, column=0, padx=padx, pady=pady)
        ctk.CTkButton(self.form1, text="Close All BUY", fg_color="green",           command=lambda: self.util.close_orders_by_condition(self.util.get_symbol(self.symbol_var), "all-buy")).grid(row=1, column=1, padx=padx, pady=pady)
        ctk.CTkButton(self.form1, text="Close All Sell", fg_color="red",            command=lambda: self.util.close_orders_by_condition(self.util.get_symbol(self.symbol_var), "all-sell")).grid(row=1, column=2, padx=padx, pady=pady)
        
        ctk.CTkButton(self.form1, text="Close All Profit",                          command=lambda: self.util.close_orders_by_condition(self.util.get_symbol(self.symbol_var), "all-profit")).grid(row=2, column=0, padx=padx, pady=pady)
        ctk.CTkButton(self.form1, text="Close BUY with Profit", fg_color="green",   command=lambda: self.util.close_orders_by_condition(self.util.get_symbol(self.symbol_var), "buy-profit")).grid(row=2, column=1, padx=padx, pady=pady)
        ctk.CTkButton(self.form1, text="Close Sell with Profit", fg_color="red",    command=lambda: self.util.close_orders_by_condition(self.util.get_symbol(self.symbol_var), "sell-profit")).grid(row=2, column=2, padx=padx, pady=pady)
     
        ctk.CTkButton(self.form1, text="Close All Loss",                            command=lambda: self.util.close_orders_by_condition(self.util.get_symbol(self.symbol_var), "all-loss")).grid(row=3, column=0, padx=padx, pady=pady)
        ctk.CTkButton(self.form1, text="Close BUY with Loss", fg_color="green",     command=lambda: self.util.close_orders_by_condition(self.util.get_symbol(self.symbol_var), "buy-loss")).grid(row=3, column=1, padx=padx, pady=pady)
        ctk.CTkButton(self.form1, text="Close Sell with Loss", fg_color="red",      command=lambda: self.util.close_orders_by_condition(self.util.get_symbol(self.symbol_var), "sell-loss")).grid(row=3, column=2, padx=padx, pady=pady)
     
        ctk.CTkLabel(self.form1, text="Set STOPLOSS to BREAKEVEN", anchor="w", justify="left", width=450).grid(row=4, column=0, padx=padx, pady=pady, columnspan=3)

        ctk.CTkButton(self.form1, text="Set All SL to BE",                          command=lambda: self.util.update_SL_to_BE_by_condition(self.util.get_symbol(self.symbol_var), "all")).grid(row=5, column=0, padx=padx, pady=pady)
        ctk.CTkButton(self.form1, text="Set BUY SL to BE", fg_color="green",        command=lambda: self.util.update_SL_to_BE_by_condition(self.util.get_symbol(self.symbol_var), "all-buy")).grid(row=5, column=1, padx=padx, pady=pady)
        ctk.CTkButton(self.form1, text="Set Sell SL to BE", fg_color="red",         command=lambda: self.util.update_SL_to_BE_by_condition(self.util.get_symbol(self.symbol_var), "all-sell")).grid(row=5, column=2, padx=padx, pady=pady)

        ctk.CTkLabel(self.form1, text="Set PROFIT to BREAKEVEN", anchor="w", justify="left", width=450).grid(row=6, column=0, padx=padx, pady=pady, columnspan=3)

        ctk.CTkButton(self.form1, text="Set All TP to BE",                          command=lambda: self.util.update_TP_to_BE_by_condition(self.util.get_symbol(self.symbol_var), "all")).grid(row=7, column=0, padx=padx, pady=pady)
        ctk.CTkButton(self.form1, text="Set BUY TP to BE", fg_color="green",        command=lambda: self.util.update_TP_to_BE_by_condition(self.util.get_symbol(self.symbol_var), "all-buy")).grid(row=7, column=1, padx=padx, pady=pady)
        ctk.CTkButton(self.form1, text="Set Sell TP to BE", fg_color="red",         command=lambda: self.util.update_TP_to_BE_by_condition(self.util.get_symbol(self.symbol_var), "all-sell")).grid(row=7, column=2, padx=padx, pady=pady)

        # Trailing Stop Section
        ctk.CTkLabel(self.form1, text="TRAILING STOPLOSS", anchor="w", justify="left", width=450).grid(row=8, column=0, padx=padx, pady=pady, columnspan=3)

        ctk.CTkButton(self.form1, text="Set Trailing SL", fg_color="purple",        command=lambda: self.set_trailing_sl()).grid(row=9, column=0, padx=padx, pady=pady)
        ctk.CTkButton(self.form1, text="Set BUY Trailing SL", fg_color="green",     command=lambda: self.set_trailing_sl("buy")).grid(row=9, column=1, padx=padx, pady=pady)
        ctk.CTkButton(self.form1, text="Set SELL Trailing SL", fg_color="red",      command=lambda: self.set_trailing_sl("sell")).grid(row=9, column=2, padx=padx, pady=pady)

        ctk.CTkEntry(self.form3, textvariable=self.lot_var).grid(row=1, column=0, padx=padx, pady=(40, 0))

        ctk.CTkButton(self.form3, text="Force Renew (GBPUSD)", fg_color="orange",   command=self.force_renew).grid(row=1, column=1, padx=padx, pady=(40, 0))

    def set_trailing_sl(self, order_type="all"):
        """Manually trigger trailing stop loss calculation - wrapper for util function"""
        symbol = self.util.get_symbol(self.symbol_var)
        return self.util.set_trailing_sl(symbol, order_type)

        # # Pending Orders Section
        # ctk.CTkLabel(self.form3, text="PENDING ORDERS CONTROL", font=ctk.CTkFont(size=16, weight="bold")).grid(row=0, column=0, columnspan=3, padx=padx, pady=(pady, 5))

        # # Filter input for pending orders
        # ctk.CTkLabel(self.form3, text="Filter (endswith):").grid(row=1, column=0, padx=padx, pady=pady, sticky="e")
        # ctk.CTkEntry(self.form3, textvariable=self.pending_filter_var, placeholder_text="Leave empty for all orders").grid(row=1, column=1, padx=padx, pady=pady, sticky="ew")

        # # Button to close pending orders
        # ctk.CTkButton(self.form3, text="Close Pending Orders",
        #              fg_color="orange", hover_color="darkorange",
        #              command=self.close_pending_orders).grid(row=1, column=2, padx=padx, pady=pady)

        # Configure column weights for proper resizing
        # self.form3.grid_columnconfigure(1, weight=1)

    def close_pending_orders(self):
        """Close pending orders based on filter"""
        symbol = self.util.get_symbol(self.symbol_var)
        filter_comment = self.pending_filter_var.get().strip()

        # Call the utility function to close pending orders
        closed_count = self.util.close_pending_orders_by_filter(symbol, filter_comment)

        if closed_count > 0:
            filter_msg = f" with filter '{filter_comment}'" if filter_comment else ""
            self.util.add_status_frame(f"✅ Successfully cancelled {closed_count} pending orders for {symbol}{filter_msg}", "green")
        else:
            filter_msg = f" with filter '{filter_comment}'" if filter_comment else ""
            self.util.add_status_frame(f"ℹ️ No pending orders found for {symbol}{filter_msg}", "yellow")

    # def toggle_auto_be(self):
    #     if self.switch_auto_be.get():
    #         self.start_loop()
    #     else:
    #         self.stop_loop()

    # def start_loop(self):
    #     if not self.loop_running:
    #         self.loop_running = True 
    #         self.thread1 = threading.Thread(target=self.checking_loop, daemon=True)
    #         self.thread1.start()
    #     self.util.add_status_frame(f"🟢 Monitoring Set BE started {self.filter_var.get()}")

    # def stop_loop(self):
    #     self.loop_running = False
    #     self.util.add_status_frame(f"🔴 Monitoring Set BE stopped {self.filter_var.get()}")

    # def checking_loop(self):
    #     try:
    #         symbol = self.util.get_symbol(self.symbol_var)
    #         i = 0 # Loop ตรวจสอบ
    #         while self.loop_running:
    #             if self.util.is_in_restricted_time():
    #                 self.util.set_status_label(f"{time.strftime('%H:%M:%S')} ⏳ Restricted time (21:00 - 22:00 UTC). Skipping...", "yellow")
    #                 time.sleep(60*60) # 1 hour
    #             else:
    #                 i += 1
    #                 self.tf_count = self.util.update_SL_to_BE_by_point(symbol, self.filter_var.get(), False) 
    #                 self.util.set_status_label(f"{time.strftime('%H:%M:%S')} 🕒 Checking {i}: curently {self.tf_count['All_FT']} orders", "yellow")
    #                 time.sleep(self.time_var.get())  # 60 sec = 1 min /  300 sec = 5 mins
    #     except KeyboardInterrupt:
    #         print("Stopped by user")
    
    def force_renew(self):
        lot = self.lot_var.get() #/2
        symbol = "GBPUSD" + self.config.symbol_posfix # lowest spread
        # symbol = "EURUSD" + self.config.symbol_posfix # lowest spread
        # symbol = self.util.get_symbol(self.symbol_var)

        entryB = mt5.symbol_info_tick(symbol).ask
        entryS = mt5.symbol_info_tick(symbol).bid
        spread = entryB - entryS
        plus = spread*3

        slB = entryB - plus
        tpB = entryB + plus #+ spread 
        # self.util.send_order("Buy Now", symbol, lot, entryB, slB, tpB, "renew")
        self.util.send_order("Buy Now", symbol, lot, entryB, slB, tpB, "renew")

        # slS =  entryS + plus #+ spread
        # tpS =  entryS - plus
        # self.util.send_order("Sell Now", symbol, lot, entryS, slS, tpS, "renew")
        # self.util.send_order("Sell Now", symbol, lot, entryS, slS, tpS, "renew")

        self.util.close_orders_by_condition(symbol, "filter", "renew")