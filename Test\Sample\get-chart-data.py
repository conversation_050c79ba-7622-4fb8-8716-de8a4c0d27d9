import customtkinter as ctk
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import numpy as np
import talib as ta
import os
# ปรับสไตล์
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

# -----------------------------
# ฟังก์ชันจำลองข้อมูล 30m + สัญญาณ
# -----------------------------
def get_chart_data(type = "csv"):
    # -----------------------------
    # โหลดข้อมูลจาก CSV
    # -----------------------------
    if type == "csv":
        current_dir = os.path.dirname(os.path.abspath(__file__))
        df = pd.read_csv(os.path.join(current_dir, "30m.csv"))
        df['time'] = pd.to_datetime(df['time'])

        # สุ่มสัญญาณ long/short
        df["longRetest"] = False
        df["shortRetest"] = False
        df.loc[5, "longRetest"] = True
        df.loc[15, "shortRetest"] = True

    else:
        # สร้าง dataframe จำลอง
        df = pd.DataFrame({
            "time": pd.date_range("2024-01-01", periods=30, freq="30min"),
            "open": np.random.uniform(100, 110, size=30),
        })
        df["close"] = df["open"] + np.random.uniform(-2, 2, size=30)
        df["high"] = np.maximum(df["open"], df["close"]) + np.random.uniform(0, 1, size=30)
        df["low"] = np.minimum(df["open"], df["close"]) - np.random.uniform(0, 1, size=30)

    # คำนวณ EMA
    retest = 50
    df['emaRetest'] = ta.EMA(df['close'], timeperiod=retest)
    df['emaTrend'] = ta.EMA(df['close'], timeperiod=100)
    df['emaLast'] = ta.EMA(df['close'], timeperiod=200)

    return df

# -----------------------------
# ฟังก์ชันวาดกราฟ
# -----------------------------
def plot_chart(frame, df):
    fig, ax = plt.subplots(figsize=(16, 8), dpi=100)
    # fig.patch.set_facecolor('#242424')
    ax.set_facecolor("#242424")
    ax.tick_params(colors='white')
    ax.grid(True, linestyle="--", alpha=0.3)
 
    # วาดแท่งเทียน
    for i in range(len(df)):
        # color = 'lime' if df['close'][i] > df['open'][i] else 'red'
        color = 'white' if df['close'][i] > df['open'][i] else 'black'
        # t = df.iloc[i]
        # ax.plot([t['time'], t['time']], [t['low'], t['high']], color=color)
        # ax.plot([t['time'], t['time']], [t['open'], t['close']], color=color, linewidth=5)
        ax.plot([i, i], [df['low'][i], df['high'][i]], color=color)
        ax.add_patch(plt.Rectangle(
            (i - 0.3, min(df['open'][i], df['close'][i])),
            0.6,
            abs(df['open'][i] - df['close'][i]),
            color=color
        ))

    # วาดสัญญาณ
    for i in range(len(df)):
        if df['longRetest'][i]:
            ax.plot(i, df['low'][i] - 0.5, 'g^', markersize=10, color='lime')
        if df['shortRetest'][i]:
            ax.plot(i, df['high'][i] + 0.5, 'rv', markersize=10, color='red')

    ax.set_xticks(range(len(df)))
    ax.set_xticklabels(df["time"].dt.strftime("%H:%M"), rotation=45, ha='right')
    ax.set_title("30m Chart with Long/Short Retest Signals", color='white')

    # Embed chart ลงใน tkinter
    for widget in frame.winfo_children():
        widget.destroy() 
    canvas = FigureCanvasTkAgg(fig, master=frame)
    canvas.draw()
    canvas_widget = canvas.get_tk_widget()
    canvas_widget.pack(fill="both", expand=True)
    
    # # สร้าง Navigation Toolbar และฝังใน CTkFrame ด้านบนหรือด้านล่างกราฟ
    # toolbar = NavigationToolbar2Tk(canvas, frame)
    # toolbar.update()
    # toolbar.pack(side="top", fill="x")


# -----------------------------
# GUI App
# -----------------------------
def create_gui():
    app = ctk.CTk()
    app.geometry("900x600")
    app.title("Trading Signal Dashboard")

    chart_frame = ctk.CTkFrame(app)
    chart_frame.pack(pady=20, fill="both", expand=True)

    def refresh_chart():
        df = get_chart_data()
        plot_chart(chart_frame, df)

    refresh_btn = ctk.CTkButton(app, text="🔄 Refresh Chart", command=refresh_chart)
    refresh_btn.pack(pady=10)

    refresh_chart()  # วาดตอนเปิดแอป
    app.mainloop()

# เรียก GUI
create_gui()
