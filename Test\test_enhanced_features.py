#!/usr/bin/env python3
"""
Test script for enhanced AI Bot features including:
- Log level system
- Multi-timeframe chart data
- AI API integration setup
- Auto trading features
- Chart image upload
"""

import requests
import json
import time

# Configuration
WEBHOOK_URL = "http://localhost:5000"
AUTH_TOKEN = "your_auth_token_here"
ACCESS_TOKEN = "your_access_token_here"

def test_log_level_system():
    """Test the new log level system"""
    print("🧪 Testing Log Level System...")
    
    # Test different log levels
    levels = [1, 2, 3, 4, 5]
    level_names = ["Critical", "Error", "Warning", "Info", "Debug"]
    
    for level, name in zip(levels, level_names):
        print(f"  - Level {level} ({name}): Messages at this level and below should be visible")
    
    print("✅ Log level system test completed")
    print()

def test_multi_timeframe_chart_data():
    """Test multi-timeframe chart data webhook"""
    print("🧪 Testing Multi-timeframe Chart Data...")
    
    # Test single timeframe (backward compatibility)
    single_tf_data = {
        "symbol": "XAUUSD",
        "timeframe": "H1",
        "barback": 50
    }
    
    print("  Testing single timeframe request...")
    response = send_webhook_request("/webhook_chart_data", single_tf_data)
    if response and not response.get("error"):
        print(f"  ✅ Single timeframe: {response.get('bars_count', 0)} bars retrieved")
    else:
        print(f"  ❌ Single timeframe failed: {response.get('message', 'Unknown error') if response else 'No response'}")
    
    # Test multi-timeframe request
    multi_tf_data = {
        "symbol": "XAUUSD",
        "timeframes": ["M15", "H1", "H4"],
        "barback": 30
    }
    
    print("  Testing multi-timeframe request...")
    response = send_webhook_request("/webhook_chart_data", multi_tf_data)
    if response and not response.get("error"):
        total_bars = response.get("total_bars", 0)
        timeframes = response.get("timeframes", [])
        print(f"  ✅ Multi-timeframe: {len(timeframes)} timeframes, {total_bars} total bars")
        
        # Check individual timeframe data
        data = response.get("data", {})
        for tf in timeframes:
            tf_bars = len(data.get(tf, []))
            print(f"    - {tf}: {tf_bars} bars")
    else:
        print(f"  ❌ Multi-timeframe failed: {response.get('message', 'Unknown error') if response else 'No response'}")
    
    print("✅ Multi-timeframe chart data test completed")
    print()

def test_ai_bot_configuration():
    """Test AI bot configuration with new features"""
    print("🧪 Testing AI Bot Configuration...")
    
    # Test bot with all new features
    bot_config = {
        "name": "Enhanced XAUUSD Bot",
        "symbol": "XU",
        "timeframe": "H1",
        "bars_back": 100,
        "prompt": "Analyze XAUUSD chart data with technical indicators and provide trading recommendations.",
        "api_provider": "gpt",
        "auto_place_order": True,
        "schedule_check": True,
        "check_interval": 30,
        "use_chart_image": True,
        "chart_image_path": "/path/to/chart.png"
    }
    
    print("  Bot configuration features:")
    print(f"    - AI Provider: {bot_config['api_provider'].upper()}")
    print(f"    - Auto Place Order: {'✅' if bot_config['auto_place_order'] else '❌'}")
    print(f"    - Schedule Check: {'✅' if bot_config['schedule_check'] else '❌'}")
    if bot_config['schedule_check']:
        print(f"    - Check Interval: {bot_config['check_interval']} minutes")
    print(f"    - Chart Image: {'✅' if bot_config['use_chart_image'] else '❌'}")
    
    print("✅ AI bot configuration test completed")
    print()

def test_webhook_with_bot_params():
    """Test webhook with AI bot parameters"""
    print("🧪 Testing Webhook with AI Bot Parameters...")
    
    bot_data = {
        "symbol": "XAUUSD",
        "timeframes": ["H1", "H4"],
        "barback": 50,
        "bot_id": "bot_test123",
        "bot_name": "Test XAUUSD Bot",
        "custom_prompt": "Analyze the gold market trends and provide entry/exit signals."
    }
    
    print("  Testing webhook with bot parameters...")
    response = send_webhook_request("/webhook_chart_data", bot_data)
    if response and not response.get("error"):
        print(f"  ✅ Bot webhook successful")
        print(f"    - Bot ID: {response.get('bot_id', 'N/A')}")
        print(f"    - Bot Name: {response.get('bot_name', 'N/A')}")
        print(f"    - AI Analysis Ready: {'✅' if response.get('ai_analysis_ready') else '❌'}")
    else:
        print(f"  ❌ Bot webhook failed: {response.get('message', 'Unknown error') if response else 'No response'}")
    
    print("✅ Webhook with AI bot parameters test completed")
    print()

def send_webhook_request(endpoint, data):
    """Send webhook request with authentication"""
    try:
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {AUTH_TOKEN}",
            "X-Access-Token": ACCESS_TOKEN
        }
        
        response = requests.post(
            f"{WEBHOOK_URL}{endpoint}",
            json=data,
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"  ❌ HTTP {response.status_code}: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"  ❌ Request failed: {e}")
        return None

def main():
    """Run all enhanced feature tests"""
    print("🚀 Enhanced Features Test Suite")
    print("=" * 50)
    print()
    
    # Test log level system
    test_log_level_system()
    
    # Test multi-timeframe chart data
    test_multi_timeframe_chart_data()
    
    # Test AI bot configuration
    test_ai_bot_configuration()
    
    # Test webhook with bot parameters
    test_webhook_with_bot_params()
    
    print("🎉 All enhanced feature tests completed!")
    print()
    print("📋 Summary of New Features:")
    print("  ✅ Log Level System (1-5 importance levels)")
    print("  ✅ Multi-timeframe Chart Data Support")
    print("  ✅ AI API Provider Selection (GPT/Gemini)")
    print("  ✅ Auto Place Order Toggle")
    print("  ✅ Schedule Check with Intervals")
    print("  ✅ Chart Image Upload Support")
    print("  ✅ Enhanced AI Bot Configuration")
    print("  ✅ Default Symbol Changed to XAUUSD")

if __name__ == "__main__":
    main()
