# import os
import pandas as pd
import numpy as np
import talib as ta
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import mock_util as _util
# import objgraph
# from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
# import customtkinter as ctk
# from datetime import datetime, timedelta
pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)
# objgraph.show_growth()  # ดู object ที่เพิ่มขึ้น

# TF = "M30"
TF = "M5"
HTF = "H1"
isDrawSignal_1 = True
isDrawSignal_2 = True
isDrawSignal_3 = True
isDrawSignal_4 = True
isDrawSignal_5 = True
trades = []  

                
def compute_rsma_trend(df_BHTF):
    signals = []
    df_BHTF['rsi_1'] = ta.RSI(df_BHTF['close'], timeperiod=25)
    df_BHTF['sma_1'] = ta.SMA(df_BHTF['rsi_1'], timeperiod=50)
    
    df_BHTF['rsi_prev_1'] = df_BHTF['rsi_1'].shift(1)
    df_BHTF['sma_prev_1'] = df_BHTF['sma_1'].shift(1)

    df_BHTF['rsi_2'] = ta.RSI(df_BHTF['close'], timeperiod=50)
    df_BHTF['sma_2'] = ta.SMA(df_BHTF['rsi_2'], timeperiod=25)
    
    df_BHTF['rsi_prev_2'] = df_BHTF['rsi_2'].shift(1)
    df_BHTF['sma_prev_2'] = df_BHTF['sma_2'].shift(1)

    df_BHTF['rst_1'] = ((df_BHTF['rsi_prev_1'] > df_BHTF['sma_prev_1']) & (df_BHTF['rsi_1'] > df_BHTF['sma_1']))
    df_BHTF['rst_2'] = ((df_BHTF['rsi_prev_2'] > df_BHTF['sma_prev_2']) & (df_BHTF['rsi_2'] > df_BHTF['sma_2']))

    df_BHTF['rst_bull'] = (df_BHTF['rst_1'] & df_BHTF['rst_2']) 
    df_BHTF['rst_bear'] = (~df_BHTF['rst_1'] & ~df_BHTF['rst_2']) 

    df_BHTF['rsmaTrend'] = np.select(
        [df_BHTF['rst_bull'], df_BHTF['rst_bear']],
        [1, -1],
        default=0
    )
    df_BHTF['colorTrend'] = np.select(
        [df_BHTF['rst_bull'], df_BHTF['rst_bear']],
        ["green", "red"],
        default="white"
    ) 

    # Detect trend change
    df_BHTF['prevTrend'] = df_BHTF['rsmaTrend'].shift(1)

    # Bullish signal: -1 → 1
    bullish_signals = df_BHTF[(df_BHTF['prevTrend'] <= 0) & (df_BHTF['rsmaTrend'] > 0)]

    # Bearish signal: 1 → -1
    bearish_signals = df_BHTF[(df_BHTF['prevTrend'] >= 0) & (df_BHTF['rsmaTrend'] < 0)]

    # print(bullish_signals)
    # print(bearish_signals)

    for i in range(0, len(bullish_signals)-1):
        t = bullish_signals.iloc[i]
        plt.annotate('▲', xy=(t['time'], t['close']), xytext=(0, -45), textcoords='offset points',
                    ha='center', color='black', fontsize=20)
    for i in range(0, len(bearish_signals)-1):
        t = bearish_signals.iloc[i]
        plt.annotate('▼', xy=(t['time'], t['close']), xytext=(0, 45), textcoords='offset points',
                    ha='center', color='black', fontsize=20)

    return signals

def compute_signal_ema(df_AHTF, emaRetest, name, isDraw, color):
    signals = []
    if isDraw:
        df_AHTF['bodySize'] = abs(df_AHTF['open'] - df_AHTF['close'])
        # df_AHTF['candleSize'] = abs(df_AHTF['high'] - df_AHTF['low'])
        # df_AHTF['bodyPercent'] = (df_AHTF['bodySize'] * 100 / df_AHTF['candleSize']) #if  df_AHTF['candleSize'] != 0 else 0
        df_AHTF['isBull'] = df_AHTF['close'] > df_AHTF['open']
        df_AHTF['emaRetest'] = emaRetest 
        df_AHTF['longRetest'] = False
        df_AHTF['shortRetest'] = False
        # print(df_AHTF.iloc[-100:,-5:])
        # คำนวณ signal
        for i in range(3, len(df_AHTF)-1):
            # cur_AHTF = df_AHTF.iloc[i]
            # prev_AHTF = df_AHTF.iloc[i-1] 
            n_AHTF = df_AHTF.iloc[i-1]
            cur_AHTF = df_AHTF.iloc[i-2]
            prev_AHTF = df_AHTF.iloc[i-3]

            prevIsOverlapLong = (prev_AHTF['low'] <= prev_AHTF['emaRetest']) and (prev_AHTF['close'] >= prev_AHTF['emaRetest']) and (prev_AHTF['close'] - prev_AHTF['low']) > prev_AHTF['bodySize']
            curIsOverlapLong = (cur_AHTF['low'] <= cur_AHTF['emaRetest']) and (cur_AHTF['open'] >= cur_AHTF['emaRetest']) and (cur_AHTF['open'] - cur_AHTF['low']) > cur_AHTF['bodySize']

            prevIsOverlapShort = (prev_AHTF['high'] >= prev_AHTF['emaRetest']) and (prev_AHTF['close'] <= prev_AHTF['emaRetest']) and (prev_AHTF['high'] - prev_AHTF['close']) > prev_AHTF['bodySize']
            curIsOverlapShort = (cur_AHTF['high'] >= cur_AHTF['emaRetest']) and (cur_AHTF['open'] <= cur_AHTF['emaRetest']) and (cur_AHTF['high'] - cur_AHTF['open']) > cur_AHTF['bodySize']

            #validTrend = True# if not cur_AHTF['emaTrend'] else (cur_AHTF['low'] >= cur_AHTF['emaTrend'] if cur_AHTF['isBull'] else cur_AHTF['high'] <= cur_AHTF['emaTrend'])
             
            # ---- Long ----
            if (
                (prevIsOverlapLong or curIsOverlapLong) 
                and cur_AHTF['isBull'] 
                and not prev_AHTF['isBull'] 
                and n_AHTF['low'] > cur_AHTF['low']
                and cur_AHTF['rsmaTrend'] > 0
            ):
                df_AHTF.at[i, 'longRetest'] = True
                # signal = f"📈 Long Retest Signal @ {cur_AHTF['time']}"
                signals.append((cur_AHTF['time'], cur_AHTF['close'], 'long', i))

            # ---- Short ----
            elif (
                (prevIsOverlapShort or curIsOverlapShort)  
                and not cur_AHTF['isBull'] 
                and prev_AHTF['isBull'] 
                and n_AHTF['high'] < cur_AHTF['high']
                and cur_AHTF['rsmaTrend'] < 0
            ): 
                df_AHTF.at[i, 'shortRetest'] = True
                # signal = f"📉 Short Retest Signal @ {cur_AHTF['time']}"
                signals.append((cur_AHTF['time'], cur_AHTF['close'], 'short', i))
        
        for t, price, sig_type, idx in signals:
            if sig_type == 'long':
                plt.annotate(f'▲', xy=(t, price), xytext=(0, -45), textcoords='offset points',
                            ha='center', color=color, fontsize=20)
                plt.annotate(f'{idx}', xy=(t, price), xytext=(0, -65), textcoords='offset points',
                            ha='center', color=color, fontsize=10)
            else:
                plt.annotate(f'{idx}', xy=(t, price), xytext=(0, 65), textcoords='offset points',
                            ha='center', color=color, fontsize=10)
                plt.annotate(f'▼', xy=(t, price), xytext=(0, 45), textcoords='offset points',
                            ha='center', color=color, fontsize=20)

    print("")
    print("=========================")
    print(f"========= {name} ========")
    print("=========================")
    _util.simulate_trades(df_AHTF, 45, 15, True)
    return signals

# สร้าง figure และ axes ของ matplotlib
plt.style.use('dark_background')  # <- ธีมดำพร้อมใช้
fig, ax = plt.subplots(figsize=(14, 6), dpi=72)

df_AHTF = _util.get_from_csv(f"{TF}.csv")
df_BHTF = _util.get_from_csv(f"{HTF}.csv")
signals_r = compute_rsma_trend(df_BHTF)


src = df_AHTF['close']
ema1 = ta.EMA(src, timeperiod=9) 
ema2 = ta.EMA(src, timeperiod=20) 
ema3 = ta.EMA(src, timeperiod=50) 
ema4 = ta.EMA(src, timeperiod=100) 
ema5 = ta.EMA(src, timeperiod=200) 

df_TEMP = df_BHTF[['time', 'rsmaTrend', 'colorTrend']].copy()
df_TEMP = df_TEMP.rename(columns={'time': 'time_BHTF'})

list_AHTF = pd.merge_asof(
    df_AHTF.sort_values('time'),
    df_TEMP.sort_values('time_BHTF'),
    left_on='time',
    right_on='time_BHTF',
    direction='backward'
)


a_alpha = 0.5
ax.plot(list_AHTF['time'], ema1, label=f'EMA 9',   color='yellow', alpha=a_alpha)
ax.plot(list_AHTF['time'], ema2, label=f'EMA 20',  color='orange', alpha=a_alpha)
ax.plot(list_AHTF['time'], ema3, label=f'EMA 50',  color='red',    alpha=a_alpha)
ax.plot(list_AHTF['time'], ema4, label=f'EMA 100', color='aqua',   alpha=a_alpha)
ax.plot(list_AHTF['time'], ema5, label=f'EMA 200', color='fuchsia', alpha=a_alpha)

# คำนวณ EMA
signals_1 = compute_signal_ema(list_AHTF, ema1, "EMA 9", isDrawSignal_1, 'yellow')
signals_2 = compute_signal_ema(list_AHTF, ema2, "EMA 20", isDrawSignal_2, 'orange')
signals_3 = compute_signal_ema(list_AHTF, ema3, "EMA 50", isDrawSignal_3, 'red')
signals_4 = compute_signal_ema(list_AHTF, ema4, "EMA 100", isDrawSignal_4, 'aqua')
signals_5 = compute_signal_ema(list_AHTF, ema5, "EMA 200", isDrawSignal_5, 'fuchsia')

# trades_list_AHTF, summary1_table, summary2_table = _util.simulate_trades(list_AHTF, 30, 10, True)
# print(list_AHTF.iloc[-100:,-10:])

# วาดแท่งเทียน
for i in range(len(list_AHTF)):
    t = list_AHTF.iloc[i]
    color = 'lime' if t['close'] >= t['open'] else 'red'
    ax.plot([t['time'], t['time']], [t['low'], t['high']], color=color)
    ax.plot([t['time'], t['time']], [t['open'], t['close']], color=color, linewidth=5)


# --- Trend bar (fixed height above chart) ---
# Create a second axis sharing the same x-axis but independent y-axis
ax2 = ax.twinx()
bar_height = 0.1  # Just a small visual block
bar_bottom = 1.90 #list_AHTF['close'].max() * 0.02 # Slightly above the top y-limit of main chart

# Convert dates to matplotlib format
x_dates = mdates.date2num(list_AHTF['time'])

# Set colors
# colors = ['green' if t == 1 else 'red' for t in list_AHTF['rsmaTrend']]
# print(list_AHTF.iloc[-200:, -10:])

# Plot the color bar (use fill_between for a cleaner look) colorTrend
for i in range(len(list_AHTF)-1):
    ax2.fill_between(
        [x_dates[i], x_dates[i+1]],
        bar_bottom,
        bar_bottom + bar_height,
        color=list_AHTF.at[i, "colorTrend"],
        linewidth=0
    )

# Hide secondary y-axis
ax2.get_yaxis().set_visible(False)
ax2.set_ylim(0, 2)  # Enough height for fixed bar

fig.autofmt_xdate(rotation=45)
# ตั้งค่ารูปแบบของแกน X: เวลา + วันที่แบบย่อ
ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M\n%d %b')) # ตัวอย่าง: 15:30\n22 Apr
ax.set_title(f'{TF} Chart with EMA and Signal Arrows')
ax.set_ylabel("Price")
ax.legend(loc='lower right')
plt.grid(True, "both", "both")
plt.tight_layout()
# plt.xticks(rotation=45)
plt.show()

# app = ctk.CTk()
# app.geometry("900x600")
# app.title("Trading Signal Dashboard")
# chart_frame = ctk.CTkFrame(app)
# chart_frame.pack(pady=20, fill="both", expand=True)

# canvas = FigureCanvasTkAgg(fig, master=chart_frame)
# canvas.draw()
# canvas_widget = canvas.get_tk_widget()
# canvas_widget.pack(fill="both", expand=True)

# toolbar = NavigationToolbar2Tk(canvas, chart_frame)
# toolbar.update()
# toolbar.pack(side="top", fill="x")
