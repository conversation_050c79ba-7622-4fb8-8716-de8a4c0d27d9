# Webhook Access Token Authentication

This document explains how to use the webhook access token authentication feature that has been added to secure your webhook endpoints.

## Overview

The webhook system now requires dual-header authentication to prevent unauthorized access:
- `Authorization: Bearer <token>` header
- `X-Access-Token: <token>` header

Both headers must contain the same valid access token for the request to be accepted.

## Setup

### 1. Configure Access Token

Add your webhook access token to the `.env` file:

```env
WEBHOOK_ACCESS_TOKEN=your_secure_webhook_access_token_here
```

**Important:** Replace `your_secure_webhook_access_token_here` with a strong, unique token. Consider using a UUID or a long random string.

### 2. Generate a Secure Token

You can generate a secure token using various methods:

**Python:**
```python
import secrets
token = secrets.token_urlsafe(32)
print(f"WEBHOOK_ACCESS_TOKEN={token}")
```

**Online UUID Generator:**
- Visit https://www.uuidgenerator.net/
- Use the generated UUID as your token

**Command Line (Linux/Mac):**
```bash
openssl rand -base64 32
```

## Usage

### Client-Side Implementation

When sending requests to webhook endpoints, include both authentication headers:

```javascript
const accessToken = "your_secure_webhook_access_token_here";

const headers = {
  'Content-Type': 'application/json',
  'User-Agent': 'NextJS-Webhook-Proxy/1.0',
  'Authorization': `Bearer ${accessToken}`,
  'X-Access-Token': accessToken
};

const response = await fetch(webhookUrl, {
  method: 'POST',
  headers,
  body: JSON.stringify({
    ...data,
    _meta: {
      source: 'web_app',
      group: 'your_group',
      timestamp: new Date().toISOString(),
      accessToken: accessToken ? 'provided' : 'not_provided'
    }
  }),
});
```

### Python Example

```python
import requests

access_token = "your_secure_webhook_access_token_here"
webhook_url = "http://localhost:5000/webhook_instant"

headers = {
    'Content-Type': 'application/json',
    'Authorization': f'Bearer {access_token}',
    'X-Access-Token': access_token
}

data = {
    "s": "XAUUSD",
    "a": "Buy Now",
    "p": 2000.50,
    "c": "Test Order",
    "lot": 0.01
}

response = requests.post(webhook_url, headers=headers, json=data)
```

## Protected Endpoints

The following webhook endpoints now require authentication:

1. **`/webhook_input`** - For complex orders with multiple TP levels
2. **`/webhook_instant`** - For instant order execution
3. **`/webhook_control`** - For order management (close, modify, close pending orders)

## Error Responses

### Missing or Invalid Token

**Status Code:** `401 Unauthorized`

**Response:**
```json
{
  "error": true,
  "message": "Access denied: Missing Authorization Bearer token; Missing X-Access-Token header"
}
```

### Token Mismatch

**Status Code:** `401 Unauthorized`

**Response:**
```json
{
  "error": true,
  "message": "Access denied: Invalid Authorization Bearer token; Invalid X-Access-Token header"
}
```

### Webhook Disabled

**Status Code:** `200 OK`

**Response:**
```json
{
  "error": true,
  "message": "Webhook disabled"
}
```

## Testing

Use the provided test script to verify your authentication setup:

```bash
python test_webhook_auth.py
```

The test script will:
1. Test requests without authentication (should fail)
2. Test requests with wrong tokens (should fail)
3. Test requests with correct tokens (should succeed)

## Security Best Practices

1. **Use Strong Tokens:** Generate tokens with at least 32 characters of entropy
2. **Keep Tokens Secret:** Never commit tokens to version control
3. **Rotate Tokens:** Periodically change your access tokens
4. **Use HTTPS:** Always use HTTPS in production to encrypt token transmission
5. **Monitor Access:** Check webhook logs for unauthorized access attempts

## Troubleshooting

### Common Issues

1. **"Access token not configured" error:**
   - Ensure `WEBHOOK_ACCESS_TOKEN` is set in your `.env` file
   - Make sure it's not the default placeholder value

2. **Authentication still failing:**
   - Verify both headers are being sent
   - Check that both headers contain the exact same token value
   - Ensure there are no extra spaces or characters in the token

3. **Webhook server not responding:**
   - Confirm the webhook server is running
   - Check the correct port (default: 5000)
   - Verify firewall settings if accessing remotely

### Debug Mode

To see detailed authentication logs, check the application status messages. Failed authentication attempts will be logged with specific error details.

## Migration from Unprotected Webhooks

If you're upgrading from a version without authentication:

1. Add the `WEBHOOK_ACCESS_TOKEN` to your `.env` file
2. Update all webhook clients to include the authentication headers
3. Test thoroughly before deploying to production
4. Consider implementing a grace period where both authenticated and unauthenticated requests are accepted during migration

## New Feature: Close Pending Orders

### Webhook Control - Close Inactive Orders

The `/webhook_control` endpoint now supports closing pending orders (Limit and Stop orders that haven't been filled yet) using the `close-inactive` action.

**Request Format:**
```json
{
  "s": "XAUUSD",
  "a": "close-inactive",
  "c": "",
  "filter": "optional_filter_string"
}
```

**Parameters:**
- `s`: Symbol (required)
- `a`: Action - use `"close-inactive"` (required)
- `c`: Condition (can be empty for this action)
- `filter`: Optional filter string. If empty, closes all pending orders. If provided, only closes orders where the comment ends with this filter string.

**Examples:**

1. **Close all pending orders for a symbol:**
```javascript
const data = {
  s: "XAUUSD",
  a: "close-inactive",
  c: "",
  filter: ""
};
```

2. **Close pending orders with specific filter:**
```javascript
const data = {
  s: "XAUUSD",
  a: "close-inactive",
  c: "",
  filter: "signal_123"  // Only closes orders where comment ends with "signal_123"
};
```

### UI Control Panel

The Control tab now includes a new section for managing pending orders:

1. **Filter Input Field**: Enter a filter string (uses `endswith` matching)
2. **Close Pending Orders Button**: Closes pending orders based on the filter
3. **Empty Filter**: If the filter field is empty, all pending orders for the selected symbol will be closed

## Support

If you encounter issues with webhook authentication:

1. Check the application logs for detailed error messages
2. Verify your token configuration
3. Test with the provided test script
4. Ensure your client is sending both required headers correctly
