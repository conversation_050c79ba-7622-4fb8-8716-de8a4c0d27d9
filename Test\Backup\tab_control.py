
import MetaTrader5 as mt5
import customtkinter as ctk
import os

# ===========================
# Class: TabAccount
# ===========================
class TabControl:
    def __init__(self, master, config, util):
        self.frame = master.add("Control")
        self.config = config
        self.util = util

        self.symbol_var = ctk.StringVar(value="XU")
        
        self.form1 = ctk.CTkFrame(self.frame)
        self.form1.pack(pady=20)

        self.build_form()

    def build_form(self): 
        pady = 2
        padx = 10

        ctk.CTkLabel(self.form1, text="Close Order", anchor="w", justify="left", width=125).grid(row=0, column=0, padx=padx, pady=pady)

        self.symbol_label = ctk.CTkLabel(self.form1, text="Symbol:", anchor="e", justify="right", width=125)
        self.symbol_label.grid(row=0, column=1, padx=10, pady=5)
        self.symbol_var.set("XU")  # Default symbol
        self.symbol_dropdown = ctk.CTkOptionMenu(self.form1, values=list(self.config.symbols), variable=self.symbol_var)
        self.symbol_dropdown.grid(row=0, column=2, padx=10, pady=5)
        
        # symbol = self.util.get_symbol(self.symbol_var)
        ctk.CTkButton(self.form1, text="Close All", command=lambda: self.util.close_orders_by_condition(self.util.get_symbol(self.symbol_var), "all")).grid(row=1, column=0, padx=padx, pady=pady)
        ctk.CTkButton(self.form1, text="Close All BUY", fg_color="green", command=lambda: self.util.close_orders_by_condition(self.util.get_symbol(self.symbol_var), "all-buy")).grid(row=1, column=1, padx=padx, pady=pady)
        ctk.CTkButton(self.form1, text="Close All Sell", fg_color="red", command=lambda: self.util.close_orders_by_condition(self.util.get_symbol(self.symbol_var), "all-sell")).grid(row=1, column=2, padx=padx, pady=pady)
        
        ctk.CTkButton(self.form1, text="Close All Profit", command=lambda: self.util.close_orders_by_condition(self.util.get_symbol(self.symbol_var), "all-profit")).grid(row=2, column=0, padx=padx, pady=pady)
        ctk.CTkButton(self.form1, text="Close BUY with Profit", fg_color="green", command=lambda: self.util.close_orders_by_condition(self.util.get_symbol(self.symbol_var), "buy-profit")).grid(row=2, column=1, padx=padx, pady=pady)
        ctk.CTkButton(self.form1, text="Close Sell with Profit", fg_color="red", command=lambda: self.util.close_orders_by_condition(self.util.get_symbol(self.symbol_var), "sell-profit")).grid(row=2, column=2, padx=padx, pady=pady)
     
        ctk.CTkButton(self.form1, text="Close All Loss", command=lambda: self.util.close_orders_by_condition(self.util.get_symbol(self.symbol_var), "all-loss")).grid(row=3, column=0, padx=padx, pady=pady)
        ctk.CTkButton(self.form1, text="Close BUY with Loss", fg_color="green", command=lambda: self.util.close_orders_by_condition(self.util.get_symbol(self.symbol_var), "buy-loss")).grid(row=3, column=1, padx=padx, pady=pady)
        ctk.CTkButton(self.form1, text="Close Sell with Loss", fg_color="red", command=lambda: self.util.close_orders_by_condition(self.util.get_symbol(self.symbol_var), "sell-loss")).grid(row=3, column=2, padx=padx, pady=pady)
     
        ctk.CTkLabel(self.form1, text="Set STOPLOSS to BREAKEVEN", anchor="w", justify="left", width=450).grid(row=4, column=0, padx=padx, pady=pady, columnspan=3)

        ctk.CTkButton(self.form1, text="Set All SL to BE", command=lambda: self.util.update_SL_to_BE_by_condition(self.util.get_symbol(self.symbol_var), "all")).grid(row=5, column=0, padx=padx, pady=pady)
        ctk.CTkButton(self.form1, text="Set BUY SL to BE", fg_color="green", command=lambda: self.util.update_SL_to_BE_by_condition(self.util.get_symbol(self.symbol_var), "all-buy")).grid(row=5, column=1, padx=padx, pady=pady)
        ctk.CTkButton(self.form1, text="Set Sell SL to BE", fg_color="red", command=lambda: self.util.update_SL_to_BE_by_condition(self.util.get_symbol(self.symbol_var), "all-sell")).grid(row=5, column=2, padx=padx, pady=pady)
     
        ctk.CTkLabel(self.form1, text="Set PROFIT to BREAKEVEN", anchor="w", justify="left", width=450).grid(row=6, column=0, padx=padx, pady=pady, columnspan=3)
        
        ctk.CTkButton(self.form1, text="Set All TP to BE", command=lambda: self.util.update_TP_to_BE_by_condition(self.util.get_symbol(self.symbol_var), "all")).grid(row=7, column=0, padx=padx, pady=pady)
        ctk.CTkButton(self.form1, text="Set BUY TP to BE", fg_color="green", command=lambda: self.util.update_TP_to_BE_by_condition(self.util.get_symbol(self.symbol_var), "all-buy")).grid(row=7, column=1, padx=padx, pady=pady)
        ctk.CTkButton(self.form1, text="Set Sell TP to BE", fg_color="red", command=lambda: self.util.update_TP_to_BE_by_condition(self.util.get_symbol(self.symbol_var), "all-sell")).grid(row=7, column=2, padx=padx, pady=pady)
        
        ctk.CTkButton(self.form1, text="Force Renew", fg_color="orange", command=self.force_renew).grid(row=8, column=2, padx=padx, pady=(40, 0))
     
    def force_renew(self):
        lot = 0.5
        symbol = "GBPUSD" + self.config.symbol_posfix.get() # lowest spread
        # symbol = "EURUSD" + self.config.symbol_posfix.get() # lowest spread
        # symbol = self.util.get_symbol(self.symbol_var)

        entryB = mt5.symbol_info_tick(symbol).ask
        entryS = mt5.symbol_info_tick(symbol).bid
        spread = entryB - entryS
        plus = spread*2

        # slB = entryB - plus
        # tpB = entryB + plus + spread
        # self.util.send_order("Buy Now", symbol, lot, entryB, slB, tpB, "renew")

        slS =  entryS + plus + spread
        tpS =  entryS - plus
        self.util.send_order("Sell Now", symbol, lot, entryS, slS, tpS, "renew")
        self.util.send_order("Sell Now", symbol, lot, entryS, slS, tpS, "renew")

        self.util.close_orders_by_condition(self.util.get_symbol(self.symbol_var), "filter", "renew")