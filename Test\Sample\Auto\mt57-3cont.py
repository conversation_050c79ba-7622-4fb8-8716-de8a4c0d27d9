import MetaTrader5 as mt5
import pandas as pd
from time import sleep
from dotenv import load_dotenv
import os
import time
import socket
import ta

load_dotenv()

folder_dir = os.getenv('DIR')
login = int(os.getenv('LOGIN'))
pwd = os.getenv('PWD')
server = os.getenv('SERVER')
symbol = os.getenv('SYMBOL') 
path = "C:\\Program Files\\MetaTrader 5\\terminal64.exe" 
strategy_name = "Candlestick Bot"

 
# connect to MetaTrader5 Terminal
if not mt5.initialize(path=path):
    print("Initialize() failed, error code =", mt5.last_error())
    quit()
else:
    if mt5.login(login,pwd,server):
        print("logged in succesffully")
    else: 
        print("login failed, error code: {}".format(mt5.last_error()))

print(f"Logged in to {server} as {login}")

# ปรับค่าพารามิเตอร์ 
timeframe = mt5.TIMEFRAME_M1  # กรอบเวลา 1 นาที
consecutive_bars_up = 3
consecutive_bars_down = 3

# ฟังก์ชันดึงข้อมูลแท่งเทียน
def get_prices(symbol, timeframe, bars=100):
    rates = mt5.copy_rates_from_pos(symbol, timeframe, 0, bars)
    return pd.DataFrame(rates)

# ฟังก์ชันตรวจสอบเงื่อนไข
def check_conditions(data):
    ups = 0
    downs = 0
    for i in range(1, len(data)):
        if data['close'][i] > data['close'][i - 1]:
            ups += 1
            downs = 0
        elif data['close'][i] < data['close'][i - 1]:
            downs += 1
            ups = 0
        else:
            ups = downs = 0
        
        # ตรวจสอบเงื่อนไข
        if ups >= consecutive_bars_up:
            return "buy"
        if downs >= consecutive_bars_down:
            return "sell"
    return None

def close_old_position(new_action): 
    # Retrieve all open positions
    positions = mt5.positions_get()

    if positions is None or len(positions) == 0:
        print("No open positions found.")
    else:
        print(f"Found {len(positions)} open positions.")
        for position in positions:
            symbol = position.symbol
            ticket = position.ticket
            lot_size = position.volume
            action = "sell" if position.type == mt5.ORDER_TYPE_BUY else "buy"

            if new_action != action:
                # Prepare the trade request
                request = {
                    "action": mt5.TRADE_ACTION_DEAL,
                    "symbol": symbol,
                    "volume": lot_size,
                    "type": mt5.ORDER_TYPE_SELL if position.type == mt5.ORDER_TYPE_BUY else mt5.ORDER_TYPE_BUY,
                    "position": ticket,
                    "price": mt5.symbol_info_tick(symbol).bid if action == "sell" else mt5.symbol_info_tick(symbol).ask,
                    "deviation": 20,
                    "magic": 123456,
                    "comment": "Close position",
                }

                # Send the trade request
                result = mt5.order_send(request)

        # Check the result
        if result.retcode == mt5.TRADE_RETCODE_DONE:
            print(f"Successfully closed position {ticket} on {symbol}.")
        else:
            print(f"Failed to close position {ticket} on {symbol}: {result.comment}")

# เริ่ม loop ตรวจสอบ
while True:
    try:
        data = get_prices(symbol, timeframe, bars=100)
        if not data.empty:
            action = check_conditions(data)
            if action == "buy":
                print("Placing a Buy order")
                close_old_position(action)
                mt5.order_send({
                    "action": mt5.TRADE_ACTION_DEAL,
                    "symbol": symbol,
                    "volume": 0.01,  # ขนาด lot
                    "type": mt5.ORDER_TYPE_BUY,
                    "price": mt5.symbol_info_tick(symbol).ask,
                    "deviation": 10,
                    "magic": 234000,
                    "comment": "Consecutive Up Strategy",
                })
            elif action == "sell":
                print("Placing a Sell order")
                close_old_position(action)
                mt5.order_send({
                    "action": mt5.TRADE_ACTION_DEAL,
                    "symbol": symbol,
                    "volume": 0.01,
                    "type": mt5.ORDER_TYPE_SELL,
                    "price": mt5.symbol_info_tick(symbol).bid,
                    "deviation": 10,
                    "magic": 234000,
                    "comment": "Consecutive Down Strategy",
                })
    except Exception as e:
        print(f"Error: {e}")
    time.sleep(1)  # หน่วงเวลาการตรวจสอบทุกๆ 1 วินาที

# ปิดการเชื่อมต่อ MT5
mt5.shutdown()