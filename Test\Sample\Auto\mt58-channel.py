import numpy as np
import pandas as pd
import talib as ta
import os
import MetaTrader5 as mt5
from time import sleep
from dotenv import load_dotenv

load_dotenv()
folder_dir = os.getenv('DIR')
login = int(os.getenv('LOGIN'))
pwd = os.getenv('PWD')
server = os.getenv('SERVER')
symbol = os.getenv('SYMBOL') 
path = "C:\\Program Files\\MetaTrader 5\\terminal64.exe"
strategy_name = "Candlestick Bot"

# connect to MetaTrader5 Terminal
if not mt5.initialize(path=path):
    print("Initialize() failed, error code =", mt5.last_error())
    quit()
else:
    if mt5.login(login,pwd,server):
        print("logged in succesffully")
    else: 
        print("login failed, error code: {}".format(mt5.last_error()))

print(f"Logged in to {server} as {login}")

# ตั้งค่าข้อมูล
symbol_info_tick = mt5.symbol_info_tick(symbol)
symbol_info = mt5.symbol_info(symbol)
timeframe = mt5.TIMEFRAME_M5
volume = 0.01
deviation=20
magic=30
stop_loss=0.0
take_profit=0.0
sl = 400
tp = 400
data_length = 500  # จำนวนแท่งเทียนที่ต้องการดึงข้อมูล


print('########################################################################################################\n')

def market_order(order_type, stop_loss, take_profit, strategy_name):
    symbol_info_tick = mt5.symbol_info_tick(symbol)

    order_type_dict = {
        'buy': mt5.ORDER_TYPE_BUY,
        'sell': mt5.ORDER_TYPE_SELL
    }

    price_dict = {
        'buy': symbol_info_tick.ask,
        'sell': symbol_info_tick.bid
    }

    request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": volume,  # FLOAT
        "type": order_type_dict[order_type],
        "price": price_dict[order_type],
        "sl": stop_loss,  # FLOAT
        # "tp": take_profit,  # FLOAT
        "deviation": deviation,  # INTERGER
        "magic": magic,  # INTERGER
        "comment": strategy_name,
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,  # mt5.ORDER_FILLING_FOK if IOC does not work
    }
 
    print(f"Signal {order_type}, ask price {price_dict[order_type]}, SL {stop_loss}, TP {take_profit}")
    order_result = mt5.order_send(request)
    return (order_result)
         
def close_old_position(new_action): 
    # Retrieve all open positions
    positions = mt5.positions_get()

    if positions is None or len(positions) == 0:
        print("No open positions found.")
    else:
        print(f"Found {len(positions)} open positions.")
        for position in positions:
            symbol = position.symbol
            ticket = position.ticket
            lot_size = position.volume
            action = "sell" if position.type == mt5.ORDER_TYPE_BUY else "buy"

            if new_action != action:
                # Prepare the trade request
                request = {
                    "action": mt5.TRADE_ACTION_DEAL,
                    "symbol": symbol,
                    "volume": lot_size,
                    "type": mt5.ORDER_TYPE_SELL if position.type == mt5.ORDER_TYPE_BUY else mt5.ORDER_TYPE_BUY,
                    "position": ticket,
                    "price": symbol_info_tick.bid if action == "sell" else symbol_info_tick.ask,
                    "deviation": 20,
                    "magic": 123456,
                    "comment": "Close position",
                }

                # Send the trade request
                result = mt5.order_send(request)

                # Check the result
                if result.retcode == mt5.TRADE_RETCODE_DONE:
                    print(f"Successfully closed position {ticket} on {symbol}.")
                else:
                    print(f"Failed to close position {ticket} on {symbol}: {result.comment}")

# ฟังก์ชันดึงข้อมูลแท่งเทียน
def get_prices(symbol, timeframe, bars=100):
    rates = mt5.copy_rates_from_pos(symbol, timeframe, 2, bars)
    if rates is not None:
        return pd.DataFrame(rates)
    else:
        return pd.DataFrame()

# ฟังก์ชันคำนวณค่า Channel Breakout
def calculate_channel(data, length):
    if len(data) >= length:
        high_series = data['high'][-length:]
        low_series = data['low'][-length:]
        up_bound = high_series.max()
        down_bound = low_series.min()
        return up_bound, down_bound
    return None, None
 
if __name__ == '__main__':

    length = 3  # ระยะเวลาคำนวณ Channel 
    i = 0 # Loop ตรวจสอบ

    trading_allowed = True
    while trading_allowed:
        symbol_info_tick = mt5.symbol_info_tick(symbol)
        i += 1
        print(f"Checking.. {i}")
        try:
            data = get_prices(symbol, timeframe, bars=length + 1)
            if not data.empty:
                up_bound, down_bound = calculate_channel(data, length)
                if up_bound and down_bound:
                    # ส่งคำสั่งซื้อขายตามเงื่อนไข
                    last_close = data['close'].iloc[-1]
                    ud = up_bound - last_close
                    dd = last_close - down_bound
                    print(f"Checking Close {last_close} | UP {up_bound} ({ud}) | DOWN {down_bound} ({dd})")
                    if last_close > up_bound:
                        point = symbol_info.point
                        stop_loss = last_close - point # * sl 
                        take_profit = symbol_info_tick.ask + point * tp 
                        close_old_position("buy")
                        market_order("buy", stop_loss, take_profit, strategy_name + " Long")
                        trading_allowed = False
                    elif last_close < down_bound: 
                        point = symbol_info.point
                        stop_loss = last_close + point # * sl 
                        take_profit = symbol_info_tick.bid - point * tp 
                        close_old_position("sell")
                        market_order("sell", stop_loss, take_profit, strategy_name + " Short")
                        trading_allowed = False
        except Exception as e:
            print(f"Error: {e}")
        sleep(1)  # หน่วงเวลาการตรวจสอบทุกๆ 1 วินาที

    # ปิดการเชื่อมต่อ MT5
    mt5.shutdown()