import customtkinter as ctk
import MetaTrader5 as mt5
from dotenv import load_dotenv
import os

# Load .env
load_dotenv()

accounts = {
    "Demo 1": "DEMO1",
    "Real 1": "REAL1",
    "Real 2": "REAL2"
}

SYMBOL = os.getenv("SYMBOL", "EURUSD")
PATH = "C:\\Program Files\\MetaTrader 5\\terminal64.exe"

# ตั้งค่าธีม
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

# สร้างหน้าต่าง
app = ctk.CTk()
app.geometry("500x400")
app.title("Trading GUI")

# สถานะการเชื่อมต่อ
status_label = ctk.CTkLabel(app, text="🔌 Status: Not connected", text_color="orange")
status_label.pack(pady=5)

# dropdown เลือกบัญชี
account_var = ctk.StringVar(value="Real 2")
account_menu = ctk.CTkOptionMenu(app, values=list(accounts.keys()), variable=account_var)
account_menu.pack(pady=10)

# ปุ่มเชื่อมต่อ
def connect():
    account_key = account_var.get()
    prefix = accounts[account_key]
    login = int(os.getenv(f"{prefix}_LOGIN"))
    pwd = os.getenv(f"{prefix}_PWD")
    server = os.getenv(f"{prefix}_SERVER")

    if not mt5.initialize(path=PATH):
        status_label.configure(text=f"❌ Init failed: {mt5.last_error()}", text_color="red")
        return

    if mt5.login(login, pwd, server):
        status_label.configure(text=f"✅ Connected: {account_key}", text_color="green")
    else:
        status_label.configure(text=f"❌ Login failed: {mt5.last_error()}", text_color="red")

ctk.CTkButton(app, text="Connect", command=connect).pack(pady=5)

# แสดง Symbol
symbol_label = ctk.CTkLabel(app, text=f"Symbol: {SYMBOL}")
symbol_label.pack(pady=5)

# Volume
volume_label = ctk.CTkLabel(app, text="Volume (Lot):")
volume_label.pack()
volume_input = ctk.CTkEntry(app, placeholder_text="e.g. 0.1")
volume_input.pack(pady=5)

# แสดงผลลัพธ์
result_label = ctk.CTkLabel(app, text="")
result_label.pack(pady=10)

# ฟังก์ชัน Buy/Sell
def send_order(action):
    volume = float(volume_input.get())
    order_type = mt5.ORDER_TYPE_BUY if action == "buy" else mt5.ORDER_TYPE_SELL
    price = mt5.symbol_info_tick(SYMBOL).ask if action == "buy" else mt5.symbol_info_tick(SYMBOL).bid

    request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": SYMBOL,
        "volume": volume,
        "type": order_type,
        "price": price,
        "deviation": 10,
        "magic": 10032025,
        "comment": "CTK Trade",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }

    result = mt5.order_send(request)
    if result.retcode == mt5.TRADE_RETCODE_DONE:
        result_label.configure(text=f"✅ {action.upper()} OK: {result.order}", text_color="green")
    else:
        result_label.configure(text=f"❌ {action.upper()} FAILED: {result.retcode}", text_color="red")

# ปุ่ม BUY/SELL
btn_frame = ctk.CTkFrame(app)
btn_frame.pack(pady=10)

ctk.CTkButton(btn_frame, text="BUY", fg_color="green", command=lambda: send_order("buy")).pack(side="left", padx=10)
ctk.CTkButton(btn_frame, text="SELL", fg_color="red", command=lambda: send_order("sell")).pack(side="right", padx=10)

# Start GUI
app.mainloop()
