import os
import pandas as pd
import numpy as np
import talib as ta
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import customtkinter as ctk
from datetime import datetime, timedelta
import mock_util as _util

# TF = "M30"
TF = "M15"
isDrawSignal_1 = True
isDrawSignal_2 = True
isDrawSignal_3 = True
isDrawSignal_4 = True
isDrawSignal_5 = True
 
def compute_signal(df, emaRetest, emaTrend, isDraw, color):
    signals = []
    if isDraw:
        df['emaRetest'] = emaRetest
        df['emaTrend'] = emaTrend

        df['candleSize'] = abs(df['high'] - df['low'])
        df['bodySize'] = abs(df['open'] - df['close'])
        df['bodyPercent'] = (df['bodySize'] * 100 /  df['candleSize']) #if  df['candleSize'] != 0 else 0
        
        # _cur_AHTF = df.shift(1)
        # _prev_AHTF = df.shift(2)
        
        # คำนวณ signal
        for i in range(3, len(df)-1):
            # cur_AHTF = df.iloc[i]
            # prev_AHTF = df.iloc[i-1]
            n_AHTF = df.iloc[i-1]
            cur_AHTF = df.iloc[i-2]
            prev_AHTF = df.iloc[i-3]
        
            # candleSize = abs(cur_AHTF['high'] - cur_AHTF['low']) 
            # bodySize = abs(cur_AHTF['open'] - cur_AHTF['close'])
            # bodyPercent = (bodySize * 100 / candleSize) if candleSize != 0 else 0

            haftPrice = (cur_AHTF['open'] + cur_AHTF['close']) / 2
            prevHaftPrice = (prev_AHTF['open'] + prev_AHTF['close']) / 2

            isBull = cur_AHTF['close'] > cur_AHTF['open']
            prevIsBull = prev_AHTF['close'] > prev_AHTF['open']

            if (abs(cur_AHTF['emaRetest'] - cur_AHTF['emaTrend']) > cur_AHTF['bodySize'] * 2) and cur_AHTF['bodyPercent'] > 50:

                prevIsOverlap = prev_AHTF['low'] <= prev_AHTF['emaRetest'] and prev_AHTF['emaRetest'] <= prev_AHTF['high']
                #validTrend = True if not cur_AHTF['emaTrend'] else (cur_AHTF['low'] >= cur_AHTF['emaTrend'] if isBull else cur_AHTF['high'] <= cur_AHTF['emaTrend'])
                # ---- Long ----
                if (
                    prevIsOverlap 
                    and prevHaftPrice > prev_AHTF['emaRetest'] 
                    and haftPrice > cur_AHTF['emaRetest'] 
                    and isBull and not prevIsBull 
                    and n_AHTF['low'] > cur_AHTF['low']
                ):
                    signal = f"📈 Long Retest Signal @ {cur_AHTF['time']}"
                    signals.append((cur_AHTF['time'], cur_AHTF['close'], 'long'))

                # ---- Short ----
                elif (
                    prevIsOverlap  
                    and prevHaftPrice < prev_AHTF['emaRetest']  
                    and haftPrice < cur_AHTF['emaRetest']  
                    and not isBull and prevIsBull  
                    and n_AHTF['high'] < cur_AHTF['high']
                ): 
                    signal = f"📉 Short Retest Signal @ {cur_AHTF['time']}"
                    signals.append((cur_AHTF['time'], cur_AHTF['close'], 'short'))
        
        for t, price, sig_type in signals:
            if sig_type == 'long':
                plt.annotate('▲', xy=(t, price), xytext=(0, -45), textcoords='offset points',
                            ha='center', color=color, fontsize=20)
            else:
                plt.annotate('▼', xy=(t, price), xytext=(0, 45), textcoords='offset points',
                            ha='center', color=color, fontsize=20)

    return signals

# df = generate_mock_data('2025-04-18 09:00:00', periods=300, freq='30T')
df = _util.get_from_csv(f"{TF}.csv")
src = df['close']
ema1 = src.ewm(span=9).mean()
ema2 = src.ewm(span=20).mean()
ema3 = src.ewm(span=50).mean()
ema4 = src.ewm(span=100).mean()
ema5 = src.ewm(span=200).mean()
# ema1 = ta.EMA(src, timeperiod=9) 
# ema2 = ta.EMA(src, timeperiod=20) 
# ema3 = ta.EMA(src, timeperiod=50) 
# ema4 = ta.EMA(src, timeperiod=100) 
# ema5 = ta.EMA(src, timeperiod=200) 
# ema6 = ta.EMA(src, timeperiod=300) 
 

# สร้าง figure และ axes ของ matplotlib
fig, ax = plt.subplots(figsize=(14, 6), dpi=72)
ax.set_title("กราฟแบบ Interactive")

# ตั้งค่ารูปแบบของแกน X: เวลา + วันที่แบบย่อ
formatter = mdates.DateFormatter('%H:%M\n%d %b')  # ตัวอย่าง: 15:30\n22 Apr
ax.xaxis.set_major_formatter(formatter)

# เพิ่มระยะห่างเพื่อให้มองเห็น label ชัดเจน
fig.autofmt_xdate(rotation=0)

a_alpha = 0.5
ax.plot(df['time'], ema1, label=f'EMA 9',   color='yellow', alpha=a_alpha)
ax.plot(df['time'], ema2, label=f'EMA 20',  color='orange', alpha=a_alpha)
ax.plot(df['time'], ema3, label=f'EMA 50',  color='red',    alpha=a_alpha)
ax.plot(df['time'], ema4, label=f'EMA 100', color='blue',   alpha=a_alpha)
ax.plot(df['time'], ema5, label=f'EMA 200', color='purple', alpha=a_alpha)

# คำนวณ EMA
signals_1 = compute_signal(df, ema1, ema4, isDrawSignal_1, 'yellow')
signals_2 = compute_signal(df, ema2, ema4, isDrawSignal_2, 'orange')
signals_3 = compute_signal(df, ema3, ema4, isDrawSignal_3, 'red')
signals_4 = compute_signal(df, ema4, ema5, isDrawSignal_4, 'blue')
signals_5 = compute_signal(df, ema5, ema4, isDrawSignal_5, 'purple') 

# วาดแท่งเทียน
for i in range(len(df)):
    t = df.iloc[i]
    color = 'lime' if t['close'] >= t['open'] else 'red'
    ax.plot([t['time'], t['time']], [t['low'], t['high']], color=color)
    ax.plot([t['time'], t['time']], [t['open'], t['close']], color=color, linewidth=5)

plt.legend()
plt.title(f'{TF} Chart with EMA and Signal Arrows')
plt.grid(True, "both", "both")
plt.tight_layout()
# plt.xticks(rotation=45)
plt.show()

# app = ctk.CTk()
# app.geometry("900x600")
# app.title("Trading Signal Dashboard")
# chart_frame = ctk.CTkFrame(app)
# chart_frame.pack(pady=20, fill="both", expand=True)

# canvas = FigureCanvasTkAgg(fig, master=chart_frame)
# canvas.draw()
# canvas_widget = canvas.get_tk_widget()
# canvas_widget.pack(fill="both", expand=True)

# toolbar = NavigationToolbar2Tk(canvas, chart_frame)
# toolbar.update()
# toolbar.pack(side="top", fill="x")
