#!/usr/bin/env python3
"""
Test script for AI Bots functionality
"""

import requests
import json
import time

def test_ai_bot_webhook():
    """Test the AI bot integration with webhook chart data endpoint"""
    print("Testing AI Bot Webhook Integration...")
    
    # Webhook configuration
    webhook_url = "http://localhost:5000/webhook_chart_data"
    
    # Test AI bot configurations
    test_bots = [
        {
            "bot_id": "bot_eurusd_scalper",
            "bot_name": "EURUSD Scalper Bot",
            "symbol": "EURUSD",
            "timeframe": "M15",
            "barback": 50,
            "custom_prompt": "Analyze the EURUSD M15 chart for scalping opportunities. Focus on RSI divergences and EMA crossovers. Provide entry/exit signals with risk management."
        },
        {
            "bot_id": "bot_gbpusd_swing",
            "bot_name": "GBPUSD Swing Bot", 
            "symbol": "GBPUSD",
            "timeframe": "H4",
            "barback": 100,
            "custom_prompt": "Perform swing trading analysis on GBPUSD H4 timeframe. Look for trend reversals using MACD and RSI indicators. Suggest position sizing and stop loss levels."
        },
        {
            "bot_id": "bot_gold_trend",
            "bot_name": "Gold Trend Following Bot",
            "symbol": "XAUUSD", 
            "timeframe": "H1",
            "barback": 200,
            "custom_prompt": "Analyze XAUUSD for trend following opportunities. Use EMA20 as trend filter and RSI for momentum confirmation. Provide detailed market structure analysis."
        }
    ]
    
    # Headers for authentication (replace with actual tokens)
    headers = {
        "Authorization": "Bearer your_bearer_token_here",
        "X-Access-Token": "your_access_token_here",
        "Content-Type": "application/json"
    }
    
    print(f"\nTesting endpoint: {webhook_url}")
    print("Note: Make sure the webhook server is running first!")
    
    for i, bot_config in enumerate(test_bots, 1):
        print(f"\n{'='*60}")
        print(f"Test {i}: {bot_config['bot_name']}")
        print(f"{'='*60}")
        
        try:
            # Send POST request with AI bot data
            response = requests.post(
                webhook_url,
                headers=headers,
                json=bot_config,
                timeout=30
            )
            
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Success: {data.get('message', 'No message')}")
                print(f"📊 Symbol: {data.get('symbol', 'N/A')}")
                print(f"⏰ Timeframe: {data.get('timeframe', 'N/A')}")
                print(f"📈 Bars Count: {data.get('bars_count', 'N/A')}")
                
                # AI Bot specific response data
                if data.get('bot_id'):
                    print(f"🤖 Bot ID: {data.get('bot_id')}")
                if data.get('bot_name'):
                    print(f"🏷️ Bot Name: {data.get('bot_name')}")
                if data.get('custom_prompt'):
                    print(f"💬 Custom Prompt: {data.get('custom_prompt')[:100]}...")
                if data.get('ai_analysis_ready'):
                    print(f"🧠 AI Analysis Ready: {data.get('ai_analysis_ready')}")
                
                # Show sample of chart data
                chart_data = data.get('data', [])
                if chart_data:
                    print(f"\n📊 Chart Data Sample (First Bar):")
                    first_bar = chart_data[0]
                    print(f"   Time: {first_bar.get('time')}")
                    print(f"   OHLC: {first_bar.get('open')}/{first_bar.get('high')}/{first_bar.get('low')}/{first_bar.get('close')}")
                    print(f"   EMA20: {first_bar.get('ema_20')}")
                    print(f"   RSI14: {first_bar.get('rsi_14')}")
                    print(f"   MACD: {first_bar.get('macd')}")
                    
                    if len(chart_data) > 1:
                        print(f"\n📊 Chart Data Sample (Last Bar):")
                        last_bar = chart_data[-1]
                        print(f"   Time: {last_bar.get('time')}")
                        print(f"   OHLC: {last_bar.get('open')}/{last_bar.get('high')}/{last_bar.get('low')}/{last_bar.get('close')}")
                        print(f"   EMA20: {last_bar.get('ema_20')}")
                        print(f"   RSI14: {last_bar.get('rsi_14')}")
                        print(f"   MACD: {last_bar.get('macd')}")
                
                print(f"\n🎯 Ready for AI Analysis:")
                print(f"   - Chart data with {len(chart_data)} bars retrieved")
                print(f"   - Technical indicators calculated")
                print(f"   - Custom prompt provided")
                print(f"   - Send this data to your AI service for analysis")
                
            else:
                print(f"❌ Error: {response.text}")
                
        except requests.exceptions.ConnectionError:
            print("❌ Connection failed - Is the webhook server running?")
        except requests.exceptions.Timeout:
            print("❌ Request timeout")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        # Small delay between requests
        if i < len(test_bots):
            time.sleep(2)
    
    print(f"\n{'='*60}")
    print("AI Bot Integration Test Summary")
    print(f"{'='*60}")
    print("✅ Features Tested:")
    print("   - AI bot specific parameters (bot_id, bot_name, custom_prompt)")
    print("   - Chart data retrieval with technical indicators")
    print("   - Response format for AI analysis")
    print("   - Multiple symbol/timeframe combinations")
    
    print(f"\n🔧 Integration Steps:")
    print("1. Start your trading application")
    print("2. Enable the webhook server")
    print("3. Go to 'AI Bots' tab and add/configure bots")
    print("4. Test individual bots or test all enabled bots")
    print("5. Use the webhook response data for AI analysis")
    
    print(f"\n📋 Expected AI Analysis Workflow:")
    print("1. Bot sends request to /webhook_chart_data")
    print("2. Receive OHLCV data + technical indicators")
    print("3. Send data + custom_prompt to AI service")
    print("4. AI analyzes data and provides trading insights")
    print("5. Display/act on AI recommendations")

def test_ai_bot_config():
    """Test AI bot configuration structure"""
    print(f"\n{'='*60}")
    print("AI Bot Configuration Test")
    print(f"{'='*60}")
    
    # Sample AI bot configurations
    sample_configs = {
        "bot_scalper_eu": {
            "name": "EURUSD Scalper",
            "symbol": "EU",  # Key from config.symbols
            "timeframe": "M15",
            "bars_back": 50,
            "prompt": "Analyze EURUSD M15 for scalping. Focus on RSI and EMA signals.",
            "enabled": True,
            "created_at": "2025-07-30 14:30:00",
            "last_used": None
        },
        "bot_swing_gu": {
            "name": "GBPUSD Swing Trader",
            "symbol": "GU",
            "timeframe": "H4", 
            "bars_back": 100,
            "prompt": "Swing trading analysis for GBPUSD H4. Look for trend reversals and momentum shifts.",
            "enabled": True,
            "created_at": "2025-07-30 14:35:00",
            "last_used": "2025-07-30 15:00:00"
        }
    }
    
    print("Sample AI Bot Configurations:")
    print(json.dumps(sample_configs, indent=2))
    
    print(f"\n✅ Configuration Features:")
    print("   - Unique bot IDs for identification")
    print("   - Human-readable bot names")
    print("   - Symbol keys mapped to full symbol names")
    print("   - Flexible timeframe selection")
    print("   - Configurable bars back for analysis")
    print("   - Custom prompts for AI analysis")
    print("   - Enable/disable functionality")
    print("   - Creation and usage timestamps")

if __name__ == "__main__":
    test_ai_bot_config()
    test_ai_bot_webhook()
