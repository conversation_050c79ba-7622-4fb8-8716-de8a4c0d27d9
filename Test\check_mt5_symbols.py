#!/usr/bin/env python3
"""
Check what symbols are actually available in MT5
"""

import MetaTrader5 as mt5

def check_mt5_symbols():
    """Check available symbols in MT5"""
    print("🔍 Checking MT5 Symbols")
    print("=" * 30)
    
    try:
        # Initialize MT5
        if not mt5.initialize():
            print("❌ MT5 initialization failed")
            return
        
        print("✅ MT5 initialized successfully")
        
        # Get account info
        account_info = mt5.account_info()
        if account_info:
            print(f"✅ Connected to account: {account_info.login}")
            print(f"   Server: {account_info.server}")
            print(f"   Company: {account_info.company}")
        
        # Get all symbols
        symbols = mt5.symbols_get()
        if not symbols:
            print("❌ No symbols retrieved")
            return
        
        print(f"✅ Found {len(symbols)} total symbols")
        
        # Look for gold-related symbols
        gold_symbols = []
        for symbol in symbols:
            name = symbol.name.upper()
            if any(keyword in name for keyword in ['GOLD', 'XAU', 'AU']):
                gold_symbols.append({
                    'name': symbol.name,
                    'description': symbol.description,
                    'visible': symbol.visible,
                    'select': symbol.select
                })
        
        print(f"\n🥇 Gold-related symbols ({len(gold_symbols)} found):")
        for symbol in gold_symbols:
            status = "✅" if symbol['visible'] and symbol['select'] else "⚠️"
            print(f"   {status} {symbol['name']} - {symbol['description']}")
        
        # Look for forex symbols
        forex_symbols = []
        for symbol in symbols:
            name = symbol.name.upper()
            if any(keyword in name for keyword in ['EUR', 'USD', 'GBP', 'JPY']) and len(symbol.name) <= 10:
                forex_symbols.append({
                    'name': symbol.name,
                    'description': symbol.description,
                    'visible': symbol.visible,
                    'select': symbol.select
                })
        
        print(f"\n💱 Forex symbols (first 10 of {len(forex_symbols)}):")
        for symbol in forex_symbols[:10]:
            status = "✅" if symbol['visible'] and symbol['select'] else "⚠️"
            print(f"   {status} {symbol['name']} - {symbol['description']}")
        
        # Test data retrieval for gold symbols
        print(f"\n📊 Testing data retrieval for gold symbols:")
        for symbol in gold_symbols[:3]:  # Test first 3
            symbol_name = symbol['name']
            try:
                rates = mt5.copy_rates_from_pos(symbol_name, mt5.TIMEFRAME_H1, 0, 10)
                if rates is not None and len(rates) > 0:
                    print(f"   ✅ {symbol_name}: {len(rates)} bars retrieved")
                    print(f"      Latest: {rates[-1][4]:.5f} at {rates[-1][0]}")
                else:
                    print(f"   ❌ {symbol_name}: No data")
            except Exception as e:
                print(f"   ❌ {symbol_name}: Error - {e}")
        
        # Show symbol format patterns
        print(f"\n🔍 Symbol naming patterns:")
        patterns = {}
        for symbol in symbols:
            name = symbol.name
            if '.' in name:
                suffix = name.split('.')[-1]
                if suffix in patterns:
                    patterns[suffix] += 1
                else:
                    patterns[suffix] = 1
        
        for suffix, count in sorted(patterns.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"   .{suffix}: {count} symbols")
        
        mt5.shutdown()
        
    except Exception as e:
        print(f"❌ Error checking MT5 symbols: {e}")

if __name__ == "__main__":
    check_mt5_symbols()
