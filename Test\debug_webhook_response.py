#!/usr/bin/env python3
"""
Debug webhook response to identify JSON serialization issues
"""

import os
import sys
import json
import traceback

# Load environment variables first
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ Environment variables loaded from .env file")
except ImportError:
    # Manual loading
    env_path = os.path.join(os.getcwd(), '.env')
    if os.path.exists(env_path):
        with open(env_path, 'r') as f:
            for line in f:
                if '=' in line and not line.strip().startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
        print("✅ Environment variables loaded manually from .env file")

def test_json_serialization():
    """Test if the AI analysis response can be serialized to JSON"""
    print("🧪 Testing JSON Serialization")
    print("=" * 35)
    
    try:
        from App.config import Config
        from App.util import Util
        
        config = Config()
        util = Util(config)
        
        # Test the exact same parameters from the failing request
        symbol = "XAUUSD"
        timeframes = ["H1"]
        barback = 10  # Small number for testing
        custom_prompt = "Brief analysis"
        ai_provider = "gpt"
        image_url = ""
        use_signal_format = True
        
        print(f"✅ Testing AI analysis response serialization...")
        
        # Step 1: Get chart data
        chart_data_result = util.get_multi_timeframe_data(symbol, timeframes, barback)
        if not chart_data_result:
            print("❌ Chart data failed")
            return False
        
        print("✅ Chart data retrieved")
        
        # Step 2: Perform AI analysis
        analysis_result = util.perform_ai_analysis(
            chart_data_result,
            custom_prompt,
            ai_provider,
            image_url,
            symbol,
            timeframes,
            use_signal_format
        )
        
        if analysis_result.get("error"):
            print(f"⚠️ AI analysis failed: {analysis_result.get('message')}")
            # Test if error response can be serialized
            try:
                json_str = json.dumps(analysis_result, indent=2)
                print("✅ Error response can be serialized to JSON")
                return True
            except Exception as e:
                print(f"❌ Error response JSON serialization failed: {e}")
                return False
        
        print("✅ AI analysis completed")
        
        # Step 3: Test JSON serialization
        try:
            json_str = json.dumps(analysis_result, indent=2)
            print("✅ Success response can be serialized to JSON")
            print(f"   JSON length: {len(json_str)} characters")
            
            # Test specific fields that might cause issues
            problematic_fields = []
            for key, value in analysis_result.items():
                try:
                    json.dumps({key: value})
                except Exception as e:
                    problematic_fields.append((key, str(e)))
            
            if problematic_fields:
                print("❌ Problematic fields found:")
                for field, error in problematic_fields:
                    print(f"   - {field}: {error}")
                return False
            else:
                print("✅ All fields can be serialized")
                return True
                
        except Exception as e:
            print(f"❌ JSON serialization failed: {e}")
            print(f"   Response type: {type(analysis_result)}")
            print(f"   Response keys: {list(analysis_result.keys()) if isinstance(analysis_result, dict) else 'Not a dict'}")
            
            # Try to identify the problematic field
            if isinstance(analysis_result, dict):
                for key, value in analysis_result.items():
                    try:
                        json.dumps(value)
                        print(f"   ✅ {key}: OK")
                    except Exception as field_error:
                        print(f"   ❌ {key}: {field_error}")
            
            return False
            
    except Exception as e:
        print(f"❌ JSON serialization test failed: {e}")
        traceback.print_exc()
        return False

def test_webhook_response_format():
    """Test the exact webhook response format"""
    print("\n🧪 Testing Webhook Response Format")
    print("=" * 40)
    
    try:
        from flask import Flask, jsonify
        import json
        
        # Create a test Flask app
        app = Flask(__name__)
        
        # Test response data (similar to what webhook returns)
        test_response = {
            "error": False,
            "message": "AI analysis completed successfully",
            "symbol": "XAUUSD",
            "timeframes": ["H1"],
            "ai_provider": "GPT",
            "analysis": "Test analysis text",
            "custom_prompt": "Brief analysis",
            "image_analyzed": False,
            "use_signal_format": True,
            "timestamp": "2025-01-01T12:00:00.000000"
        }
        
        print("✅ Testing basic response format...")
        
        # Test 1: Direct JSON serialization
        try:
            json_str = json.dumps(test_response)
            print("✅ Direct JSON serialization works")
        except Exception as e:
            print(f"❌ Direct JSON serialization failed: {e}")
            return False
        
        # Test 2: Flask jsonify
        with app.app_context():
            try:
                flask_response = jsonify(test_response)
                print("✅ Flask jsonify works")
                print(f"   Response type: {type(flask_response)}")
            except Exception as e:
                print(f"❌ Flask jsonify failed: {e}")
                return False
        
        # Test 3: Response with signal data
        test_response_with_signal = test_response.copy()
        test_response_with_signal.update({
            "signal_data": {
                "signal_id": "test123",
                "symbol": "XAUUSD",
                "signal_type": "Buy Limit",
                "entry_price": "3367.23",
                "sl_price": "3360.00",
                "tp1_price": "3375.00",
                "reason": "Test reason"
            },
            "structured_signal": True
        })
        
        try:
            json_str = json.dumps(test_response_with_signal)
            print("✅ Response with signal data serializes correctly")
        except Exception as e:
            print(f"❌ Response with signal data failed: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Webhook response format test failed: {e}")
        return False

def test_actual_webhook_call():
    """Test actual webhook call with detailed error capture"""
    print("\n🧪 Testing Actual Webhook Call")
    print("=" * 35)
    
    try:
        import requests
        
        # The exact data from the failing request
        webhook_data = {
            "symbol": "XAUUSD",
            "timeframes": ["H1"],
            "barback": 10,  # Small number for testing
            "prompt": "Brief analysis",
            "ai": "gpt",
            "image": "",
            "use_signal_format": True
        }
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer test_token",
            "X-Access-Token": "test_access_token"
        }
        
        print("   Sending request to localhost:5050...")
        print(f"   Data: {json.dumps(webhook_data, indent=2)}")
        
        try:
            response = requests.post(
                "http://localhost:5050/webhook_ai_analysis",
                json=webhook_data,
                headers=headers,
                timeout=120
            )
            
            print(f"   Response status: {response.status_code}")
            print(f"   Response headers: {dict(response.headers)}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print("✅ Webhook call successful!")
                    print(f"   Response keys: {list(result.keys())}")
                    print(f"   Error: {result.get('error')}")
                    print(f"   Message: {result.get('message', 'N/A')}")
                    return True
                except json.JSONDecodeError as e:
                    print(f"❌ Response is not valid JSON: {e}")
                    print(f"   Raw response: {response.text[:500]}...")
                    return False
            else:
                print(f"❌ Webhook call failed: {response.status_code}")
                print(f"   Content-Type: {response.headers.get('content-type')}")
                
                if 'application/json' in response.headers.get('content-type', ''):
                    try:
                        error_data = response.json()
                        print(f"   Error data: {error_data}")
                    except:
                        print(f"   Raw response: {response.text}")
                else:
                    print(f"   HTML response: {response.text[:200]}...")
                
                return False
                
        except requests.exceptions.ConnectionError:
            print("❌ Connection failed - webhook server not running?")
            return False
        except requests.exceptions.Timeout:
            print("❌ Request timeout - server taking too long")
            return False
            
    except Exception as e:
        print(f"❌ Webhook call test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run webhook response debugging"""
    print("🔍 Webhook Response Debugging")
    print("=" * 35)
    
    # Run tests
    tests = [
        ("JSON Serialization", test_json_serialization),
        ("Response Format", test_webhook_response_format),
        ("Actual Webhook Call", test_actual_webhook_call)
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            traceback.print_exc()
            results[test_name] = False
    
    # Summary
    print("\n📋 Debug Results:")
    print("=" * 20)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  - {test_name}: {status}")
    
    print("\n💡 Recommendations:")
    if not results.get("JSON Serialization", False):
        print("  🎯 Fix JSON serialization issues in AI analysis response")
    if not results.get("Response Format", False):
        print("  🎯 Fix Flask response formatting")
    if not results.get("Actual Webhook Call", False):
        print("  🎯 Check webhook server logs for detailed error messages")
    
    if all(results.values()):
        print("  🎉 All tests passed - webhook should be working!")
    else:
        print("  ⚠️ Issues found - check the details above")

if __name__ == "__main__":
    main()
