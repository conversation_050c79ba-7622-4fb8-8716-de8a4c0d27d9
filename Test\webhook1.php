<?php
// Turn On PHP Error Reporting
// ini_set('display_errors', 'on');
// error_reporting(-1);

function _array_default($default, &$array)
{
    if (!is_array($array))
        $array = [];
    foreach ($default as $k => $v)
        $array[$k] = isset($array[$k]) ? $array[$k] : $default[$k];
}
function _connect_curl(&$ch, $url, $data = null, $option = [])
{
    _array_default([
        'show_header' => false,
        'show_body' => true,
        'ca_path' => false,
        'force_SSL3' => false,
        'to_file' => false,
        'test' => false,
        'method' => "POST",
        'cookies' => '',
    ], $option);

    // $cookies_file = storage_path('cookies.txt');
    $uriParts = parse_url($url);
    $header = array();
    $header[0] = "Accept: text/xml,application/xml,application/xhtml+xml,";
    $header[0] .= "text/html;q=0.9,text/plain;q=0.8,image/png,";
    $header[0] .= "application/json,text/javascript,";
    $header[0] .= "*/*;q=0.01";
    $header[] = "Cache-Control: max-age=0";
    // $header[] = "Expires: " . gmdate('D, d M Y H:i:s \G\M\T', time() + 31536000);
    // $header[] = "Connection: keep-alive";
    $header[] = "Connection: close";
    // $header[] = "Keep-Alive: 3600";
    // $header[] = "Accept-Encoding: gzip, deflate, br"; 
    $header[] = "Accept-Charset: ISO-8859-1,tis-620,utf-8;q=0.7,*;q=0.7";
    $header[] = "Accept-Language: en-us,en,th-TH,th;q=0.5";
    $header[] = "Pragma: no-cache";
    $header[] = "Host: " .  $uriParts['host'];
    if ($option['cookies'] == '') {
        $cookie_header = "Cookie:";
        foreach ($_COOKIE as $key => $val) {
            $cookie_header .= " " . $key . "=" . $val . ";";
        }
        $header[] = $cookie_header . ' path=/; ';
    }
    if (is_array($option['headers'])) {
        foreach ($option['headers'] as $key => $val) {
            $header[] =  ucfirst(strtolower($key)) . ": " . $val;
        }
    }
    // $header[] = "REMOTE_ADDR: " . $_SERVER['REMOTE_ADDR'];
    // $header[] = "HTTP_X_FORWARDED_FOR: " . $_SERVER['REMOTE_ADDR'];

    if (is_array($data)) {
        $requestBody = http_build_query($data, '', '&');
    } else
        $requestBody = $data;

    if (!$ch)
        $ch = curl_init();

    if ($option['referer']) {
        curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        if ($option['referer'] == 'auto')
            curl_setopt($ch, CURLOPT_REFERER, 'http://' . $_SERVER['SERVER_NAME'] . $_SERVER['REQUEST_URI']);
        else
            curl_setopt($ch, CURLOPT_REFERER, $option['referer']);
    }
    //  enable it if need to debug
    if ($option['test'])
        curl_setopt($ch, CURLOPT_VERBOSE, 1);

    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);

    if ($option['method'] === 'POST' || $option['method'] === 'PUT') { 
        if ($option['method'] === 'PUT') {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
        } else {
            curl_setopt($ch, CURLOPT_POST, true);
        }

        curl_setopt($ch, CURLOPT_POSTFIELDS, $requestBody);
    } else {
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $option['method']);
        if ($requestBody !== "")
            $url .= (strpos($requestBody, '?') === -1 || strpos($requestBody, '?') === false  ? '?' : '&') . $requestBody;
    }

    curl_setopt($ch, CURLOPT_URL, $url);
    // curl_setopt($ch, CURLOPT_PORT, $port);
    curl_setopt($ch, CURLOPT_COOKIESESSION, 1);
    curl_setopt($ch, CURLOPT_COOKIE, $option['cookies'] . ' user=zd; activity=reading');
    // curl_setopt($ch, CURLOPT_COOKIEFILE, $cookies_file);
    // curl_setopt($ch, CURLOPT_COOKIEJAR, $cookies_file);

    curl_setopt($ch, CURLOPT_TIMEOUT, 360000);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 360000);

    curl_setopt($ch, CURLOPT_MAXREDIRS, 100);
    curl_setopt($ch, CURLOPT_ENCODING, '');
    curl_setopt($ch, CURLOPT_NOBODY, !$option['show_body']);
    curl_setopt($ch, CURLOPT_FAILONERROR, 1);
    if ($option['force_SSL3']) {
        curl_setopt($ch, CURLOPT_SSLVERSION, 3);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, !!$option['ca_path']);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, !!$option['ca_path']);
        curl_setopt($ch, CURLOPT_CAINFO, $option['ca_path']);
    }
    if ($option['to_file']) {
        curl_setopt($ch, CURLOPT_PUT, true); // without CURLOPT_PUT, CURLOPT_INFILE is ignored.
        curl_setopt($ch, CURLOPT_FILE, $option['to_file']);
    }
    curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
    curl_setopt($ch, CURLOPT_HEADER, !!$option['show_header']);


    if ($option['test'])
        var_dump("header", $header, "url", $url, "requestBody", $requestBody, "curl_exec", curl_exec($ch), "curl_error", curl_error($ch));

    if (curl_error($ch))
        return curl_error($ch);

    return curl_exec($ch);
    //        $exec = curl_exec($ch);
    //        curl_close($ch);
    //        return $exec;
}

// $json = '{
//     "s": "XU",
//     "a": "Sell Limit",
//     "p": 3310.01,
//     "c": "TEST_Comment"
// }';
// $data = [
//     "s"=> "XU",
//     "a"=> "Sell Limit",
//     "p"=> 3310.01,
//     "c"=> "TEST_Comment"
// ]
// $json = json_decode($data, true);
// echo http_build_query($_POST, '', '&');;
// var_dump($data);
// exit(0);

$json = file_get_contents('php://input');
try {
    $ch = curl_init(); 
    echo _connect_curl($ch, 'http://61.19.18.99:2249/webhook', $json, [ 
    // echo _connect_curl($ch, 'https://mysql.suksapanpanit.com', $data, [ 
        'method' => 'POST',
        'referer' => 'auto',
        'test' => false,
        'headers' => [
            'Content-Type' => 'application/json',
            'Content-Length' => strlen($json)
        ]
    ]); 
    exit(0);
} catch (Exception $e) {
    var_dump($e->getMessage());
    exit(0);
}

// Test with 
// sestatus
// setsebool -P httpd_can_network_connect 1
// curl -v -A "Mozilla/5.0" -H "Accept: */*" -X POST https://teaching.suksapanpanit.com/extra/playground/test1.php -d '{"hello":"world"}' -H "Content-Type: application/json"
// curl -v -A "Mozilla/5.0" -H "Accept: */*" -X POST http://61.19.18.99:2249/webhook -d '{"hello":"world"}' -H "Content-Type: application/json"
// /opt/plesk/php/8.3/bin/php  -r '$ch = curl_init("http://61.19.18.99:2249/webhook"); curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); $res = curl_exec($ch); if (curl_errno($ch)) { echo curl_error($ch); } else { echo $res; } curl_close($ch);'