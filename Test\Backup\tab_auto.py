
import MetaTrader5 as mt5
import customtkinter as ctk
import pandas as pd
import numpy as np
import talib as ta
import threading
import time
from datetime import datetime
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import gc

# ===========================
# Class: TabAuto
# ===========================
class TabAuto:
    def __init__(self, master, config, util):
        self.frame = master.add("Auto")
        self.config = config
        self.util = util
        self.name = "A2"
        self.side_var = ctk.StringVar()
        self.symbol_var = ctk.StringVar()
        self.lot_var = ctk.DoubleVar()
        # self.point_bsl_var = ctk.IntVar(value=self.config.SL_POINTS)
        # self.point_btp_var = ctk.IntVar(value=self.config.TP1_POINTS)
        # self.point_ssl_var = ctk.IntVar(value=self.config.SL_POINTS)
        # self.point_stp_var = ctk.IntVar(value=self.config.TP1_POINTS)
        self.time_var = ctk.IntVar()
        self.checkbox_tf_vars = {}
        self.tf_count = {"All":0, "All_FT":0, "All_TF":0}
        self.last_htf_time = {}
        self.last_htf_text = {}
        self.count_orders = 0
        self.loop_running = False
        self.auto_be_enabled = False
        self.thread1 = None
        self.thread2 = None

        self.form1 = ctk.CTkFrame(self.frame)
        self.form1.pack(pady=10) 
        self.form2 = ctk.CTkFrame(self.frame)
        self.form2.pack(pady=10, padx=20) 
        self.form3 = ctk.CTkFrame(self.frame)
        self.form3.pack(pady=10) 

        self.build_ui()

    def build_ui(self):
        self.lot_var.set(0.02)  # Default lot size
        self.lot_label = ctk.CTkLabel(self.form1, text="L Size:")
        self.lot_label.grid(row=1, column=0, padx=10, pady=5)
        self.lot_val = ctk.CTkEntry(self.form1, textvariable=self.lot_var)
        self.lot_val.grid(row=1, column=1, padx=10, pady=5)

        self.symbol_label = ctk.CTkLabel(self.form1, text="Symbol:")
        self.symbol_label.grid(row=1, column=2, padx=10, pady=5)
        self.symbol_var.set("XU")  # Default symbol
        self.symbol_dropdown = ctk.CTkOptionMenu(self.form1, values=list(self.config.symbols), variable=self.symbol_var)
        self.symbol_dropdown.grid(row=1, column=3, padx=10, pady=5)
        
        self.time_var.set(1)  # Default 5 min
        self.time_label = ctk.CTkLabel(self.form1, text="Loop (min):")
        self.time_label.grid(row=2, column=0, padx=10, pady=5)
        self.time_val = ctk.CTkEntry(self.form1, textvariable=self.time_var)
        self.time_val.grid(row=2, column=1, padx=10, pady=5)

        self.side_label = ctk.CTkLabel(self.form1, text="Side:")
        self.side_label.grid(row=2, column=2, padx=10, pady=5)
        self.side_var.set("BUY")  # Default symbol
        self.side_dropdown = ctk.CTkOptionMenu(self.form1, values=["BUY","SELL","BOTH"], variable=self.side_var)
        self.side_dropdown.grid(row=2, column=3, padx=10, pady=5)
 
        # self.point_bsl_var.set(self.config.SL_POINTS*3)  # Default sl size
        # self.point_bsl_label = ctk.CTkLabel(self.form1, text="B.SL Point:")
        # self.point_bsl_label.grid(row=3, column=0, padx=10, pady=5)
        # self.point_bsl_val = ctk.CTkEntry(self.form1, textvariable=self.point_bsl_var)
        # self.point_bsl_val.grid(row=3, column=1, padx=10, pady=5)

        # self.point_btp_var.set(self.config.TP3_POINTS)  # Default tp size
        # self.point_btp_label = ctk.CTkLabel(self.form1, text="B.TP Point:")
        # self.point_btp_label.grid(row=3, column=2, padx=10, pady=5)
        # self.point_btp_val = ctk.CTkEntry(self.form1, textvariable=self.point_btp_var)
        # self.point_btp_val.grid(row=3, column=3, padx=10, pady=5)

        # self.point_ssl_var.set(self.config.SL_POINTS)  # Default sl size
        # self.point_ssl_label = ctk.CTkLabel(self.form1, text="S.SL Point:")
        # self.point_ssl_label.grid(row=4, column=0, padx=10, pady=5)
        # self.point_ssl_val = ctk.CTkEntry(self.form1, textvariable=self.point_ssl_var)
        # self.point_ssl_val.grid(row=4, column=1, padx=10, pady=5)

        # self.point_stp_var.set(self.config.TP1_POINTS)  # Default tp size
        # self.point_stp_label = ctk.CTkLabel(self.form1, text="S.TP Point:")
        # self.point_stp_label.grid(row=4, column=2, padx=10, pady=5)
        # self.point_stp_val = ctk.CTkEntry(self.form1, textvariable=self.point_stp_var)
        # self.point_stp_val.grid(row=4, column=3, padx=10, pady=5)

        # ctk.CTkLabel(self.form2, text="🕒 Select Timeframes:").grid(row=0, column=0, columnspan=6, padx=10, pady=(0, 5), sticky="w")
 
        self.status_label = ctk.CTkLabel(self.form3, text="🔘 Not Monitoring")
        self.status_label.pack(pady=1)
        self.status_label_0_tf = {}
        self.status_label_1_tf = {}
        self.status_label_2_tf = {}
        self.status_label_3_tf = {}
        self.status_label_4_tf = {}
        self.status_label_5_tf = {}
        self.chart_btn = {}
        font = ("Arial", 10)
        pady = 2
        for i, tf_name in enumerate(self.config.timeframes):
            var = ctk.BooleanVar(value=True if tf_name == "M5" or tf_name == "M15" else False)
            checkbox = ctk.CTkCheckBox(self.form2, text=tf_name, variable=var)
            checkbox.grid(row=0, column=i, padx=0, pady=pady, sticky="w")
            self.tf_count[tf_name] = {"B":0, "S":0}
            self.checkbox_tf_vars[tf_name] = var
            self.status_label_0_tf[tf_name] = ctk.CTkLabel(self.form2, text="", font=font)
            self.status_label_0_tf[tf_name].grid(row=1, column=i, padx=0, pady=pady, sticky="w")
            self.status_label_1_tf[tf_name] = ctk.CTkLabel(self.form2, text="", font=font)
            self.status_label_1_tf[tf_name].grid(row=2, column=i, padx=0, pady=pady, sticky="w")
            # self.status_label_2_tf[tf_name] = ctk.CTkLabel(self.form2, text="", font=font)
            # self.status_label_2_tf[tf_name].grid(row=3, column=i, padx=0, pady=pady, sticky="w")
            # self.status_label_3_tf[tf_name] = ctk.CTkLabel(self.form2, text="", font=font)
            # self.status_label_3_tf[tf_name].grid(row=4, column=i, padx=0, pady=pady, sticky="w")
            # self.status_label_4_tf[tf_name] = ctk.CTkLabel(self.form2, text="", font=font)
            # self.status_label_4_tf[tf_name].grid(row=5, column=i, padx=0, pady=pady, sticky="w")
            # self.status_label_5_tf[tf_name] = ctk.CTkLabel(self.form2, text="", font=font)
            # self.status_label_5_tf[tf_name].grid(row=6, column=i, padx=0, pady=pady, sticky="w")
            self.last_htf_time[tf_name] = None
            self.last_htf_text[tf_name] = None

        self.chart_btn["M5"]  = ctk.CTkButton(self.form2, text="M5",  fg_color="orange", command=lambda: self.draw_charts("M5"),  width=10).grid(row=7, column=0, padx=0, pady=pady, sticky="w")
        self.chart_btn["M15"] = ctk.CTkButton(self.form2, text="M15", fg_color="orange", command=lambda: self.draw_charts("M15"), width=10).grid(row=7, column=1, padx=0, pady=pady, sticky="w")
        self.chart_btn["M30"] = ctk.CTkButton(self.form2, text="M30", fg_color="orange", command=lambda: self.draw_charts("M30"), width=10).grid(row=7, column=2, padx=0, pady=pady, sticky="w")
        self.chart_btn["H1"]  = ctk.CTkButton(self.form2, text="H1",  fg_color="orange", command=lambda: self.draw_charts("H1"),  width=10).grid(row=7, column=3, padx=0, pady=pady, sticky="w")
        self.chart_btn["H4"]  = ctk.CTkButton(self.form2, text="H4",  fg_color="orange", command=lambda: self.draw_charts("H4"),  width=10).grid(row=7, column=4, padx=0, pady=pady, sticky="w")
        
        stop_btn = ctk.CTkButton(self.frame, text="⏹ Stop", fg_color="red", command=self.stop_loop)
        stop_btn.pack(side="left", pady=10)
        
        start_btn = ctk.CTkButton(self.frame, text="▶ Start", fg_color="green", command=self.start_loop)
        start_btn.pack(side="right", pady=10)

    def get_selected_timeframes(self):
        selected = [self.config.timeframes[name] for name, var in self.checkbox_tf_vars.items() if var.get()]
        # selected = [self.config.timeframes[name] for name, var in self.checkbox_tf_vars if var]
        print("✅ Selected Timeframes:", selected)
        return selected
    
    def start_loop(self):
        if not self.loop_running:
            self.loop_running = True 
            self.thread1 = threading.Thread(target=self.checking_loop, daemon=True)
            self.thread1.start()
            self.thread2 = threading.Thread(target=self.compute_loop, daemon=True)
            self.thread2.start()
        self.util.add_status_frame(f"🟢 Monitoring {self.side_var.get()} side started {self.symbol_var.get()}")
        self.status_label.configure(text=f"🟢 Monitoring {self.side_var.get()} side started {self.symbol_var.get()}")

    def stop_loop(self):
        self.loop_running = False
        self.util.add_status_frame(f"🔴 Monitoring {self.side_var.get()} side stopped {self.symbol_var.get()}")
        self.status_label.configure(text=f"🔴 Monitoring {self.side_var.get()} side stopped {self.symbol_var.get()}")

    def checking_loop(self):
        try:
            symbol = self.util.get_symbol(self.symbol_var)
            i = 0 # Loop ตรวจสอบ
            while self.loop_running and self.auto_be_enabled:
                if self.util.is_in_restricted_time():
                    self.util.set_status_label(f"{time.strftime('%H:%M:%S')} ⏳ Restricted time (21:00 - 22:00 UTC). Skipping...", "yellow")
                    time.sleep(60*60) # 1 hour
                else:
                    i += 1
                    self.tf_count = self.util.update_SL_to_BE_by_point(symbol, "", False) 
                    # self.tf_count = self.util.positions_count(symbol, self.name+"_") 
                    self.util.set_status_label(f"{time.strftime('%H:%M:%S')} 🕒 Checking {i}: curently {self.tf_count['All_FT']} orders", "yellow")
                    # time.sleep(self.time_var.get()*60)  # 60 sec = 1 min /  300 sec = 5 mins
                    time.sleep(60)  # 60 sec = 1 min /  300 sec = 5 mins
        except KeyboardInterrupt:
            print("Stopped by user")
        # finally:
        #     mt5.shutdown()
    
    def compute_loop(self):
        # while self.loop_running: 
        try:

            symbol = self.util.get_symbol(self.symbol_var)
            lot = self.lot_var.get()
            i = 0 # Loop ตรวจสอบ
            while self.loop_running:
                i += 1 
                if  not self.auto_be_enabled:
                    self.tf_count = self.util.positions_count(symbol, self.name+"_") 
                    self.util.set_status_label(f"{time.strftime('%H:%M:%S')} 🕒 Checking {i}: curently {self.tf_count['All_FT']} orders", "yellow")

                if self.util.is_in_restricted_time():
                    # self.util.set_status_label(f"{time.strftime('%H:%M:%S')} ⏳ Restricted time (21:00 - 22:00 UTC). Skipping...", "yellow")
                    time.sleep(60*60) # 1 hour
                else:
                    # self.util.set_status_label(f"{time.strftime('%H:%M:%S')} 🕒 Checking {i}: curently {self.count_orders} orders", "yellow")
                    for tf_name, checked_tf in self.checkbox_tf_vars.items():
                        if checked_tf.get(): 
                            list_AHTF = self.compute_tf(symbol, tf_name)
                            
                            if list_AHTF.empty:
                                continue

                            src = list_AHTF['close']
                            ema1 = ta.EMA(src, timeperiod=9) 
                            ema2 = ta.EMA(src, timeperiod=20) 
                            ema3 = ta.EMA(src, timeperiod=50) 
                            ema4 = ta.EMA(src, timeperiod=100) 
                            # ema5 = ta.EMA(src, timeperiod=200) 
                            
                            self.compute_ema_retest(list_AHTF, symbol, 0, lot, 3, 1, tf_name, ema1, tf_name+"_E1")
                            self.compute_ema_retest(list_AHTF, symbol, 0, lot, 3, 1, tf_name, ema2, tf_name+"_E2")
                            self.compute_ema_retest(list_AHTF, symbol, 0, lot, 3, 1, tf_name, ema3, tf_name+"_E3")
                            self.compute_ema_retest(list_AHTF, symbol, 0, lot, 3, 1, tf_name, ema4, tf_name+"_E4")
                            # e5 = self.compute_ema_retest(list_AHTF, symbol, lot, 6, 3, tf_name, ema5, tf_name+"_E5")
                    
                            self.status_label_1_tf[tf_name].configure(text=('▲' if list_AHTF['rsmaTrend'].iloc[-1] > 0 else '▼'), text_color=('green' if list_AHTF['rsmaTrend'].iloc[-1] > 0 else 'red'), font=("Arial", 30)) 
                            
                    self.status_label.configure(text=f"🟢 Monitoring {self.side_var.get()} side started {self.symbol_var.get()} {time.strftime('%H:%M:%S')} 🕒 Checking indicators {i} time(s)")

                    if i % 100 == 0:  # เคลียร์ทุก 100 รอบ
                        gc.collect()

                    # time.sleep(self.config.TIMEFRAME1)  # 60 sec = 1 min /  300 sec = 5 mins
                    time.sleep(self.time_var.get()*60)  # 60 sec = 1 min /  300 sec = 5 mins
        except KeyboardInterrupt:
            print("Stopped by user")
        # finally:
        #     mt5.shutdown()

    def compute_tf(self, symbol, tf_name):
        
        current_time = datetime.now()

        result = {}
        # Set time range
        rates_AHTF = mt5.copy_rates_from(symbol, self.config.timeframes[tf_name], current_time, 1000)
        df_AHTF = pd.DataFrame(rates_AHTF)
        df_AHTF['time'] = pd.to_datetime(df_AHTF['time'], unit='s').dt.tz_localize('UTC')
        
        rates_BHTF = mt5.copy_rates_from(symbol, self.config.timeframes_map_htf[tf_name], current_time, 1000)
        df_BHTF = pd.DataFrame(rates_BHTF)
        df_BHTF['time'] = pd.to_datetime(df_BHTF['time'], unit='s').dt.tz_localize('UTC')
        self.compute_rsma_trend(df_BHTF)

        self.status_label_0_tf[tf_name].configure(text=f"📦 {self.tf_count[tf_name]['B']} / {self.tf_count[tf_name]['S']}\n{df_AHTF['time'].iloc[-1].strftime('%H:%M')}", text_color="yellow")
            
        df_TEMP = df_BHTF[['time', 'rsmaTrend', 'colorTrend']].copy()
        df_TEMP = df_TEMP.rename(columns={'time': 'time_BHTF'})

        list_AHTF = pd.merge_asof(
            df_AHTF.sort_values('time'),
            df_TEMP.sort_values('time_BHTF'),
            left_on='time',
            right_on='time_BHTF',
            direction='backward'
        )
        cur_AHTF  = list_AHTF.iloc[-1] 
 
        # กันสัญญาณซ้ำ
        if cur_AHTF['time'] == self.last_htf_time[tf_name]:
            result["message"] = f"{current_time} - {self.last_htf_text[tf_name]} ⏳ ยังเป็นแท่ง HTF เดิม ข้าม {tf_name}"
            print(result["message"])
            time.sleep(60)
            return pd.DataFrame()

        self.last_htf_time[tf_name] = cur_AHTF['time']
        self.last_htf_text[tf_name] = self.last_htf_time[tf_name].strftime('%H:%M')

        list_AHTF['bodySize'] = abs(list_AHTF['open'] - list_AHTF['close'])
        list_AHTF['isBull'] = list_AHTF['close'] > list_AHTF['open']
        return list_AHTF
        
    def draw_charts(self, tf_name):
        print("draw_charts "+tf_name) 
        symbol = self.util.get_symbol(self.symbol_var)
        list_AHTF = self.compute_tf(symbol, tf_name)

        if list_AHTF.empty:
            return

        src = list_AHTF['close']
        ema1 = ta.EMA(src, timeperiod=9) 
        ema2 = ta.EMA(src, timeperiod=20) 
        ema3 = ta.EMA(src, timeperiod=50) 
        ema4 = ta.EMA(src, timeperiod=100) 
        ema5 = ta.EMA(src, timeperiod=200) 

        plt.style.use('dark_background')  # <- ธีมดำพร้อมใช้
        fig, ax = plt.subplots(figsize=(14, 6), dpi=72)
        a_alpha = 0.9
        ax.plot(list_AHTF['time'], ema1, label=f'EMA 9',   color='yellow', alpha=a_alpha)
        ax.plot(list_AHTF['time'], ema2, label=f'EMA 20',  color='orange', alpha=a_alpha)
        ax.plot(list_AHTF['time'], ema3, label=f'EMA 50',  color='red',    alpha=a_alpha)
        ax.plot(list_AHTF['time'], ema4, label=f'EMA 100', color='aqua',   alpha=a_alpha)
        ax.plot(list_AHTF['time'], ema5, label=f'EMA 200', color='fuchsia', alpha=a_alpha)

        # --- Trend bar (fixed height above chart) ---
        # Create a second axis sharing the same x-axis but independent y-axis
        ax2 = ax.twinx()
        bar_height = 0.1  # Just a small visual block
        bar_bottom = 1.90 #list_AHTF['close'].max() * 0.02 # Slightly above the top y-limit of main chart

        # Convert dates to matplotlib format
        # valid_dates = [d for d in list_AHTF["time"] if pd.notna(d)]
        x_dates = mdates.date2num(list_AHTF["time"])  


        # Hide secondary y-axis
        for i in range(len(list_AHTF)):
            t = list_AHTF.iloc[i]
            color = 'lime' if t['close'] >= t['open'] else 'red'
            ax.plot([t['time'], t['time']], [t['low'], t['high']], color=color)
            ax.plot([t['time'], t['time']], [t['open'], t['close']], color=color, linewidth=5) 
            
            e1 = self.compute_ema_retest(list_AHTF, symbol, i, 0, 3, 1, tf_name, ema1, tf_name+"_E1")
            self.draw_signal(ax, t['time'], t['close'], e1, "yellow")
            e2 = self.compute_ema_retest(list_AHTF, symbol, i, 0, 3, 1, tf_name, ema2, tf_name+"_E2")
            self.draw_signal(ax, t['time'], t['close'], e2, "orange")
            e3 = self.compute_ema_retest(list_AHTF, symbol, i, 0, 3, 1, tf_name, ema3, tf_name+"_E3")
            self.draw_signal(ax, t['time'], t['close'], e3, "red")
            e4 = self.compute_ema_retest(list_AHTF, symbol, i, 0, 3, 1, tf_name, ema4, tf_name+"_E4")
            self.draw_signal(ax, t['time'], t['close'], e4, "blue")

            if i != 0:
                ax2.fill_between(
                    [x_dates[i-1], x_dates[i]],
                    bar_bottom,
                    bar_bottom + bar_height,
                    # color=t["colorTrend"],
                    color=list_AHTF.at[i, "colorTrend"],
                    linewidth=0
                )
                
        ax2.get_yaxis().set_visible(False)
        ax2.set_ylim(0, 2)  # Enough height for fixed bar

        fig.autofmt_xdate(rotation=45)
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M\n%d %b')) # ตัวอย่าง: 15:30\n22 Apr
        ax.set_title(f'{tf_name} Chart with EMA and Signal Arrows')
        ax.set_ylabel("Price")
        ax.legend(loc='lower right')
        plt.grid(True, "both", "both")
        plt.tight_layout()
        plt.show()

    def draw_signal(self, ax, time, price, sig_type, color):
        if sig_type == 'long':
            ax.annotate(f'▲', xy=(time, price), xytext=(0, -45), textcoords='offset points',
                        ha='center', color=color, fontsize=20)
            ax.annotate(time.strftime('%H:%M'), xy=(time, price), xytext=(0, -65), textcoords='offset points',
                        ha='center', color=color, fontsize=10)
        elif sig_type =='short':
            ax.annotate(time.strftime('%H:%M'), xy=(time, price), xytext=(0, 65), textcoords='offset points',
                        ha='center', color=color, fontsize=10)
            ax.annotate(f'▼', xy=(time, price), xytext=(0, 45), textcoords='offset points',
                        ha='center', color=color, fontsize=20)
            
    def compute_rsma_trend(self, df_BHTF):
        df_BHTF['rsi_1'] = ta.RSI(df_BHTF['close'], timeperiod=25)
        df_BHTF['sma_1'] = ta.SMA(df_BHTF['rsi_1'], timeperiod=50)
        
        df_BHTF['rsi_prev_1'] = df_BHTF['rsi_1'].shift(1)
        df_BHTF['sma_prev_1'] = df_BHTF['sma_1'].shift(1)

        df_BHTF['rsi_2'] = ta.RSI(df_BHTF['close'], timeperiod=50)
        df_BHTF['sma_2'] = ta.SMA(df_BHTF['rsi_2'], timeperiod=25)
        
        df_BHTF['rsi_prev_2'] = df_BHTF['rsi_2'].shift(1)
        df_BHTF['sma_prev_2'] = df_BHTF['sma_2'].shift(1)
    
        df_BHTF['rst_1'] = ((df_BHTF['rsi_prev_1'] > df_BHTF['sma_prev_1']) & (df_BHTF['rsi_1'] > df_BHTF['sma_1']))
        df_BHTF['rst_2'] = ((df_BHTF['rsi_prev_2'] > df_BHTF['sma_prev_2']) & (df_BHTF['rsi_2'] > df_BHTF['sma_2']))

        df_BHTF['rst_bull'] = (df_BHTF['rst_1'] & df_BHTF['rst_2']) 
        df_BHTF['rst_bear'] = (~df_BHTF['rst_1'] & ~df_BHTF['rst_2']) 

        df_BHTF['rsmaTrend'] = np.select(
            [df_BHTF['rst_bull'], df_BHTF['rst_bear']],
            [1, -1],
            default=0
        )
        df_BHTF['colorTrend'] = np.select(
            [df_BHTF['rst_bull'], df_BHTF['rst_bear']],
            ["green", "red"],
            default="white"
        ) 

    def compute_ema_retest(self, df_AHTF, symbol, i, lot, multiplyTP, multiplySL, tf_name, emaRetest, comment):
        order_type = "NO" 
        count = self.tf_count[tf_name]
        point = mt5.symbol_info(symbol).point
        start_tpsl = self.config.timeframes_start_point[tf_name] * point

        df_AHTF['emaRetest'] = emaRetest # ta.EMA(df_AHTF['close'], timeperiod=emaRetest)
        df_AHTF['longRetest'] = False
        df_AHTF['shortRetest'] = False

        # n_AHTF  = df_AHTF.iloc[-1] 
        # cur_AHTF  = df_AHTF.iloc[-2] 
        # prev_AHTF = df_AHTF.iloc[-3]

        n_AHTF = df_AHTF.iloc[i-1]
        cur_AHTF = df_AHTF.iloc[i-2]
        prev_AHTF = df_AHTF.iloc[i-3]

        signal = None
        # signals = []
        # longRetest  = False
        # shortRetest = False 

        prevIsOverlapLong = (prev_AHTF['low'] <= prev_AHTF['emaRetest']) and (prev_AHTF['close'] >= prev_AHTF['emaRetest']) and (prev_AHTF['close'] - prev_AHTF['low']) > prev_AHTF['bodySize']
        curIsOverlapLong  = ( cur_AHTF['low'] <=  cur_AHTF['emaRetest']) and (  cur_AHTF['open'] >=  cur_AHTF['emaRetest']) and (  cur_AHTF['open'] -  cur_AHTF['low']) >  cur_AHTF['bodySize']

        prevIsOverlapShort = (prev_AHTF['high'] >= prev_AHTF['emaRetest']) and (prev_AHTF['close'] <= prev_AHTF['emaRetest']) and (prev_AHTF['high'] - prev_AHTF['close']) > prev_AHTF['bodySize']
        curIsOverlapShort  = ( cur_AHTF['high'] >=  cur_AHTF['emaRetest']) and (  cur_AHTF['open'] <=  cur_AHTF['emaRetest']) and ( cur_AHTF['high'] -   cur_AHTF['open']) >  cur_AHTF['bodySize']
 
        # ---- Long ----
        if (
            (prevIsOverlapLong or curIsOverlapLong) 
            and cur_AHTF['isBull'] 
            and not prev_AHTF['isBull'] 
            and n_AHTF['low'] > cur_AHTF['low']
            and n_AHTF['rsmaTrend'] > 0
        ):
            df_AHTF.at[-1, 'longRetest']  = True
            signal = "long" # f"📈 Long Retest Signal {comment} @ {cur_AHTF['time']}"

        # ---- Short ----
        elif (
            (prevIsOverlapShort or curIsOverlapShort) 
            and not cur_AHTF['isBull'] 
            and prev_AHTF['isBull'] 
            and n_AHTF['high'] < cur_AHTF['high']
            and n_AHTF['rsmaTrend'] < 0
        ): 
            df_AHTF.at[-1, 'shortRetest']  = True
            signal = "short" # f"📉 Short Retest Signal {comment} @ {cur_AHTF['time']}"
 
        if n_AHTF['longRetest']:
            entry = mt5.symbol_info_tick(symbol).ask
            sl = entry - (start_tpsl*multiplySL)
            tp = entry + (start_tpsl*multiplyTP)
            order_type = "Buy Now"
            if (self.side_var.get() == "BUY" or self.side_var.get() == "BOTH") and (count['B'] < self.config.MAX_ORDERS) and lot != 0:
                self.util.close_orders_by_condition(symbol, "all-sell")
                self.util.send_order(order_type, symbol, lot, entry, sl, tp, self.name+"_"+comment)

        elif n_AHTF['shortRetest']:
            entry = mt5.symbol_info_tick(symbol).bid
            sl =  entry + (start_tpsl*multiplySL)
            tp =  entry - (start_tpsl*multiplyTP)
            order_type = "Sell Now"
            if (self.side_var.get() == "SELL" or self.side_var.get() == "BOTH") and (count['S'] < self.config.MAX_ORDERS) and lot != 0:
                self.util.close_orders_by_condition(symbol, "all-buy")
                self.util.send_order(order_type, symbol, lot, entry, sl, tp, self.name+"_"+comment)
        # else:
        #     print(f"✅ ไม่มีสัญญาณใหม่ @ {cur_AHTF['time']} {comment}")
 
        return signal
    
        