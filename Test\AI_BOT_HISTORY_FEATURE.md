# AI Bot History Feature

## Overview

The AI Bot History feature provides comprehensive tracking and analysis of AI bot performance over time. It includes pagination, performance summaries, and detailed analysis viewing capabilities.

## Features

### 📊 Performance Summary Dashboard
- **Total Analyses**: Count of all AI analyses performed
- **TP/SL Signals**: Count of Take Profit and Stop Loss signals
- **Monthly Profit**: Profit points for current month
- **Yearly Profit**: Profit points for current year  
- **Win Rate**: Percentage of successful signals

### 📋 Paginated History View
- **10 items per page** with navigation controls
- **Chronological ordering** (newest first)
- **Signal type color coding** (BUY=green, SELL=red, HOLD=gray)
- **Detailed signal information** (Entry, TP, SL prices)
- **Analysis preview** with full text viewing

### 🔄 Smart Caching & Updates
- **Automatic cache management** for performance
- **Real-time updates** when schedule check is enabled
- **Manual refresh** capability
- **Summary recalculation** on data changes

## How to Use

### 1. Access History
1. Navigate to **AI Bots** tab
2. Find your bot in the list
3. Click **📊 History** button
4. History popup window opens

### 2. Navigate History
- Use **◀ Previous** / **Next ▶** buttons for pagination
- View **Page X of Y** information
- Click **🔄 Refresh** to update data
- Click **👁️ View Details** for full analysis

### 3. Performance Tracking
- **Summary updates automatically** when schedule check is enabled
- **Monthly/Yearly tracking** for profit analysis
- **Win rate calculation** based on TP vs total signals
- **Signal type distribution** (BUY/SELL/HOLD counts)

## Technical Implementation

### Data Sources
```
Logs/Signals/{Bot_Name}_{YYYYMMDD_HHMMSS}.json
```

### File Structure
```json
{
  "error": false,
  "message": "AI analysis completed successfully",
  "symbol": "XAUUSD.iux",
  "timeframes": ["H1"],
  "ai_provider": "GPT",
  "analysis": "Market analysis text...",
  "timestamp": "2025-01-01T12:00:00",
  "signal_data": {
    "signal_type": "BUY",
    "entry_price": 2650.00,
    "tp_price": 2680.00,
    "sl_price": 2630.00,
    "reason": "Analysis reasoning..."
  }
}
```

### Performance Metrics

#### Profit Calculation
- **BUY signals**: `profit_points = tp_price - entry_price`
- **SELL signals**: `profit_points = entry_price - tp_price`
- **Monthly aggregation**: Sum by `YYYY-MM` format
- **Yearly aggregation**: Sum by `YYYY` format

#### Win Rate Formula
```python
win_rate = (tp_count / total_analyses) * 100
```

#### Signal Classification
- **TP Count**: Signals with valid TP prices
- **SL Count**: Signals with valid SL prices  
- **Hold Count**: HOLD/WAIT signals
- **Total**: All analysis entries

## Schedule Check Integration

### Automatic Updates
When **schedule check is enabled**:
- Summary updates after each scheduled analysis
- Cache invalidation for real-time data
- Performance metrics recalculation
- No manual intervention required

### Manual Mode
When **schedule check is disabled**:
- Summary calculated on-demand
- Cache persists until manual refresh
- No automatic updates
- User controls data refresh timing

## Testing the Feature

### 1. Generate Sample Data
```bash
python test_bot_history.py
```
This creates 50 sample history entries over 90 days.

### 2. Create Test Bot
1. Add new bot named "My XAUUSD Bot"
2. Configure with XAUUSD symbol
3. Enable/disable schedule check as needed

### 3. View History
1. Click **📊 History** button
2. Navigate through pages
3. View performance summary
4. Check detailed analyses

## Performance Considerations

### Caching Strategy
- **History data cached** per bot ID
- **Summary data cached** separately
- **Cache invalidation** on data changes
- **Memory efficient** pagination

### File System Optimization
- **Glob pattern matching** for bot files
- **Lazy loading** of file contents
- **Error handling** for corrupted files
- **UTF-8 encoding** support

## Future Enhancements

### Planned Features
- **Export to CSV/Excel** functionality
- **Advanced filtering** (date range, signal type)
- **Performance charts** and visualizations
- **Comparison between bots**
- **Backup/restore** capabilities

### Integration Opportunities
- **Trading journal** integration
- **Risk management** metrics
- **Portfolio performance** tracking
- **Alert system** for performance thresholds

## Troubleshooting

### Common Issues
1. **No history data**: Check `Logs/Signals/` directory exists
2. **Empty summary**: Verify bot name matches file pattern
3. **Slow loading**: Clear cache and refresh
4. **Missing signals**: Check signal_data structure in files

### Debug Steps
1. Check file naming pattern: `{Bot_Name}_{YYYYMMDD_HHMMSS}.json`
2. Verify JSON structure matches expected format
3. Ensure proper UTF-8 encoding
4. Check file permissions and accessibility

## Configuration

### Default Settings
- **Items per page**: 10
- **Cache timeout**: Until manual refresh
- **Date format**: ISO 8601
- **Profit precision**: 2 decimal places

### Customization Options
- Modify `items_per_page` in `create_history_content()`
- Adjust color coding in `create_history_item()`
- Change summary calculations in `calculate_bot_summary()`
- Update file patterns in `get_bot_history()`
