#!/usr/bin/env python3
"""
Test script for webhook chart data endpoint
"""

import requests
import json
import time

def test_webhook_chart_data():
    """Test the webhook chart data endpoint"""
    print("Testing Webhook Chart Data Endpoint...")
    
    # Webhook configuration
    webhook_url = "http://localhost:5000/webhook_chart_data"
    
    # Test data
    test_requests = [
        {
            "symbol": "EURUSD",
            "timeframe": "H1",
            "barback": 50
        },
        {
            "symbol": "GBPUSD", 
            "timeframe": "M15",
            "barback": 100
        },
        {
            "symbol": "USDJPY",
            "timeframe": "D1", 
            "barback": 30
        }
    ]
    
    # Headers for authentication (replace with actual tokens)
    headers = {
        "Authorization": "Bearer your_bearer_token_here",
        "X-Access-Token": "your_access_token_here",
        "Content-Type": "application/json"
    }
    
    print(f"\nTesting endpoint: {webhook_url}")
    print("Note: Make sure the webhook server is running first!")
    
    for i, test_data in enumerate(test_requests, 1):
        print(f"\n=== Test {i}: {test_data['symbol']} {test_data['timeframe']} ===")
        
        try:
            # Send POST request
            response = requests.post(
                webhook_url,
                headers=headers,
                json=test_data,
                timeout=30
            )
            
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"Success: {data.get('message', 'No message')}")
                print(f"Symbol: {data.get('symbol', 'N/A')}")
                print(f"Timeframe: {data.get('timeframe', 'N/A')}")
                print(f"Bars Count: {data.get('bars_count', 'N/A')}")
                
                # Show sample of chart data
                chart_data = data.get('data', [])
                if chart_data:
                    print("\nSample data (first bar):")
                    print(json.dumps(chart_data[0], indent=2))
                    
                    if len(chart_data) > 1:
                        print("\nSample data (last bar):")
                        print(json.dumps(chart_data[-1], indent=2))
                else:
                    print("No chart data received")
            else:
                print(f"Error: {response.text}")
                
        except requests.exceptions.ConnectionError:
            print("❌ Connection failed - Is the webhook server running?")
        except requests.exceptions.Timeout:
            print("❌ Request timeout")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        # Small delay between requests
        if i < len(test_requests):
            time.sleep(1)
    
    print("\n=== Manual Test Instructions ===")
    print("1. Start your trading application")
    print("2. Enable the webhook server")
    print("3. Update the headers with your actual tokens")
    print("4. Run this test script again")
    print("\n=== Expected Response Format ===")
    expected_format = {
        "error": False,
        "message": "Chart data retrieved successfully",
        "symbol": "EURUSD",
        "timeframe": "H1",
        "bars_count": 50,
        "data": [
            {
                "time": "2025-07-30 13:45:00",
                "open": 1.1054,
                "high": 1.1061,
                "low": 1.1049,
                "close": 1.1058,
                "volume": 259,
                "ema_20": 1.1051,
                "rsi_14": 61.7,
                "macd": 0.0012,
                "macd_signal": 0.0010,
                "rsi_25": 58.3,
                "sma50_rsi25": 55.2,
                "rsi_50": 52.1,
                "sma25_rsi50": 48.9
            }
        ]
    }
    print(json.dumps(expected_format, indent=2))

if __name__ == "__main__":
    test_webhook_chart_data()
